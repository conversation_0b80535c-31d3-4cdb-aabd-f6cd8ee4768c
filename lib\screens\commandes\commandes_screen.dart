import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/commande_provider.dart';
import '../../providers/firebase_client_provider.dart';
import '../../models/commande.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../../widgets/professional_ui_components.dart';
import 'nouvelle_commande_screen.dart';
import 'commande_detail_screen.dart';
import 'facture_screen.dart';

class CommandesScreen extends StatefulWidget {
  const CommandesScreen({super.key});

  @override
  State<CommandesScreen> createState() => _CommandesScreenState();
}

class _CommandesScreenState extends State<CommandesScreen> {

  @override
  void initState() {
    super.initState();
    // Load commandes when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CommandeProvider>().chargerCommandes();
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(left: padding, bottom: 16),
              title: Row(
                children: [
                  const VitaBrosseLogo(height: 28, showText: false),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Commandes',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 22,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'Gestion commandes',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF3B82F6).withValues(alpha: 0.05),
                      const Color(0xFF1D4ED8).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: isSmallScreen ? 55 : 30,
                      right: isSmallScreen ? 10 : 15,
                      child: Container(
                        width: isSmallScreen ? 60 : 80,
                        height: isSmallScreen ? 60 : 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF3B82F6).withValues(alpha: 0.1),
                              const Color(0xFF1D4ED8).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.shopping_cart_outlined,
                            size: isSmallScreen ? 24 : 32,
                            color: const Color(0xFF3B82F6),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Liste des commandes
          SliverFillRemaining(
            child: Consumer2<CommandeProvider, FirebaseClientProvider>(
              builder: (context, commandeProvider, clientProvider, child) {
                if (commandeProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (commandeProvider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red[300],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          commandeProvider.error!,
                          style: Theme.of(context).textTheme.bodyLarge,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            commandeProvider.effacerErreur();
                            commandeProvider.chargerCommandes();
                          },
                          child: const Text('Réessayer'),
                        ),
                      ],
                    ),
                  );
                }

                final commandes = commandeProvider.commandes;
                if (commandes.isEmpty) {
                  return ModernEmptyState(
                    icon: Icons.shopping_cart_outlined,
                    title: 'Aucune commande trouvée',
                    subtitle: 'Prenez votre première commande client',
                  );
                }

                return RefreshIndicator(
                  onRefresh: () => commandeProvider.chargerCommandes(),
                  child: ListView.builder(
                    padding: EdgeInsets.symmetric(horizontal: padding),
                    itemCount: commandes.length,
                    itemBuilder: (context, index) {
                      final commande = commandes[index];
                      return _buildCommandeCard(
                        context,
                        commande,
                        isSmallScreen,
                        clientProvider,
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _naviguerVersNouvelleCommande(context),
        backgroundColor: const Color(0xFF3B82F6),
        foregroundColor: Colors.white,
        elevation: 6,
        heroTag: "add_commande",
        child: const Icon(Icons.add),
      ),
    );
  }



  void _naviguerVersNouvelleCommande(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NouvelleCommandeScreen()),
    );
  }

  void _naviguerVersDetail(BuildContext context, Commande commande) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommandeDetailScreen(commande: commande),
      ),
    );
  }

  void _genererFacture(BuildContext context, Commande commande) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FactureScreen(commande: commande),
      ),
    );
  }



  Widget _buildCommandeCard(
    BuildContext context,
    Commande commande,
    bool isSmallScreen,
    FirebaseClientProvider clientProvider,
  ) {
    final client = clientProvider.obtenirClientParId(commande.clientId);
    final clientName = client?.nomComplet ?? 'Client inconnu';

    return Padding(
      padding: EdgeInsets.only(bottom: isSmallScreen ? 12 : 16),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header avec numéro de commande
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 8 : 10,
                      vertical: isSmallScreen ? 4 : 6,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF3B82F6),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '#${commande.id}',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: isSmallScreen ? 12 : 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: isSmallScreen ? 12 : 16),
              // Infos client et date
              Row(
                children: [
                  Icon(
                    Icons.person_outline,
                    size: isSmallScreen ? 16 : 18,
                    color: const Color(0xFF3B82F6),
                  ),
                  SizedBox(width: isSmallScreen ? 6 : 8),
                  Expanded(
                    flex: 2,
                    child: Text(
                      clientName,
                      style: TextStyle(
                        fontSize: isSmallScreen ? 14 : 16,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1F2937),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: isSmallScreen ? 4 : 8),
                  Flexible(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        DateFormat('dd/MM/yyyy').format(commande.dateCommande),
                        style: TextStyle(
                          fontSize: isSmallScreen ? 11 : 12,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: isSmallScreen ? 12 : 16),

              // Total et nombre d'articles
              Row(
                children: [
                  Icon(
                    Icons.attach_money,
                    size: isSmallScreen ? 16 : 18,
                    color: const Color(0xFF10B981),
                  ),
                  SizedBox(width: isSmallScreen ? 4 : 6),
                  Expanded(
                    child: Text(
                      'Total: ${commande.montantTotal.toStringAsFixed(3)} DT',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 16 : 18,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF10B981),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: isSmallScreen ? 8 : 12),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${commande.items.length} article${commande.items.length > 1 ? 's' : ''}',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 11 : 12,
                        color: const Color(0xFF3B82F6),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: isSmallScreen ? 16 : 20),

              // Action buttons - Updated to match devis design
              Row(
                children: [
                  // Voir détails button
                  Container(
                    margin: const EdgeInsets.only(right: 6),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () => _naviguerVersDetail(context, commande),
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                          decoration: BoxDecoration(
                            color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: const Color(0xFF3B82F6).withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            Icons.visibility,
                            size: isSmallScreen ? 16 : 18,
                            color: const Color(0xFF3B82F6),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Facture button
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => _genererFacture(context, commande),
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF059669).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: const Color(0xFF059669).withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          Icons.receipt,
                          size: isSmallScreen ? 16 : 18,
                          color: const Color(0xFF059669),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
