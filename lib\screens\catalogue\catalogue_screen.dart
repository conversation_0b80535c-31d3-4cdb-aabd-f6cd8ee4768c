import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/catalogue.dart';
import '../../providers/catalogue_provider.dart';
import '../../widgets/professional_ui_components.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'catalogue_pdf_viewer_simple.dart';
import 'catalogue_form_screen.dart';

class CatalogueScreen extends StatefulWidget {
  const CatalogueScreen({Key? key}) : super(key: key);

  @override
  State<CatalogueScreen> createState() => _CatalogueScreenState();
}

class _CatalogueScreenState extends State<CatalogueScreen> {
  final TextEditingController _searchController = TextEditingController();
  TypeCatalogue? _selectedType;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        final provider = context.read<CatalogueProvider>();
        await provider.chargerCatalogues();
        // Créer des catalogues de test si aucun n'existe
        if (mounted) {
          await provider.creerCataloguesDeTest();
        }
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(left: padding, bottom: 16),
              title: Row(
                children: [
                  const VitaBrosseLogo(height: 28, showText: false),
                  const SizedBox(width: 12),
                  Text(
                    'Catalogues',
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF1F2937),
                      fontSize: isSmallScreen ? 20 : 24,
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF10B981).withValues(alpha: 0.05),
                      const Color(0xFF059669).withValues(alpha: 0.05),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SliverPadding(
            padding: EdgeInsets.fromLTRB(padding, 16, padding, 0),
            sliver: SliverToBoxAdapter(child: _buildSearchAndFilters()),
          ),
          SliverPadding(
            padding: EdgeInsets.fromLTRB(padding, 16, padding, 0),
            sliver: SliverToBoxAdapter(child: _buildCataloguesRecents()),
          ),
          Consumer<CatalogueProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.all(40),
                      child: CircularProgressIndicator(),
                    ),
                  ),
                );
              }

              if (provider.error != null) {
                return SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(40),
                      child: Column(
                        children: [
                          const Icon(
                            Icons.error_outline,
                            size: 48,
                            color: Colors.red,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            provider.error!,
                            textAlign: TextAlign.center,
                            style: const TextStyle(color: Colors.red),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => provider.chargerCatalogues(),
                            child: const Text('Réessayer'),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              final catalogues = provider.cataloguesFiltres;

              if (catalogues.isEmpty) {
                return SliverToBoxAdapter(
                  child: ModernEmptyState(
                    icon: Icons.library_books,
                    title: 'Aucun catalogue',
                    subtitle: 'Commencez par ajouter votre premier catalogue',
                    actionText: 'Ajouter un catalogue',
                    onAction: () => _naviguerVersFormulaire(context),
                  ),
                );
              }

              return SliverPadding(
                padding: EdgeInsets.fromLTRB(padding, 8, padding, 20),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final catalogue = catalogues[index];
                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: _buildCatalogueCard(context, catalogue),
                          ),
                        ),
                      ),
                    );
                  }, childCount: catalogues.length),
                ),
              );
            },
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _naviguerVersFormulaire(context),
        backgroundColor: const Color(0xFF10B981),
        foregroundColor: Colors.white,
        heroTag: "add_catalogue",
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Column(
      children: [
        // Barre de recherche
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Rechercher un catalogue...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon:
                  _searchController.text.isNotEmpty
                      ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          context.read<CatalogueProvider>().chargerCatalogues();
                        },
                      )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onSubmitted: (value) {
              context.read<CatalogueProvider>().rechercherCatalogues(value);
            },
          ),
        ),
        const SizedBox(height: 16),

        // Filtres par type
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              _buildFilterChip('Tous', null),
              const SizedBox(width: 8),
              ...TypeCatalogue.values.map(
                (type) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: _buildFilterChip(type.name, type),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFilterChip(String label, TypeCatalogue? type) {
    final isSelected = _selectedType == type;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedType = selected ? type : null;
        });
        context.read<CatalogueProvider>().filtrerParType(_selectedType);
      },
      backgroundColor: Colors.white,
      selectedColor: const Color(0xFF10B981).withValues(alpha: 0.2),
      checkmarkColor: const Color(0xFF10B981),
      labelStyle: TextStyle(
        color: isSelected ? const Color(0xFF10B981) : const Color(0xFF6B7280),
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
      ),
    );
  }

  Widget _buildCataloguesRecents() {
    return Consumer<CatalogueProvider>(
      builder: (context, provider, child) {
        if (provider.cataloguesRecents.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SectionHeader(
              title: 'Catalogues récents',
              subtitle: 'Derniers catalogues ajoutés',
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: provider.cataloguesRecents.length,
                itemBuilder: (context, index) {
                  final catalogue = provider.cataloguesRecents[index];
                  return Container(
                    width: 160,
                    height: 140,
                    margin: const EdgeInsets.only(right: 12),
                    child: _buildCatalogueCardCompact(context, catalogue),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCatalogueCard(BuildContext context, Catalogue catalogue) {
    return ProfessionalCard(
      child: InkWell(
        onTap: () => _naviguerVersPDFViewer(context, catalogue),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Icône du catalogue
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: _getColorForType(
                    catalogue.type,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    catalogue.typeIcon,
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // Informations du catalogue
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      catalogue.nom,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getColorForType(
                          catalogue.type,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        catalogue.typeLabel,
                        style: TextStyle(
                          fontSize: 12,
                          color: _getColorForType(catalogue.type),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      catalogue.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF6B7280),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Actions
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert),
                onSelected:
                    (value) => _handleMenuAction(context, value, catalogue),
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'view',
                        child: Row(
                          children: [
                            Icon(Icons.visibility),
                            SizedBox(width: 12),
                            Text('Voir'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 12),
                            Text('Modifier'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 12),
                            Text(
                              'Supprimer',
                              style: TextStyle(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCatalogueCardCompact(BuildContext context, Catalogue catalogue) {
    return ProfessionalCard(
      child: InkWell(
        onTap: () => _naviguerVersPDFViewer(context, catalogue),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icône du catalogue
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getColorForType(
                    catalogue.type,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Center(
                  child: Text(
                    catalogue.typeIcon,
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
              ),
              const SizedBox(height: 8),

              // Nom du catalogue
              Text(
                catalogue.nom,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1F2937),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),

              // Type
              Text(
                catalogue.typeLabel,
                style: TextStyle(
                  fontSize: 11,
                  color: _getColorForType(catalogue.type),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),

              // Date
              Text(
                catalogue.dateCreation.toString().substring(0, 10),
                style: const TextStyle(fontSize: 10, color: Color(0xFF9CA3AF)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getColorForType(TypeCatalogue type) {
    switch (type) {
      case TypeCatalogue.produits:
        return const Color(0xFF10B981);
      case TypeCatalogue.promotions:
        return const Color(0xFFEF4444);
      case TypeCatalogue.nouveautes:
        return const Color(0xFFF59E0B);
      case TypeCatalogue.documentation:
        return const Color(0xFF3B82F6);
      case TypeCatalogue.formation:
        return const Color(0xFF8B5CF6);
    }
  }

  void _handleMenuAction(
    BuildContext context,
    String action,
    Catalogue catalogue,
  ) {
    switch (action) {
      case 'view':
        _naviguerVersPDFViewer(context, catalogue);
        break;
      case 'edit':
        _naviguerVersFormulaire(context, catalogue: catalogue);
        break;
      case 'delete':
        _confirmerSuppression(context, catalogue);
        break;
    }
  }

  void _naviguerVersPDFViewer(BuildContext context, Catalogue catalogue) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CataloguePDFViewer(catalogue: catalogue),
      ),
    );
  }

  void _naviguerVersFormulaire(BuildContext context, {Catalogue? catalogue}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CatalogueFormScreen(catalogue: catalogue),
      ),
    );
  }

  void _confirmerSuppression(BuildContext context, Catalogue catalogue) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer le catalogue "${catalogue.nom}" ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  if (!mounted) return;

                  // Store references before async operation
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  final catalogueProvider = context.read<CatalogueProvider>();

                  final success = await catalogueProvider.supprimerCatalogue(
                    catalogue.id,
                  );

                  if (mounted && success) {
                    scaffoldMessenger.showSnackBar(
                      const SnackBar(
                        content: Text('Catalogue supprimé avec succès'),
                      ),
                    );
                  }
                },
                child: const Text(
                  'Supprimer',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }
}
