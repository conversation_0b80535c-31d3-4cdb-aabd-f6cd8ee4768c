import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import '../services/logging_service.dart';

/// Exception personnalisée pour les erreurs API
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;

  ApiException(this.message, {this.statusCode, this.data});

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}

/// Service HTTP centralisé avec retry logic et gestion d'erreurs
class HttpService {
  static const Duration _timeout = Duration(seconds: 30);
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 2);

  // Headers par défaut
  static Map<String, String> get _defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': '${AppConfig.appName}/${AppConfig.appVersion}',
  };

  /// Effectue une requête GET avec retry logic
  static Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, String>? queryParams,
  }) async {
    final uri = _buildUri(endpoint, queryParams);
    return _executeWithRetry(() => _performGet(uri, headers));
  }

  /// Effectue une requête POST
  static Future<Map<String, dynamic>> post(
    String endpoint,
    Map<String, dynamic> data, {
    Map<String, String>? headers,
  }) async {
    final uri = _buildUri(endpoint);
    return _executeWithRetry(() => _performPost(uri, data, headers));
  }

  /// Effectue une requête PUT
  static Future<Map<String, dynamic>> put(
    String endpoint,
    Map<String, dynamic> data, {
    Map<String, String>? headers,
  }) async {
    final uri = _buildUri(endpoint);
    return _executeWithRetry(() => _performPut(uri, data, headers));
  }

  /// Effectue une requête DELETE
  static Future<Map<String, dynamic>> delete(
    String endpoint, {
    Map<String, String>? headers,
  }) async {
    final uri = _buildUri(endpoint);
    return _executeWithRetry(() => _performDelete(uri, headers));
  }

  /// Upload de fichier avec progress callback
  static Future<Map<String, dynamic>> uploadFile(
    String endpoint,
    File file, {
    String fieldName = 'file',
    Map<String, String>? additionalFields,
    Function(double)? onProgress,
  }) async {
    try {
      final uri = _buildUri(endpoint);
      final request =
          http.MultipartRequest('POST', uri)
            ..headers.addAll(_defaultHeaders)
            ..files.add(
              await http.MultipartFile.fromPath(fieldName, file.path),
            );

      if (additionalFields != null) {
        request.fields.addAll(additionalFields);
      }

      LoggingService.info('Uploading file to $endpoint');
      final streamedResponse = await request.send().timeout(_timeout);
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse(response);
    } catch (e) {
      LoggingService.error('File upload failed', error: e);
      rethrow;
    }
  }

  // Méthodes privées

  static Uri _buildUri(String endpoint, [Map<String, String>? queryParams]) {
    var uri = Uri.parse('${AppConfig.baseUrl}$endpoint');
    if (queryParams != null && queryParams.isNotEmpty) {
      uri = uri.replace(queryParameters: queryParams);
    }
    return uri;
  }

  static Future<Map<String, dynamic>> _executeWithRetry(
    Future<Map<String, dynamic>> Function() operation,
  ) async {
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        return await operation();
      } catch (e) {
        if (attempt == _maxRetries) {
          LoggingService.error(
            'HTTP request failed after $_maxRetries attempts',
            error: e,
          );
          rethrow;
        }

        if (_shouldRetry(e)) {
          LoggingService.warning(
            'HTTP request failed, retrying... (attempt $attempt/$_maxRetries)',
          );
          await Future.delayed(_retryDelay * attempt);
        } else {
          rethrow;
        }
      }
    }
    throw Exception('Unexpected retry loop exit');
  }

  static bool _shouldRetry(dynamic error) {
    if (error is SocketException) return true;
    if (error is HttpException) return true;
    if (error is ApiException) {
      // Retry sur les erreurs serveur 5xx, mais pas sur les erreurs client 4xx
      return error.statusCode != null && error.statusCode! >= 500;
    }
    return false;
  }

  static Future<Map<String, dynamic>> _performGet(
    Uri uri,
    Map<String, String>? headers,
  ) async {
    LoggingService.debug('GET $uri');
    final response = await http
        .get(uri, headers: {..._defaultHeaders, ...?headers})
        .timeout(_timeout);

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> _performPost(
    Uri uri,
    Map<String, dynamic> data,
    Map<String, String>? headers,
  ) async {
    LoggingService.debug('POST $uri');
    final response = await http
        .post(
          uri,
          headers: {..._defaultHeaders, ...?headers},
          body: json.encode(data),
        )
        .timeout(_timeout);

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> _performPut(
    Uri uri,
    Map<String, dynamic> data,
    Map<String, String>? headers,
  ) async {
    LoggingService.debug('PUT $uri');
    final response = await http
        .put(
          uri,
          headers: {..._defaultHeaders, ...?headers},
          body: json.encode(data),
        )
        .timeout(_timeout);

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> _performDelete(
    Uri uri,
    Map<String, String>? headers,
  ) async {
    LoggingService.debug('DELETE $uri');
    final response = await http
        .delete(uri, headers: {..._defaultHeaders, ...?headers})
        .timeout(_timeout);

    return _handleResponse(response);
  }

  static Map<String, dynamic> _handleResponse(http.Response response) {
    LoggingService.debug('Response ${response.statusCode}: ${response.body}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        if (response.body.isEmpty) {
          return {'success': true};
        }
        return json.decode(response.body) as Map<String, dynamic>;
      } catch (e) {
        LoggingService.error('Failed to parse response JSON', error: e);
        throw ApiException(
          'Invalid JSON response',
          statusCode: response.statusCode,
        );
      }
    } else {
      String errorMessage = 'HTTP ${response.statusCode}';
      dynamic errorData;

      try {
        errorData = json.decode(response.body);
        errorMessage = errorData['message'] ?? errorMessage;
      } catch (e) {
        errorMessage = response.body.isNotEmpty ? response.body : errorMessage;
      }

      throw ApiException(
        errorMessage,
        statusCode: response.statusCode,
        data: errorData,
      );
    }
  }

  /// Vérifie la connectivité réseau
  static Future<bool> isConnected() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// Health check de l'API
  static Future<bool> healthCheck() async {
    try {
      final response = await get('/health').timeout(Duration(seconds: 10));
      return response['status'] == 'ok';
    } catch (e) {
      LoggingService.warning('API health check failed', error: e);
      return false;
    }
  }
}
