class Rapport {
  final String id;
  final String missionId;
  final String merchandiserId;
  final String commercialId;
  final DateTime dateCreation;
  final DateTime dateRapport; // Date du jour du rapport
  final String statut; // 'brouillon', 'envoye', 'valide', 'rejete'
  final String? commentaires; // Commentaires du merchandiser
  final String? feedbackCommercial; // Feedback du commercial
  final List<String> photosUrls; // URLs des photos prises
  final Map<String, dynamic>
  donnees; // Données du rapport (produits, stocks, etc.)
  final double? dureeVisite; // Durée de la visite en heures
  final String? problemesDeclares; // Problèmes rencontrés
  final List<String> tachesRealisees; // Tâches accomplies
  final List<String> tachesNonRealisees; // Tâches non accomplies avec raisons

  Rapport({
    required this.id,
    required this.missionId,
    required this.merchandiserId,
    required this.commercialId,
    required this.dateCreation,
    required this.dateRapport,
    this.statut = 'brouillon',
    this.commentaires,
    this.feedbackCommercial,
    this.photosUrls = const [],
    this.donnees = const {},
    this.dureeVisite,
    this.problemesDeclares,
    this.tachesRealisees = const [],
    this.tachesNonRealisees = const [],
  });

  // Convertir un Rapport en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'missionId': missionId,
      'merchandiserId': merchandiserId,
      'commercialId': commercialId,
      'dateCreation': dateCreation.toIso8601String(),
      'dateRapport': dateRapport.toIso8601String(),
      'statut': statut,
      'commentaires': commentaires,
      'feedbackCommercial': feedbackCommercial,
      'photosUrls': photosUrls.join('|'),
      'donnees': donnees.toString(),
      'dureeVisite': dureeVisite,
      'problemesDeclares': problemesDeclares,
      'tachesRealisees': tachesRealisees.join('|'),
      'tachesNonRealisees': tachesNonRealisees.join('|'),
    };
  }

  // Créer un Rapport à partir d'une Map de la base de données
  factory Rapport.fromMap(Map<String, dynamic> map) {
    return Rapport(
      id: map['id'],
      missionId: map['missionId'],
      merchandiserId: map['merchandiserId'],
      commercialId: map['commercialId'],
      dateCreation: DateTime.parse(map['dateCreation']),
      dateRapport: DateTime.parse(map['dateRapport']),
      statut: map['statut'],
      commentaires: map['commentaires'],
      feedbackCommercial: map['feedbackCommercial'],
      photosUrls: map['photosUrls']?.split('|') ?? [],
      donnees:
          map['donnees'] != null
              ? Map<String, dynamic>.from(map['donnees'])
              : {},
      dureeVisite: map['dureeVisite']?.toDouble(),
      problemesDeclares: map['problemesDeclares'],
      tachesRealisees: map['tachesRealisees']?.split('|') ?? [],
      tachesNonRealisees: map['tachesNonRealisees']?.split('|') ?? [],
    );
  }

  // Créer une copie du rapport avec des modifications
  Rapport copyWith({
    String? missionId,
    String? merchandiserId,
    String? commercialId,
    DateTime? dateCreation,
    DateTime? dateRapport,
    String? statut,
    String? commentaires,
    String? feedbackCommercial,
    List<String>? photosUrls,
    Map<String, dynamic>? donnees,
    double? dureeVisite,
    String? problemesDeclares,
    List<String>? tachesRealisees,
    List<String>? tachesNonRealisees,
  }) {
    return Rapport(
      id: id,
      missionId: missionId ?? this.missionId,
      merchandiserId: merchandiserId ?? this.merchandiserId,
      commercialId: commercialId ?? this.commercialId,
      dateCreation: dateCreation ?? this.dateCreation,
      dateRapport: dateRapport ?? this.dateRapport,
      statut: statut ?? this.statut,
      commentaires: commentaires ?? this.commentaires,
      feedbackCommercial: feedbackCommercial ?? this.feedbackCommercial,
      photosUrls: photosUrls ?? this.photosUrls,
      donnees: donnees ?? this.donnees,
      dureeVisite: dureeVisite ?? this.dureeVisite,
      problemesDeclares: problemesDeclares ?? this.problemesDeclares,
      tachesRealisees: tachesRealisees ?? this.tachesRealisees,
      tachesNonRealisees: tachesNonRealisees ?? this.tachesNonRealisees,
    );
  }

  // Méthodes utilitaires
  bool get estBrouillon => statut == 'brouillon';
  bool get estEnvoye => statut == 'envoye';
  bool get estValide => statut == 'valide';
  bool get estRejete => statut == 'rejete';

  String get statutAffichage {
    switch (statut) {
      case 'brouillon':
        return 'Brouillon';
      case 'envoye':
        return 'Envoyé';
      case 'valide':
        return 'Validé';
      case 'rejete':
        return 'Rejeté';
      default:
        return statut;
    }
  }

  double get pourcentageTachesRealisees {
    final totalTaches = tachesRealisees.length + tachesNonRealisees.length;
    if (totalTaches == 0) return 0;
    return (tachesRealisees.length / totalTaches) * 100;
  }

  bool get estComplet {
    return commentaires?.isNotEmpty == true && tachesRealisees.isNotEmpty;
  }
}
