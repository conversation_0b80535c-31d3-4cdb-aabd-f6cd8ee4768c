import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/mission_provider.dart';
import '../../providers/merchandiser_provider.dart';
import '../../providers/firebase_client_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/mission.dart';
import '../../models/client.dart';

class CreerMissionCalendrierScreen extends StatefulWidget {
  final DateTime selectedDate;
  final Mission? missionToEdit;

  const CreerMissionCalendrierScreen({
    Key? key,
    required this.selectedDate,
    this.missionToEdit,
  }) : super(key: key);

  @override
  _CreerMissionCalendrierScreenState createState() =>
      _CreerMissionCalendrierScreenState();
}

class _CreerMissionCalendrierScreenState
    extends State<CreerMissionCalendrierScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titreController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();
  final _tacheController = TextEditingController();
  final _clientSearchController = TextEditingController();

  String? _merchandiserSelectionne;
  Client? _clientSelectionne;
  late DateTime _dateEcheance;
  String _priorite = 'normale';
  List<String> _taches = [];
  List<Client> _clientsFiltres = [];
  bool _isLoading = false;
  bool _showClientDropdown = false;

  @override
  void initState() {
    super.initState();
    final DateTime now = DateTime.now();
    _dateEcheance =
        widget.selectedDate.isBefore(now) ? now : widget.selectedDate;

    // If editing a mission, populate the form
    if (widget.missionToEdit != null) {
      _populateFormForEdit();
    }

    _chargerDonnees();
  }

  void _populateFormForEdit() {
    final mission = widget.missionToEdit!;
    _titreController.text = mission.titre;
    _descriptionController.text = mission.description;
    _notesController.text = mission.notes ?? '';
    _merchandiserSelectionne = mission.merchandiserId;
    _dateEcheance = mission.dateEcheance;
    _priorite = mission.priorite;
    _taches = List<String>.from(mission.taches);

    // Note: Client will be set when the client provider loads
  }

  void _chargerDonnees() async {
    final merchandiserProvider = Provider.of<MerchandiserProvider>(
      context,
      listen: false,
    );
    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );

    await merchandiserProvider.chargerMerchandisers();
    await clientProvider.loadClients();

    // If editing, set the selected client after clients are loaded
    if (widget.missionToEdit != null && _clientSelectionne == null) {
      final mission = widget.missionToEdit!;
      final client = clientProvider.clients.firstWhere(
        (c) => c.id == mission.clientId,
        orElse: () => clientProvider.clients.first,
      );
      setState(() {
        _clientSelectionne = client;
      });
    }
  }

  @override
  void dispose() {
    _titreController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    _tacheController.dispose();
    _clientSearchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          widget.missionToEdit != null
              ? 'Modifier la mission'
              : 'Nouvelle mission',
          style: const TextStyle(
            fontWeight: FontWeight.w700,
            color: Color(0xFF1F2937),
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF1E293B),
        elevation: 0,
        scrolledUnderElevation: 2,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Color(0xFF1E293B)),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date sélectionnée
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color(0xFF10B981).withValues(alpha: 0.2),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF10B981).withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF10B981).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.calendar_today,
                        color: Color(0xFF10B981),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Date d\'échéance',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF6B7280),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${_dateEcheance.day}/${_dateEcheance.month}/${_dateEcheance.year}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                              color: Color(0xFF1F2937),
                            ),
                          ),
                        ],
                      ),
                    ),
                    TextButton.icon(
                      onPressed: _selectionnerDate,
                      icon: const Icon(Icons.edit, size: 18),
                      label: const Text('Modifier'),
                      style: TextButton.styleFrom(
                        foregroundColor: const Color(0xFF10B981),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Titre
              _buildTextField(
                controller: _titreController,
                label: 'Titre de la mission',
                icon: Icons.title,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Veuillez saisir un titre';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Description
              _buildTextField(
                controller: _descriptionController,
                label: 'Description',
                icon: Icons.description,
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Veuillez saisir une description';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Sélection du merchandiser
              Consumer<MerchandiserProvider>(
                builder: (context, provider, child) {
                  // Debug information (can be removed in production)
                  // print('DEBUG: Merchandisers count: ${provider.merchandisers.length}');
                  // print('DEBUG: Provider loading: ${provider.isLoading}');
                  // print('DEBUG: Provider error: ${provider.error}');

                  if (provider.isLoading) {
                    return Container(
                      padding: const EdgeInsets.all(16),
                      child: const Row(
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 12),
                          Text('Chargement des merchandisers...'),
                        ],
                      ),
                    );
                  }

                  if (provider.error != null) {
                    return Container(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        'Erreur: ${provider.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    );
                  }

                  if (provider.merchandisers.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(16),
                      child: const Text(
                        'Aucun merchandiser trouvé. Vérifiez la collection Firebase.',
                        style: TextStyle(color: Colors.orange),
                      ),
                    );
                  }

                  return _buildDropdown<String>(
                    value: _merchandiserSelectionne,
                    label: 'Merchandiser',
                    icon: Icons.person,
                    items:
                        provider.merchandisers.map((merchandiser) {
                          return DropdownMenuItem(
                            value: merchandiser.id?.toString(),
                            child: Text(merchandiser.nomComplet),
                          );
                        }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _merchandiserSelectionne = value;
                      });
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'Veuillez sélectionner un merchandiser';
                      }
                      return null;
                    },
                  );
                },
              ),

              const SizedBox(height: 16),

              // Sélection du client avec recherche
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFormField(
                    controller: _clientSearchController,
                    decoration: InputDecoration(
                      labelText: 'Rechercher un client',
                      prefixIcon: Icon(
                        Icons.person_search,
                        color: Colors.grey[600],
                      ),
                      suffixIcon:
                          _clientSelectionne != null
                              ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  setState(() {
                                    _clientSelectionne = null;
                                    _clientSearchController.clear();
                                    _clientsFiltres.clear();
                                    _showClientDropdown = false;
                                  });
                                },
                              )
                              : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    onChanged: _rechercherClients,
                    validator: (value) {
                      if (_clientSelectionne == null) {
                        return 'Veuillez sélectionner un client';
                      }
                      return null;
                    },
                  ),
                  if (_showClientDropdown && _clientsFiltres.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: _clientsFiltres.length,
                        itemBuilder: (context, index) {
                          final client = _clientsFiltres[index];
                          return ListTile(
                            title: Text(client.nomComplet),
                            subtitle: Text(client.email),
                            onTap: () {
                              setState(() {
                                _clientSelectionne = client;
                                _clientSearchController.text =
                                    client.nomComplet;
                                _showClientDropdown = false;
                                _clientsFiltres.clear();
                              });
                            },
                          );
                        },
                      ),
                    ),
                  if (_clientSelectionne != null)
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.green[700],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Client sélectionné:',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.green[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  _clientSelectionne!.nomClient ??
                                      '${_clientSelectionne!.nom ?? ''} ${_clientSelectionne!.prenom ?? ''}'
                                          .trim(),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.green[700],
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                if (_clientSelectionne!.email.isNotEmpty)
                                  Text(
                                    _clientSelectionne!.email,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.green[600],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 16),

              // Priorité
              _buildDropdown<String>(
                value: _priorite,
                label: 'Priorité',
                icon: Icons.priority_high,
                items: const [
                  DropdownMenuItem(value: 'faible', child: Text('Faible')),
                  DropdownMenuItem(value: 'normale', child: Text('Normale')),
                  DropdownMenuItem(value: 'haute', child: Text('Haute')),
                  DropdownMenuItem(value: 'urgente', child: Text('Urgente')),
                ],
                onChanged: (value) {
                  setState(() {
                    _priorite = value!;
                  });
                },
              ),

              const SizedBox(height: 24),

              // Section Tâches
              Row(
                children: [
                  Icon(Icons.task_alt, color: Colors.grey[700]),
                  const SizedBox(width: 8),
                  Text(
                    'Tâches à accomplir',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Ajouter une tâche
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _tacheController,
                      decoration: InputDecoration(
                        hintText: 'Ajouter une tâche...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      onSubmitted: (_) => _ajouterTache(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _ajouterTache,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Icon(Icons.add),
                  ),
                ],
              ),

              if (_taches.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children:
                        _taches.asMap().entries.map((entry) {
                          final index = entry.key;
                          final tache = entry.value;
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.check_circle_outline,
                                  size: 20,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 8),
                                Expanded(child: Text(tache)),
                                IconButton(
                                  onPressed: () {
                                    setState(() {
                                      _taches.removeAt(index);
                                    });
                                  },
                                  icon: Icon(
                                    Icons.close,
                                    size: 20,
                                    color: Colors.red[400],
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Notes (optionnel)
              _buildTextField(
                controller: _notesController,
                label: 'Notes (optionnel)',
                icon: Icons.note,
                maxLines: 2,
              ),

              const SizedBox(height: 32),

              // Bouton de création
              Container(
                width: double.infinity,
                height: 56,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF10B981), Color(0xFF059669)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF10B981).withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _creerMission,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                  ),
                  child:
                      _isLoading
                          ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2.5,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                widget.missionToEdit != null
                                    ? Icons.edit
                                    : Icons.add_circle,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                widget.missionToEdit != null
                                    ? 'Modifier la mission'
                                    : 'Créer la mission',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ],
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            color: Color(0xFF6B7280),
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF10B981).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: const Color(0xFF10B981), size: 20),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Color(0xFF10B981), width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Color(0xFFEF4444), width: 2),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Color(0xFFEF4444), width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
        style: const TextStyle(
          color: Color(0xFF1F2937),
          fontWeight: FontWeight.w500,
        ),
        validator: validator,
      ),
    );
  }

  Widget _buildDropdown<T>({
    required T? value,
    required String label,
    required IconData icon,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
    String? Function(T?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: DropdownButtonFormField<T>(
        value: value,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            color: Color(0xFF6B7280),
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF10B981).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: const Color(0xFF10B981), size: 20),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Color(0xFF10B981), width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Color(0xFFEF4444), width: 2),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: Color(0xFFEF4444), width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
        style: const TextStyle(
          color: Color(0xFF1F2937),
          fontWeight: FontWeight.w500,
        ),
        items: items,
        onChanged: onChanged,
        validator: validator,
      ),
    );
  }

  void _selectionnerDate() async {
    final DateTime now = DateTime.now();
    final DateTime initialDate =
        _dateEcheance.isBefore(now) ? now : _dateEcheance;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: now,
      lastDate: now.add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _dateEcheance) {
      setState(() {
        _dateEcheance = picked;
      });
    }
  }

  void _ajouterTache() {
    if (_tacheController.text.trim().isNotEmpty) {
      setState(() {
        _taches.add(_tacheController.text.trim());
        _tacheController.clear();
      });
    }
  }

  void _rechercherClients(String query) {
    if (query.isEmpty) {
      setState(() {
        _clientsFiltres.clear();
        _showClientDropdown = false;
      });
      return;
    }

    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );

    final queryLower = query.toLowerCase();
    final clientsFiltres =
        clientProvider.clients
            .where((client) {
              final nomComplet = client.nomComplet.toLowerCase();
              return nomComplet.contains(queryLower) ||
                  client.email.toLowerCase().contains(queryLower) ||
                  (client.telephone?.contains(query) ?? false);
            })
            .take(5)
            .toList(); // Limiter à 5 résultats

    setState(() {
      _clientsFiltres = clientsFiltres;
      _showClientDropdown = clientsFiltres.isNotEmpty;
    });
  }

  void _creerMission() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      final missionProvider = Provider.of<MissionProvider>(
        context,
        listen: false,
      );
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Get the actual commercial user ID
      final commercialId = authProvider.currentUserId ?? 'unknown_commercial';

      final mission = Mission(
        id: widget.missionToEdit?.id ?? missionProvider.genererIdMission(),
        titre: _titreController.text.trim(),
        description: _descriptionController.text.trim(),
        clientId: _clientSelectionne!.id!,
        clientNom:
            _clientSelectionne!.nomClient ??
            '${_clientSelectionne!.nom ?? ''} ${_clientSelectionne!.prenom ?? ''}'
                .trim(),
        merchandiserId: _merchandiserSelectionne!,
        commercialId: commercialId,
        dateCreation: widget.missionToEdit?.dateCreation ?? DateTime.now(),
        dateEcheance: _dateEcheance,
        priorite: _priorite,
        taches: _taches,
        notes:
            _notesController.text.trim().isNotEmpty
                ? _notesController.text.trim()
                : null,
        statut: widget.missionToEdit?.statut ?? 'en_attente',
      );

      try {
        bool success;
        if (widget.missionToEdit != null) {
          // Update existing mission
          success = await missionProvider.mettreAJourMission(mission);
        } else {
          // Create new mission
          success = await missionProvider.creerMission(mission);
        }

        setState(() {
          _isLoading = false;
        });

        if (success) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  widget.missionToEdit != null
                      ? 'Mission modifiée avec succès'
                      : 'Mission créée avec succès',
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
            Navigator.pop(
              context,
              true,
            ); // Retourner true pour indiquer le succès
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  missionProvider.error ?? 'Erreur lors de la création',
                ),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur: $e'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    }
  }
}
