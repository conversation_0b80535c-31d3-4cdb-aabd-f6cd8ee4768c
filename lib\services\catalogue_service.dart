import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import '../models/catalogue.dart';
import 'firebase_service.dart';

class CatalogueService {
  static const String _collection = 'catalogues';

  static CollectionReference get catalogues =>
      FirebaseService.firestore.collection(_collection);

  // Obtenir tous les catalogues
  static Future<List<Catalogue>> obtenirTousLesCatalogues() async {
    try {
      final snapshot =
          await catalogues
              .where('isActive', isEqualTo: true)
              .orderBy('dateCreation', descending: true)
              .get();

      return snapshot.docs.map((doc) => Catalogue.fromFirestore(doc)).toList();
    } catch (e) {
      print('Erreur lors de la récupération des catalogues: $e');
      return [];
    }
  }

  // Obtenir les catalogues par type
  static Future<List<Catalogue>> obtenirCataloguesParType(
    TypeCatalogue type,
  ) async {
    try {
      final snapshot =
          await catalogues
              .where('type', isEqualTo: type.name)
              .where('isActive', isEqualTo: true)
              .orderBy('dateCreation', descending: true)
              .get();

      return snapshot.docs.map((doc) => Catalogue.fromFirestore(doc)).toList();
    } catch (e) {
      print('Erreur lors de la récupération des catalogues par type: $e');
      return [];
    }
  }

  // Créer un nouveau catalogue
  static Future<String> creerCatalogue(Catalogue catalogue) async {
    try {
      final docRef = await catalogues.add(catalogue.toFirestore());
      return docRef.id;
    } catch (e) {
      print('Erreur lors de la création du catalogue: $e');
      throw Exception('Erreur lors de la création du catalogue');
    }
  }

  // Mettre à jour un catalogue
  static Future<void> mettreAJourCatalogue(
    String id,
    Catalogue catalogue,
  ) async {
    try {
      await catalogues.doc(id).update(catalogue.toFirestore());
    } catch (e) {
      print('Erreur lors de la mise à jour du catalogue: $e');
      throw Exception('Erreur lors de la mise à jour du catalogue');
    }
  }

  // Supprimer un catalogue (soft delete)
  static Future<void> supprimerCatalogue(String id) async {
    try {
      await catalogues.doc(id).update({'isActive': false});
    } catch (e) {
      print('Erreur lors de la suppression du catalogue: $e');
      throw Exception('Erreur lors de la suppression du catalogue');
    }
  }

  // Uploader un fichier PDF
  static Future<String> uploaderPDF(File file, String nom) async {
    try {
      final ref = FirebaseStorage.instance
          .ref()
          .child('catalogues')
          .child('${DateTime.now().millisecondsSinceEpoch}_$nom.pdf');

      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask.whenComplete(() => null);
      final downloadURL = await snapshot.ref.getDownloadURL();

      return downloadURL;
    } catch (e) {
      print('Erreur lors de l\'upload du PDF: $e');
      throw Exception('Erreur lors de l\'upload du PDF');
    }
  }

  // Télécharger un PDF localement
  static Future<String?> telechargerPDF(String url, String nom) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final directory = await getApplicationDocumentsDirectory();
        final file = File('${directory.path}/catalogues/${nom}.pdf');

        // Créer le dossier s'il n'existe pas
        await file.parent.create(recursive: true);

        await file.writeAsBytes(response.bodyBytes);
        return file.path;
      }
      return null;
    } catch (e) {
      print('Erreur lors du téléchargement du PDF: $e');
      return null;
    }
  }

  // Vérifier si un PDF est disponible localement
  static Future<bool> isPDFAvailableLocally(String cheminLocal) async {
    try {
      final file = File(cheminLocal);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  // Obtenir le chemin local d'un PDF
  static Future<String> getLocalPDFPath(String nom) async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/catalogues/${nom}.pdf';
  }

  // Rechercher dans les catalogues
  static Future<List<Catalogue>> rechercherCatalogues(String terme) async {
    try {
      final snapshot =
          await catalogues.where('isActive', isEqualTo: true).get();

      final cataloguesList =
          snapshot.docs.map((doc) => Catalogue.fromFirestore(doc)).toList();

      return cataloguesList.where((catalogue) {
        final nomLower = catalogue.nom.toLowerCase();
        final descriptionLower = catalogue.description.toLowerCase();
        final termeLower = terme.toLowerCase();

        return nomLower.contains(termeLower) ||
            descriptionLower.contains(termeLower);
      }).toList();
    } catch (e) {
      print('Erreur lors de la recherche de catalogues: $e');
      return [];
    }
  }

  // Obtenir un catalogue par ID
  static Future<Catalogue?> obtenirCatalogueParId(String id) async {
    try {
      final doc = await catalogues.doc(id).get();
      if (doc.exists) {
        return Catalogue.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Erreur lors de la récupération du catalogue: $e');
      return null;
    }
  }

  // Obtenir les catalogues récents
  static Future<List<Catalogue>> obtenirCataloguesRecents({
    int limit = 5,
  }) async {
    try {
      final snapshot =
          await catalogues
              .where('isActive', isEqualTo: true)
              .orderBy('dateCreation', descending: true)
              .limit(limit)
              .get();

      return snapshot.docs.map((doc) => Catalogue.fromFirestore(doc)).toList();
    } catch (e) {
      print('Erreur lors de la récupération des catalogues récents: $e');
      return [];
    }
  }
}
