import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/firebase_client_provider.dart';
import '../../providers/produit_provider.dart';
import '../../providers/commande_provider.dart';
import '../../models/client.dart';
import '../../models/produit.dart';
import '../../models/commande.dart';
import '../../models/commande_item.dart';
import '../../widgets/professional_ui_components.dart';
import '../../widgets/vitabrosse_logo.dart';

class NouvelleCommandeScreen extends StatefulWidget {
  final Commande? commandeAModifier;

  const NouvelleCommandeScreen({super.key, this.commandeAModifier});

  @override
  State<NouvelleCommandeScreen> createState() => _NouvelleCommandeScreenState();
}

class _NouvelleCommandeScreenState extends State<NouvelleCommandeScreen> {
  // Controllers
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  final TextEditingController _remiseController = TextEditingController();
  final TextEditingController _tvaController =
      TextEditingController(text: '19');
  final TextEditingController _clientSearchController = TextEditingController();
  final TextEditingController _productSearchController =
      TextEditingController();
  final TextEditingController _codeController = TextEditingController();

  // State variables
  List<CommandeItem> _items = [];
  Client? _clientSelectionne;
  DateTime _dateLivraison = DateTime.now().add(const Duration(days: 7));
  String _priorite = 'normale';
  bool _isLoading = false;

  // Client search state
  List<Client> _clientsFiltres = [];
  bool _showClientDropdown = false;
  final FocusNode _clientSearchFocus = FocusNode();

  // Product search state
  bool _isSearchingProduct = false;
  final List<String> _recentProductCodes = [];
  bool _isAddingProduct = false;

  @override
  void initState() {
    super.initState();
    _loadData();

    // Add focus listener to hide dropdown when focus is lost
    _clientSearchFocus.addListener(() {
      if (!_clientSearchFocus.hasFocus) {
        setState(() {
          _showClientDropdown = false;
        });
      }
    });
  }

  Future<void> _loadData() async {
    try {
      final clientProvider = context.read<FirebaseClientProvider>();
      final produitProvider = context.read<ProduitProvider>();

      await Future.wait([
        clientProvider.chargerClients(),
        if (produitProvider.produits.isEmpty) produitProvider.chargerProduits(),
      ]);
    } catch (e) {
      debugPrint('Error loading data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement des données: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _contactController.dispose();
    _remiseController.dispose();
    _tvaController.dispose();
    _clientSearchController.dispose();
    _productSearchController.dispose();
    _codeController.dispose();
    _clientSearchFocus.dispose();
    super.dispose();
  }

  // Calculs financiers
  double get _sousTotal {
    return _items.fold(
        0.0, (total, item) => total + (item.prixUnitaire * item.quantite));
  }

  // Sous-total après remise des articles individuels
  double get _sousTotalApresRemiseArticles {
    return _items.fold(0.0, (total, item) => total + item.sousTotal);
  }

  double get _totalRemiseArticles {
    return _sousTotal - _sousTotalApresRemiseArticles;
  }

  // Remise globale appliquée sur le sous-total après remise articles
  double get _remiseGlobale {
    final remiseValue = double.tryParse(_remiseController.text) ?? 0.0;
    return _sousTotalApresRemiseArticles *
        remiseValue /
        100; // Always percentage
  }

  // Total HT = Sous-total après remise articles - remise globale
  double get _totalHT {
    return _sousTotalApresRemiseArticles - _remiseGlobale;
  }

  double get _montantTVA {
    final tauxTva = double.tryParse(_tvaController.text) ?? 19.0;
    return _totalHT * tauxTva / 100;
  }

  double get _montantTotal {
    return _totalHT + _montantTVA;
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 768;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 1,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: const Color(0xFF1F2937)),
              onPressed: () => Navigator.of(context).pop(),
            ),
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(
                left: isSmallScreen ? 56 : 72,
                bottom: 16,
              ),
              title: Row(
                children: [
                  const VitaBrosseLogo(height: 24, showText: false),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Nouvelle Commande',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 16 : 18,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'Créer une nouvelle commande',
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF3B82F6).withValues(alpha: 0.05),
                      const Color(0xFF1D4ED8).withValues(alpha: 0.05),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (isSmallScreen) ...[
                    _buildClientSection(isSmallScreen),
                    SizedBox(height: 16),
                    _buildCommandeInfo(isSmallScreen),
                    SizedBox(height: 16),
                    _buildArticlesSection(isSmallScreen),
                    SizedBox(height: 16),
                    _buildFinancialSettings(isSmallScreen),
                    SizedBox(height: 16),
                    _buildNotesSection(isSmallScreen),
                  ] else ...[
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 2,
                          child: Column(
                            children: [
                              _buildClientSection(isSmallScreen),
                              SizedBox(height: 24),
                              _buildCommandeInfo(isSmallScreen),
                            ],
                          ),
                        ),
                        SizedBox(width: 24),
                        Expanded(
                          flex: 3,
                          child: Column(
                            children: [
                              _buildArticlesSection(isSmallScreen),
                              SizedBox(height: 24),
                              _buildFinancialSettings(isSmallScreen),
                              SizedBox(height: 24),
                              _buildNotesSection(isSmallScreen),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                  SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: PrimaryActionButton(
                      text: 'Créer la commande',
                      icon: Icons.shopping_cart,
                      onPressed: _creerCommande,
                      isLoading: _isLoading,
                      isFullWidth: true,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: _clientSelectionne != null && _items.isEmpty
          ? FloatingActionButton.extended(
              onPressed: () => _chargerDerniereCommande(),
              icon: const Icon(Icons.history, size: 20),
              label: const Text(
                'Dernière commande',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
              ),
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            )
          : null,
    );
  }

  Widget _buildClientSection(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informations Client',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          SizedBox(height: 16),
          Consumer<FirebaseClientProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Client search field
                  TextField(
                    controller: _clientSearchController,
                    focusNode: _clientSearchFocus,
                    decoration: InputDecoration(
                      labelText: 'Rechercher un client',
                      hintText: 'Tapez le nom du client...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _clientSelectionne != null
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _clientSelectionne = null;
                                  _clientSearchController.clear();
                                  _showClientDropdown = false;
                                });
                              },
                            )
                          : null,
                      border: const OutlineInputBorder(),
                    ),
                    onChanged: _onClientSearchChanged,
                  ),

                  // Client dropdown
                  if (_showClientDropdown && _clientsFiltres.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: _clientsFiltres.length,
                        itemBuilder: (context, index) {
                          final client = _clientsFiltres[index];
                          return ListTile(
                            title: Text(client.nomComplet),
                            subtitle: Text(client.email),
                            onTap: () => _selectionnerClient(client),
                          );
                        },
                      ),
                    ),

                  // Selected client info
                  if (_clientSelectionne != null) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF10B981).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFF10B981).withValues(alpha: 0.3),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _clientSelectionne!.nomComplet,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                          if (_clientSelectionne!.email != null)
                            _buildClientInfoRow('Email',
                                _clientSelectionne!.email!, Icons.email),
                          if (_clientSelectionne!.telephone != null)
                            _buildClientInfoRow('Téléphone',
                                _clientSelectionne!.telephone!, Icons.phone),
                          if (_clientSelectionne!.adresse != null)
                            _buildClientInfoRow(
                                'Adresse',
                                _clientSelectionne!.adresse!,
                                Icons.location_on),
                        ],
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  void _onClientSearchChanged(String query) {
    final clientProvider = context.read<FirebaseClientProvider>();

    if (query.isEmpty) {
      setState(() {
        _showClientDropdown = false;
        _clientsFiltres.clear();
      });
      return;
    }

    final filteredClients = clientProvider.clients
        .where((client) =>
            client.nomComplet.toLowerCase().contains(query.toLowerCase()) ||
            client.email.toLowerCase().contains(query.toLowerCase()))
        .toList();

    setState(() {
      _clientsFiltres = filteredClients;
      _showClientDropdown = filteredClients.isNotEmpty;
    });
  }

  void _selectionnerClient(Client client) {
    setState(() {
      _clientSelectionne = client;
      _clientSearchController.text = client.nomComplet;
      _showClientDropdown = false;
    });
  }

  Widget _buildClientInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: EdgeInsets.only(top: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommandeInfo(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informations Commande',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          SizedBox(height: 16),

          // Date de livraison
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date de livraison souhaitée',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                      ),
                    ),
                    SizedBox(height: 8),
                    InkWell(
                      onTap: () => _selectDeliveryDate(context),
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.calendar_today,
                                size: 20, color: Colors.grey[600]),
                            SizedBox(width: 12),
                            Text(
                              '${_dateLivraison.day}/${_dateLivraison.month}/${_dateLivraison.year}',
                              style: TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 16),

          // Priorité
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Priorité',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
              ),
              SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: _priorite,
                decoration: InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                items: [
                  DropdownMenuItem(value: 'normale', child: Text('Normale')),
                  DropdownMenuItem(value: 'urgente', child: Text('Urgente')),
                  DropdownMenuItem(value: 'faible', child: Text('Faible')),
                ],
                onChanged: (value) {
                  setState(() {
                    _priorite = value ?? 'normale';
                  });
                },
              ),
            ],
          ),

          SizedBox(height: 16),

          // Contact commercial
          TextField(
            controller: _contactController,
            decoration: InputDecoration(
              labelText: 'Contact commercial (optionnel)',
              hintText: 'Nom du responsable commercial',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDeliveryDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dateLivraison,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(Duration(days: 365)),
    );
    if (picked != null && picked != _dateLivraison) {
      setState(() {
        _dateLivraison = picked;
      });
    }
  }

  Widget _buildArticlesSection(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Articles header
          Text(
            'Articles',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 12),
          // Product search field
          TextField(
            controller: _productSearchController,
            decoration: InputDecoration(
              hintText: 'Rechercher un produit par nom ou code...',
              prefixIcon: Icon(
                Icons.search,
                color: const Color(0xFF3B82F6),
              ),
              suffixIcon: _isSearchingProduct
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onSubmitted: _rechercherEtAjouterProduit,
            textInputAction: TextInputAction.search,
          ),
          const SizedBox(height: 12),
          if (_items.isEmpty)
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Aucun article ajouté',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Commencez par ajouter des articles à votre commande',
                    style: TextStyle(color: Colors.grey[500], fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          else
            Column(
              children: _items.asMap().entries.map((entry) {
                return _buildArticleItem(entry.value, entry.key);
              }).toList(),
            ),
          if (_items.isNotEmpty) ...[
            SizedBox(height: 16),
            _buildFinancialSummary(),
          ],
        ],
      ),
    );
  }

  Widget _buildArticleItem(CommandeItem item, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.nomProduit,
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                Text(
                  'Qté: ${item.quantite} • Prix: ${item.prixUnitaireFormate}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                if (item.remise > 0)
                  Text(
                    'Remise: ${item.remiseFormatee}',
                    style: TextStyle(color: Colors.orange[600], fontSize: 12),
                  ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                item.sousTotalFormate,
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              IconButton(
                onPressed: () => _supprimerItem(index),
                icon: Icon(Icons.delete_outline, color: Colors.red[600]),
                iconSize: 20,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _supprimerItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  Widget _buildFinancialSummary() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF3B82F6).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF3B82F6).withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          _buildSummaryRow(
              'ST HT:', '${_sousTotal.toStringAsFixed(3)} DT'),
          if (_totalRemiseArticles > 0)
            _buildSummaryRow('Remise produits:',
                '- ${_totalRemiseArticles.toStringAsFixed(3)} DT'),
          if (_totalRemiseArticles > 0)
            _buildSummaryRow('ST après remise produits:',
                '${_sousTotalApresRemiseArticles.toStringAsFixed(3)} DT'),
          if (_remiseGlobale > 0)
            _buildSummaryRow('Remise globale (${_remiseController.text}%):',
                '- ${_remiseGlobale.toStringAsFixed(3)} DT'),
          _buildSummaryRow('Total HT:', '${_totalHT.toStringAsFixed(3)} DT'),
          _buildSummaryRow('TVA (${_tvaController.text}%):',
              '${_montantTVA.toStringAsFixed(3)} DT'),
          Divider(),
          _buildSummaryRow(
              'Total TTC:', '${_montantTotal.toStringAsFixed(3)} DT',
              isTotal: true),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.w700 : FontWeight.w500,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? const Color(0xFF1F2937) : Colors.grey[700],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.w700 : FontWeight.w600,
              fontSize: isTotal ? 16 : 14,
              color:
                  isTotal ? const Color(0xFF1F2937) : const Color(0xFF1F2937),
            ),
          ),
        ],
      ),
    );
  }

  void _rechercherEtAjouterProduit(String query) async {
    if (query.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez entrer un nom ou code de produit'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSearchingProduct = true;
    });

    try {
      final produitProvider = context.read<ProduitProvider>();

      // Ensure products are loaded
      if (produitProvider.produits.isEmpty) {
        await produitProvider.chargerProduits();
      }

      final queryLower = query.toLowerCase();
      final produitsCorrespondants = produitProvider.produits.where((produit) {
        return produit.nom.toLowerCase().contains(queryLower) ||
            produit.code.toLowerCase().contains(queryLower);
      }).toList();

      setState(() {
        _isSearchingProduct = false;
      });

      if (produitsCorrespondants.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Aucun produit trouvé pour "$query"'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      if (produitsCorrespondants.length == 1) {
        // Si un seul produit trouvé, l'ajouter directement
        final produit = produitsCorrespondants.first;
        _productSearchController.clear();
        _afficherDialogueQuantite(produit);
      } else {
        // Si plusieurs produits trouvés, afficher une liste de sélection
        _afficherListeProduits(produitsCorrespondants, query);
      }
    } catch (e) {
      setState(() {
        _isSearchingProduct = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la recherche: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _afficherListeProduits(List<Produit> produits, String query) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Produits trouvés pour "$query"'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: produits.length,
            itemBuilder: (context, index) {
              final produit = produits[index];
              return ListTile(
                title: Text(produit.nom),
                subtitle: Text(
                    'Code: ${produit.code} • Prix: ${produit.prix.toStringAsFixed(3)} DT'),
                onTap: () {
                  Navigator.pop(context);
                  _productSearchController.clear();
                  _afficherDialogueQuantite(produit);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
        ],
      ),
    );
  }

  void _afficherDialogueQuantite(Produit produit) {
    final quantiteController = TextEditingController(text: '1');
    final prixController = TextEditingController(text: produit.prix.toString());
    final remiseController = TextEditingController(text: '0');

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('Ajouter ${produit.nom}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: quantiteController,
                decoration: const InputDecoration(
                  labelText: 'Quantité',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: prixController,
                decoration: const InputDecoration(
                  labelText: 'Prix unitaire (DT)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: remiseController,
                decoration: const InputDecoration(
                  labelText: 'Remise (%)',
                  suffixText: '%',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                final quantite = int.tryParse(quantiteController.text) ?? 0;
                final prix = double.tryParse(prixController.text) ?? 0.0;
                final remise = double.tryParse(remiseController.text) ?? 0.0;

                if (quantite > 0 && prix > 0) {
                  Navigator.pop(context);
                  _ajouterItem(produit, quantite, prix, remise);
                }
              },
              child: const Text('Ajouter'),
            ),
          ],
        ),
      ),
    );
  }

  void _ajouterItem(Produit produit, int quantite, double prix, double remise) {
    final item = CommandeItem.fromProduit(
      produitId: produit.id!,
      nomProduit: produit.nom,
      codeProduit: produit.code,
      prixUnitaire: prix,
      quantite: quantite,
      unite: 'pièce', // Default unit
      remise: remise,
      remiseEnPourcentage: true, // Always percentage
    );

    setState(() {
      _items.add(item);
    });
  }

  Widget _buildFinancialSettings(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.calculate_outlined,
                  color: const Color(0xFFF59E0B),
                  size: isSmallScreen ? 20 : 24,
                ),
              ),
              SizedBox(width: 12),
              Text(
                'Paramètres Financiers',
                style: TextStyle(
                  fontSize: isSmallScreen ? 16 : 18,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),

          // Remise globale
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Remise globale (%)',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                      ),
                    ),
                    SizedBox(height: 8),
                    TextField(
                      controller: _remiseController,
                      decoration: const InputDecoration(
                        hintText: '0',
                        suffixText: '%',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (_) => setState(() {}),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 16),

          // TVA
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Taux TVA (%)',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                      ),
                    ),
                    SizedBox(height: 8),
                    TextField(
                      controller: _tvaController,
                      decoration: InputDecoration(
                        hintText: '19',
                        suffixText: '%',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (_) => setState(() {}),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notes',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              hintText: 'Ajouter des notes pour cette commande...',
              border: OutlineInputBorder(),
            ),
            maxLines: 4,
          ),
        ],
      ),
    );
  }

  Future<void> _creerCommande() async {
    if (_clientSelectionne == null || _items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content:
              Text('Veuillez sélectionner un client et ajouter des articles'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final commande = Commande(
        clientId: _clientSelectionne!.id!,
        dateCommande: DateTime.now(),
        dateLivraison: _dateLivraison,
        montantTotal: _montantTotal,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        items: _items,
        remisePourcentage: double.tryParse(_remiseController.text) ?? 0.0,
        remiseMontant: 0.0, // Always use percentage, never fixed amount
        tauxTva: double.tryParse(_tvaController.text) ?? 19.0,
        priorite: _priorite,
        contactCommercial:
            _contactController.text.isEmpty ? null : _contactController.text,
      );

      final success =
          await context.read<CommandeProvider>().creerCommande(commande);

      if (success && mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Commande créée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la création de la commande'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildClientSelectionCard(BuildContext context, bool isSmallScreen) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: _clientSelectionne == null
            ? Border.all(color: const Color(0xFF3B82F6), width: 2)
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: _clientSelectionne != null
                        ? const Color(0xFF10B981).withValues(alpha: 0.1)
                        : const Color(0xFF3B82F6).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _clientSelectionne != null
                        ? Icons.check_circle_outline
                        : Icons.person_add_outlined,
                    color: _clientSelectionne != null
                        ? const Color(0xFF10B981)
                        : const Color(0xFF3B82F6),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _clientSelectionne != null
                            ? 'Client sélectionné'
                            : 'Étape 1: Choisir un client',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: _clientSelectionne != null
                              ? const Color(0xFF10B981)
                              : const Color(0xFF3B82F6),
                        ),
                      ),
                      if (_clientSelectionne != null)
                        Text(
                          _clientSelectionne!.nomComplet,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF1F2937),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Consumer<FirebaseClientProvider>(
              builder: (context, provider, child) {
                return DropdownButtonFormField<Client>(
                  value: _clientSelectionne,
                  decoration: InputDecoration(
                    hintText: 'Choisir un client...',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 14,
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      size: 20,
                      color: Colors.grey.shade600,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF6366F1),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                  items: provider.clients.map((client) {
                    return DropdownMenuItem(
                      value: client,
                      child: Text(
                        client.nomComplet,
                        style: const TextStyle(fontSize: 14),
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }).toList(),
                  onChanged: (client) {
                    setState(() {
                      _clientSelectionne = client;
                    });
                  },
                  isExpanded: true,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProduitsCard(BuildContext context, bool isSmallScreen) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: _clientSelectionne != null
                        ? const Color(0xFF3B82F6).withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.add_shopping_cart,
                    color: _clientSelectionne != null
                        ? const Color(0xFF3B82F6)
                        : Colors.grey.shade500,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _clientSelectionne != null
                            ? 'Étape 2: Ajouter des produits'
                            : 'Ajouter des produits',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: _clientSelectionne != null
                              ? const Color(0xFF3B82F6)
                              : Colors.grey.shade500,
                        ),
                      ),
                      if (_items.isNotEmpty)
                        Text(
                          '${_items.length} produit${_items.length > 1 ? 's' : ''} ajouté${_items.length > 1 ? 's' : ''}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Interface simplifiée pour ajouter des produits
            if (_clientSelectionne != null) ...[
              // Champ de saisie du code produit avec raccourcis
              Focus(
                onKeyEvent: (node, event) {
                  // Raccourci Escape pour vider le champ
                  if (event.logicalKey.keyLabel == 'Escape') {
                    _codeController.clear();
                    setState(() {});
                    return KeyEventResult.handled;
                  }
                  return KeyEventResult.ignored;
                },
                child: TextField(
                  controller: _codeController,
                  enabled: _clientSelectionne != null && !_isAddingProduct,
                  autofocus: true, // Auto-focus pour une saisie rapide
                  decoration: InputDecoration(
                    hintText: 'Code produit (Entrée: ajouter, Échap: vider)...',
                    hintStyle:
                        TextStyle(color: Colors.grey.shade500, fontSize: 14),
                    prefixIcon: Icon(
                      Icons.qr_code_scanner,
                      size: 22,
                      color: const Color(0xFF3B82F6),
                    ),
                    suffixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_isAddingProduct) ...[
                          Container(
                            padding: const EdgeInsets.all(12),
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  const Color(0xFF3B82F6),
                                ),
                              ),
                            ),
                          ),
                        ] else if (_codeController.text.isNotEmpty) ...[
                          IconButton(
                            icon: Icon(
                              Icons.clear,
                              size: 20,
                              color: Colors.grey.shade600,
                            ),
                            onPressed: () {
                              _codeController.clear();
                              setState(() {});
                            },
                            tooltip: 'Vider (Échap)',
                          ),
                          IconButton(
                            icon: Icon(
                              Icons.add_circle,
                              size: 24,
                              color: const Color(0xFF3B82F6),
                            ),
                            onPressed: _isAddingProduct
                                ? null
                                : () {
                                    _ajouterProduitParCode(
                                        _codeController.text);
                                    _codeController.clear();
                                  },
                            tooltip: 'Ajouter (Entrée)',
                          ),
                        ],
                      ],
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 18,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF3B82F6),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                  textCapitalization: TextCapitalization.characters,
                  onSubmitted: (code) {
                    if (code.isNotEmpty) {
                      _ajouterProduitParCode(code);
                      _codeController.clear();
                    }
                  },
                  onChanged: (value) => setState(() {}),
                ),
              ),

              const SizedBox(height: 16),

              // Bouton de recherche de produits
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _afficherSelectionProduit(),
                  icon: const Icon(Icons.search, size: 20),
                  label: const Text(
                    'Parcourir le catalogue',
                    style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
                  ),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    side: const BorderSide(color: Color(0xFF3B82F6), width: 2),
                    foregroundColor: const Color(0xFF3B82F6),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),

              // Produits récents (si disponibles)
              if (_recentProductCodes.isNotEmpty) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3B82F6).withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0xFF3B82F6).withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.history,
                            size: 16,
                            color: const Color(0xFF3B82F6),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Produits récents',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF3B82F6),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 6,
                        runSpacing: 6,
                        children: _recentProductCodes.take(5).map((code) {
                          return GestureDetector(
                            onTap: () => _ajouterProduitParCode(code),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(
                                  color: const Color(0xFF3B82F6)
                                      .withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                code,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF3B82F6),
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
              ],
            ] else ...[
              // Message quand aucun client n'est sélectionné
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.person_add_outlined,
                      size: 32,
                      color: Colors.grey.shade500,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Sélectionnez d\'abord un client',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      'pour commencer à ajouter des produits',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 20),

            // Liste des produits ajoutés
            if (_items.isEmpty && _clientSelectionne != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: const Color(0xFF3B82F6).withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                      color: const Color(0xFF3B82F6).withValues(alpha: 0.2)),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.add_shopping_cart,
                      size: 40,
                      color: const Color(0xFF3B82F6),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Prêt à ajouter des produits',
                      style: TextStyle(
                        color: const Color(0xFF1D4ED8),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tapez un code produit ou parcourez le catalogue',
                      style: TextStyle(
                        color: const Color(0xFF2563EB),
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              )
            else if (_items.isNotEmpty)
              _buildListeProduitsMobile(),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard(BuildContext context, bool isSmallScreen) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note_add_outlined,
                  color: Colors.grey.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Notes (optionnel)',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _notesController,
              decoration: InputDecoration(
                hintText: 'Commentaires ou instructions...',
                hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
                contentPadding: const EdgeInsets.all(12),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: Colors.grey.shade500,
                    width: 1.5,
                  ),
                ),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
              maxLines: 2,
              textInputAction: TextInputAction.done,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListeProduitsMobile() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // En-tête de la liste
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          child: Text(
            'Produits ajoutés (${_items.length})',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1F2937),
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Table des produits
        _buildProductTable(),
        const SizedBox(height: 16),
        // Résumé financier
        _buildFinancialSummary(),
      ],
    );
  }

  Widget _buildProductTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          // En-tête du tableau
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    'Article',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Prix Unit.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Qté',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Remise',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Montant',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                SizedBox(width: 40), // Pour les actions
              ],
            ),
          ),
          // Lignes du tableau
          ...(_items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return _buildProductRow(item, index);
          }).toList()),
        ],
      ),
    );
  }

  Widget _buildProductRow(CommandeItem item, int index) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: index < _items.length - 1
                ? Colors.grey.shade200
                : Colors.transparent,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Article
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.nomProduit,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2937),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (item.codeProduit != null && item.codeProduit!.isNotEmpty)
                  Text(
                    item.codeProduit!,
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey.shade600,
                    ),
                  ),
              ],
            ),
          ),
          // Prix unitaire
          Expanded(
            flex: 2,
            child: Text(
              item.prixUnitaireFormate,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          // Quantité
          Expanded(
            flex: 1,
            child: Text(
              '${item.quantite}',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          // Remise
          Expanded(
            flex: 2,
            child: GestureDetector(
              onTap: () => _modifierRemiseItem(item),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                decoration: BoxDecoration(
                  color: item.remise > 0
                      ? const Color(0xFF3B82F6).withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: item.remise > 0
                        ? const Color(0xFF3B82F6).withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  item.remiseFormatee,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: item.remise > 0
                        ? const Color(0xFF1D4ED8)
                        : Colors.grey.shade600,
                  ),
                ),
              ),
            ),
          ),
          // Montant
          Expanded(
            flex: 2,
            child: Text(
              item.sousTotalFormate,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: Color(0xFF1F2937),
              ),
            ),
          ),
          // Actions
          SizedBox(
            width: 40,
            child: PopupMenuButton<String>(
              icon: Icon(
                Icons.more_vert,
                size: 16,
                color: Colors.grey.shade600,
              ),
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _modifierItem(item);
                    break;
                  case 'delete':
                    _retirerItem(item);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 16, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('Modifier'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 16, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Supprimer'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _modifierRemiseItem(CommandeItem item) {
    final remiseController = TextEditingController(
      text: item.remise > 0 ? item.remise.toString() : '',
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Modifier la remise'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              item.nomProduit,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: remiseController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Remise (%)',
                suffixText: '%',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          FilledButton(
            onPressed: () {
              final remiseValue = double.tryParse(remiseController.text) ?? 0.0;
              final index =
                  _items.indexWhere((i) => i.produitId == item.produitId);
              if (index != -1) {
                final newSousTotal = CommandeItem.calculateSousTotal(
                    item.prixUnitaire,
                    item.quantite,
                    remiseValue,
                    true); // Always percentage
                _items[index] = item.copyWith(
                  remise: remiseValue,
                  remiseEnPourcentage: true, // Always percentage
                  sousTotal: newSousTotal,
                );
                setState(() {});
              }
              Navigator.pop(context);
            },
            child: const Text('Appliquer'),
          ),
        ],
      ),
    );
  }

  void _afficherSelectionProduit() {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: screenHeight * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header avec poignée de fermeture
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(
                            0xFF10B981,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.inventory_2_outlined,
                          color: Color(0xFF10B981),
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Sélectionner un produit',
                          style: Theme.of(
                            context,
                          ).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2937),
                              ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey.shade100,
                          foregroundColor: Colors.grey.shade600,
                          padding: const EdgeInsets.all(8),
                          minimumSize: const Size(36, 36),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Liste des produits
            Expanded(
              child: Consumer<ProduitProvider>(
                builder: (context, provider, child) {
                  return ListView.builder(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 16 : 24,
                      vertical: 8,
                    ),
                    itemCount: provider.produits.length,
                    itemBuilder: (context, index) {
                      final produit = provider.produits[index];
                      final isAvailable = produit.estDisponible;

                      return Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isAvailable
                                ? Colors.grey.shade200
                                : Colors.red.shade200,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.04),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: isAvailable
                                      ? const Color(
                                          0xFF10B981,
                                        ).withValues(alpha: 0.1)
                                      : Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  isAvailable
                                      ? Icons.inventory_2_outlined
                                      : Icons.warning_outlined,
                                  color: isAvailable
                                      ? const Color(0xFF10B981)
                                      : Colors.red.shade600,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      produit.nom,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: isAvailable
                                            ? const Color(0xFF1F2937)
                                            : Colors.grey.shade600,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Code: ${produit.code}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        Text(
                                          produit.prixFormate,
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            color: isAvailable
                                                ? const Color(
                                                    0xFF10B981,
                                                  )
                                                : Colors.grey.shade500,
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          'Stock: ${produit.stock}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: isAvailable
                                                ? Colors.grey.shade600
                                                : Colors.red.shade600,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 12),
                              isAvailable
                                  ? FilledButton(
                                      onPressed: () => _ajouterProduit(produit),
                                      style: FilledButton.styleFrom(
                                        backgroundColor: const Color(
                                          0xFF10B981,
                                        ),
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 8,
                                        ),
                                        minimumSize: const Size(0, 36),
                                      ),
                                      child: const Text(
                                        'Ajouter',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    )
                                  : Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.red.shade50,
                                        borderRadius: BorderRadius.circular(
                                          6,
                                        ),
                                        border: Border.all(
                                          color: Colors.red.shade200,
                                        ),
                                      ),
                                      child: Text(
                                        'Rupture',
                                        style: TextStyle(
                                          color: Colors.red.shade600,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _ajouterProduit(Produit produit) {
    Navigator.pop(context);
    _afficherDialogueQuantite(produit);
  }

  void _modifierItem(CommandeItem item) {
    final codeController = TextEditingController(text: item.codeProduit ?? '');
    final nomController = TextEditingController(text: item.nomProduit);
    final prixController = TextEditingController(
      text: item.prixUnitaire.toString(),
    );
    final quantiteController = TextEditingController(
      text: item.quantite.toString(),
    );
    final uniteController = TextEditingController(text: item.unite ?? 'pièce');

    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        contentPadding: EdgeInsets.zero,
        content: Container(
          width: isSmallScreen ? double.maxFinite : 500,
          padding: const EdgeInsets.all(24),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(
                          0xFF3B82F6,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.edit,
                        color: Color(0xFF3B82F6),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Modifier le produit',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2937),
                            ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Champ code/référence
                TextField(
                  controller: codeController,
                  decoration: InputDecoration(
                    labelText: 'Code/Référence',
                    hintText: 'Entrez le code du produit',
                    prefixIcon: const Icon(Icons.qr_code, size: 20),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF3B82F6),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                  textInputAction: TextInputAction.next,
                ),

                const SizedBox(height: 16),

                // Champ nom/désignation
                TextField(
                  controller: nomController,
                  decoration: InputDecoration(
                    labelText: 'Désignation',
                    hintText: 'Entrez la désignation du produit',
                    prefixIcon: const Icon(
                      Icons.inventory_2_outlined,
                      size: 20,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF3B82F6),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                  textInputAction: TextInputAction.next,
                ),

                const SizedBox(height: 16),

                // Champs prix et unité côte à côte
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: TextField(
                        controller: prixController,
                        decoration: InputDecoration(
                          labelText: 'Prix unitaire HT',
                          hintText: '0.000',
                          prefixIcon: const Icon(
                            Icons.attach_money,
                            size: 20,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Colors.grey.shade300,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Colors.grey.shade300,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: const BorderSide(
                              color: Color(0xFF3B82F6),
                              width: 2,
                            ),
                          ),
                          filled: true,
                          fillColor: Colors.grey.shade50,
                        ),
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                        textInputAction: TextInputAction.next,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextField(
                        controller: uniteController,
                        decoration: InputDecoration(
                          labelText: 'Unité',
                          hintText: 'pièce',
                          prefixIcon: const Icon(
                            Icons.straighten,
                            size: 20,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Colors.grey.shade300,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Colors.grey.shade300,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: const BorderSide(
                              color: Color(0xFF3B82F6),
                              width: 2,
                            ),
                          ),
                          filled: true,
                          fillColor: Colors.grey.shade50,
                        ),
                        textInputAction: TextInputAction.next,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Champ quantité
                TextField(
                  controller: quantiteController,
                  decoration: InputDecoration(
                    labelText: 'Quantité',
                    hintText: 'Entrez la quantité souhaitée',
                    prefixIcon: const Icon(Icons.numbers, size: 20),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF3B82F6),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.done,
                ),

                const SizedBox(height: 24),

                // Boutons d'action
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          side: BorderSide(color: Colors.grey.shade300),
                          foregroundColor: Colors.grey.shade700,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Annuler',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: FilledButton(
                        onPressed: () {
                          final code = codeController.text.trim();
                          final nom = nomController.text.trim();
                          final prix =
                              double.tryParse(prixController.text) ?? 0.0;
                          final quantite =
                              int.tryParse(quantiteController.text) ?? 0;
                          final unite = uniteController.text.trim();

                          if (nom.isNotEmpty && prix > 0 && quantite > 0) {
                            Navigator.pop(context);
                            final index = _items.indexWhere(
                              (i) => i.produitId == item.produitId,
                            );
                            if (index != -1) {
                              _items[index] = item.copyWith(
                                codeProduit: code.isEmpty ? null : code,
                                nomProduit: nom,
                                prixUnitaire: prix,
                                quantite: quantite,
                                unite: unite.isEmpty ? null : unite,
                                sousTotal: prix * quantite,
                              );
                              setState(() {});
                            }
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Veuillez remplir tous les champs obligatoires',
                                ),
                                backgroundColor: const Color(0xFFF59E0B),
                              ),
                            );
                          }
                        },
                        style: FilledButton.styleFrom(
                          backgroundColor: const Color(0xFF3B82F6),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Modifier',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _retirerItem(CommandeItem item) {
    setState(() {
      _items.removeWhere((i) => i.produitId == item.produitId);
    });
  }

  Future<void> _ajouterProduitParCode(String code) async {
    if (code.trim().isEmpty) return;

    setState(() {
      _isAddingProduct = true;
    });

    try {
      final produit =
          await context.read<ProduitProvider>().obtenirProduitParCode(
                code.trim(),
              );

      if (produit == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Produit avec le code "$code" introuvable'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return;
      }

      if (!produit.estDisponible) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Produit "${produit.nom}" en rupture de stock'),
              backgroundColor: const Color(0xFFF59E0B),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return;
      }

      // Ajouter le code aux produits récents
      _ajouterAuxProduitsRecents(code.trim().toUpperCase());

      // Feedback positif
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Produit "${produit.nom}" trouvé!'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(milliseconds: 1500),
          ),
        );
      }

      _afficherDialogueQuantite(produit);
    } finally {
      if (mounted) {
        setState(() {
          _isAddingProduct = false;
        });
      }
    }
  }

  void _ajouterAuxProduitsRecents(String code) {
    // Retirer le code s'il existe déjà
    _recentProductCodes.remove(code);
    // Ajouter en première position
    _recentProductCodes.insert(0, code);
    // Garder seulement les 10 derniers
    if (_recentProductCodes.length > 10) {
      _recentProductCodes.removeLast();
    }
    setState(() {});
  }

  Future<void> _chargerDerniereCommande() async {
    if (_clientSelectionne == null) return;

    try {
      // Récupérer toutes les commandes du client et prendre la plus récente
      final commandeProvider = context.read<CommandeProvider>();
      final commandes = commandeProvider.commandes
          .where((c) => c.clientId == _clientSelectionne!.id)
          .toList();

      if (commandes.isNotEmpty) {
        // Trier par date et prendre la plus récente
        commandes.sort((a, b) => b.dateCommande.compareTo(a.dateCommande));
        final derniereCommande = commandes.first;

        if (derniereCommande.items.isNotEmpty && mounted) {
          setState(() {
            _items.clear();
            _items.addAll(derniereCommande.items);
            _notesController.text = derniereCommande.notes ?? '';
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    'Dernière commande chargée (${derniereCommande.items.length} produits)'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('Aucune commande précédente trouvée pour ce client'),
              backgroundColor: Color(0xFF3B82F6),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors du chargement de la dernière commande'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
