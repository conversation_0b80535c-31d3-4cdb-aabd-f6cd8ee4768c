import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/produit_provider.dart';
import '../../models/produit.dart';
import 'produit_form_screen.dart';
import 'produit_detail_screen.dart';

class ProduitsScreen extends StatefulWidget {
  const ProduitsScreen({super.key});

  @override
  State<ProduitsScreen> createState() => _ProduitsScreenState();
}

class _ProduitsScreenState extends State<ProduitsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'Toutes';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(left: padding, bottom: 16),
              title: Text(
                'Produits',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFF1F2937),
                  fontSize: isSmallScreen ? 20 : 24,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF10B981).withValues(alpha: 0.05),
                      const Color(0xFF059669).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: isSmallScreen ? 20 : 30,
                      right: isSmallScreen ? 10 : 15,
                      child: Container(
                        width: isSmallScreen ? 60 : 80,
                        height: isSmallScreen ? 60 : 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF10B981).withValues(alpha: 0.1),
                              const Color(0xFF059669).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.inventory_2_outlined,
                            size: isSmallScreen ? 24 : 32,
                            color: const Color(0xFF10B981),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              Container(
                margin: EdgeInsets.only(right: isSmallScreen ? 12 : 16, top: 8),
                child: FilledButton.icon(
                  onPressed: () => _naviguerVersFormulaire(context),
                  icon: Icon(Icons.add, size: isSmallScreen ? 16 : 18),
                  label: Text(isSmallScreen ? 'Nouveau' : 'Nouveau'),
                  style: FilledButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 12 : 16,
                      vertical: isSmallScreen ? 6 : 8,
                    ),
                    backgroundColor: const Color(0xFF10B981),
                    textStyle: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                  ),
                ),
              ),
            ],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.fromLTRB(padding, 16, padding, 8),
              child: Column(
                children: [
                  // Barre de recherche optimisée
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(
                        isSmallScreen ? 12 : 16,
                      ),
                      border: Border.all(color: Colors.grey.shade200),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Rechercher un produit...',
                        hintStyle: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: isSmallScreen ? 14 : 16,
                        ),
                        prefixIcon: Icon(
                          Icons.search,
                          color: Colors.grey.shade400,
                          size: isSmallScreen ? 20 : 24,
                        ),
                        suffixIcon:
                            _searchController.text.isNotEmpty
                                ? IconButton(
                                  icon: Icon(
                                    Icons.clear,
                                    color: Colors.grey.shade400,
                                    size: isSmallScreen ? 20 : 24,
                                  ),
                                  onPressed: () {
                                    _searchController.clear();
                                    context
                                        .read<ProduitProvider>()
                                        .chargerProduits();
                                  },
                                )
                                : null,
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 16 : 20,
                          vertical: isSmallScreen ? 12 : 16,
                        ),
                      ),
                      onChanged: (value) {
                        if (value.isEmpty) {
                          context.read<ProduitProvider>().chargerProduits();
                        } else {
                          context.read<ProduitProvider>().rechercherProduits(
                            value,
                          );
                        }
                      },
                    ),
                  ),
                  SizedBox(height: isSmallScreen ? 12 : 16),
                  // Filtre par catégorie adaptatif
                  Consumer<ProduitProvider>(
                    builder: (context, provider, child) {
                      final categories = ['Toutes', ...provider.categories];
                      return SizedBox(
                        height: isSmallScreen ? 36 : 40,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: categories.length,
                          itemBuilder: (context, index) {
                            final category = categories[index];
                            final isSelected = category == _selectedCategory;
                            return Padding(
                              padding: EdgeInsets.only(
                                right: isSmallScreen ? 6 : 8,
                              ),
                              child: FilterChip(
                                label: Text(
                                  category,
                                  style: TextStyle(
                                    fontSize: isSmallScreen ? 12 : 14,
                                  ),
                                ),
                                selected: isSelected,
                                onSelected: (selected) {
                                  setState(() {
                                    _selectedCategory = category;
                                  });
                                  provider.filtrerParCategorie(category);
                                },
                                padding: EdgeInsets.symmetric(
                                  horizontal: isSmallScreen ? 8 : 12,
                                  vertical: isSmallScreen ? 2 : 4,
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          // Liste des produits
          Consumer<ProduitProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(strokeWidth: 3),
                        SizedBox(height: 16),
                        Text(
                          'Chargement des produits...',
                          style: TextStyle(color: Colors.grey, fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                );
              }

              if (provider.error != null) {
                return SliverFillRemaining(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.all(padding),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.red.shade50,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              Icons.error_outline,
                              size: 48,
                              color: Colors.red.shade400,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Oups ! Une erreur est survenue',
                            style: Theme.of(
                              context,
                            ).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2937),
                              fontSize: isSmallScreen ? 18 : 20,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            provider.error!,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: isSmallScreen ? 14 : 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          FilledButton.icon(
                            onPressed: () {
                              provider.effacerErreur();
                              provider.chargerProduits();
                            },
                            icon: const Icon(Icons.refresh),
                            label: const Text('Réessayer'),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              if (provider.produits.isEmpty) {
                return SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.inventory_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Aucun produit trouvé',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Ajoutez votre premier produit',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () => _naviguerVersFormulaire(context),
                          icon: const Icon(Icons.add),
                          label: const Text('Ajouter un produit'),
                        ),
                      ],
                    ),
                  ),
                );
              }

              return SliverPadding(
                padding: EdgeInsets.symmetric(horizontal: padding),
                sliver: SliverGrid(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: isSmallScreen ? 2 : 3,
                    crossAxisSpacing: isSmallScreen ? 8 : 12,
                    mainAxisSpacing: isSmallScreen ? 8 : 12,
                    childAspectRatio: isSmallScreen ? 0.75 : 0.8,
                  ),
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final produit = provider.produits[index];
                    return _buildProduitCard(context, produit, isSmallScreen);
                  }, childCount: provider.produits.length),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildProduitCard(
    BuildContext context,
    Produit produit,
    bool isSmallScreen,
  ) {
    return Card(
      clipBehavior: Clip.antiAlias,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
      ),
      child: InkWell(
        onTap: () => _naviguerVersDetail(context, produit),
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image du produit (placeholder)
            Container(
              height: isSmallScreen ? 100 : 120,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                image:
                    produit.imageUrl != null
                        ? DecorationImage(
                          image: NetworkImage(produit.imageUrl!),
                          fit: BoxFit.cover,
                        )
                        : null,
              ),
              child:
                  produit.imageUrl == null
                      ? Icon(
                        Icons.image,
                        size: isSmallScreen ? 36 : 48,
                        color: Colors.grey[400],
                      )
                      : null,
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(isSmallScreen ? 8.0 : 12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      produit.nom,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: isSmallScreen ? 13 : 14,
                        color: const Color(0xFF1F2937),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: isSmallScreen ? 2 : 4),
                    Text(
                      produit.categorie,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: isSmallScreen ? 11 : 12,
                      ),
                    ),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            produit.prixFormate,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: isSmallScreen ? 14 : 16,
                              color: const Color(0xFFF59E0B),
                            ),
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: isSmallScreen ? 6 : 8,
                            vertical: isSmallScreen ? 3 : 4,
                          ),
                          decoration: BoxDecoration(
                            color:
                                produit.stock > 10
                                    ? Colors.green[100]
                                    : produit.stock > 0
                                    ? Colors.orange[100]
                                    : Colors.red[100],
                            borderRadius: BorderRadius.circular(
                              isSmallScreen ? 8 : 12,
                            ),
                          ),
                          child: Text(
                            'Stock: ${produit.stock}',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 9 : 10,
                              fontWeight: FontWeight.w500,
                              color:
                                  produit.stock > 10
                                      ? Colors.green[800]
                                      : produit.stock > 0
                                      ? Colors.orange[800]
                                      : Colors.red[800],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            // Menu d'actions
            ButtonBar(
              buttonPadding: EdgeInsets.zero,
              children: [
                IconButton(
                  icon: Icon(Icons.edit, size: isSmallScreen ? 18 : 20),
                  onPressed:
                      () => _naviguerVersFormulaire(context, produit: produit),
                ),
                IconButton(
                  icon: Icon(
                    Icons.delete,
                    size: isSmallScreen ? 18 : 20,
                    color: Colors.red,
                  ),
                  onPressed: () => _confirmerSuppression(context, produit),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _naviguerVersFormulaire(BuildContext context, {Produit? produit}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProduitFormScreen(produit: produit),
      ),
    );
  }

  void _naviguerVersDetail(BuildContext context, Produit produit) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProduitDetailScreen(produit: produit),
      ),
    );
  }

  void _confirmerSuppression(BuildContext context, Produit produit) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer le produit "${produit.nom}" ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  final success = await context
                      .read<ProduitProvider>()
                      .supprimerProduit(produit.id!);
                  if (success && mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Produit supprimé avec succès'),
                      ),
                    );
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }
}
