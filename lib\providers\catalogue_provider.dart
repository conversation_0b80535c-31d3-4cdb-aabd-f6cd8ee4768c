import 'package:flutter/material.dart';
import '../models/catalogue.dart';
import '../services/catalogue_service.dart';

class CatalogueProvider with ChangeNotifier {
  List<Catalogue> _catalogues = [];
  List<Catalogue> _cataloguesRecents = [];
  bool _isLoading = false;
  String? _error;
  TypeCatalogue? _filtreType;

  List<Catalogue> get catalogues => _catalogues;
  List<Catalogue> get cataloguesRecents => _cataloguesRecents;
  bool get isLoading => _isLoading;
  String? get error => _error;
  TypeCatalogue? get filtreType => _filtreType;

  // Obtenir les catalogues filtrés
  List<Catalogue> get cataloguesFiltres {
    if (_filtreType == null) {
      return _catalogues;
    }
    return _catalogues.where((cat) => cat.type == _filtreType).toList();
  }

  // Charger tous les catalogues
  Future<void> chargerCatalogues() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _catalogues = await CatalogueService.obtenirTousLesCatalogues();
      _error = null;
      // Charger aussi les catalogues récents
      await chargerCataloguesRecents();
    } catch (e) {
      _error = 'Erreur lors du chargement des catalogues: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger les catalogues récents
  Future<void> chargerCataloguesRecents() async {
    try {
      _cataloguesRecents = await CatalogueService.obtenirCataloguesRecents();
      notifyListeners();
    } catch (e) {
      print('Erreur lors du chargement des catalogues récents: $e');
    }
  }

  // Filtrer par type
  void filtrerParType(TypeCatalogue? type) {
    _filtreType = type;
    notifyListeners();
  }

  // Rechercher dans les catalogues
  Future<void> rechercherCatalogues(String terme) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      if (terme.isEmpty) {
        await chargerCatalogues();
      } else {
        _catalogues = await CatalogueService.rechercherCatalogues(terme);
      }
      _error = null;
    } catch (e) {
      _error = 'Erreur lors de la recherche: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Créer un nouveau catalogue
  Future<bool> creerCatalogue(Catalogue catalogue) async {
    try {
      await CatalogueService.creerCatalogue(catalogue);
      await chargerCatalogues();
      await chargerCataloguesRecents();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la création du catalogue: $e';
      notifyListeners();
      return false;
    }
  }

  // Mettre à jour un catalogue
  Future<bool> mettreAJourCatalogue(String id, Catalogue catalogue) async {
    try {
      await CatalogueService.mettreAJourCatalogue(id, catalogue);
      await chargerCatalogues();
      await chargerCataloguesRecents();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la mise à jour du catalogue: $e';
      notifyListeners();
      return false;
    }
  }

  // Supprimer un catalogue
  Future<bool> supprimerCatalogue(String id) async {
    try {
      await CatalogueService.supprimerCatalogue(id);
      await chargerCatalogues();
      await chargerCataloguesRecents();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la suppression du catalogue: $e';
      notifyListeners();
      return false;
    }
  }

  // Obtenir un catalogue par ID
  Future<Catalogue?> obtenirCatalogueParId(String id) async {
    try {
      return await CatalogueService.obtenirCatalogueParId(id);
    } catch (e) {
      _error = 'Erreur lors de la récupération du catalogue: $e';
      notifyListeners();
      return null;
    }
  }

  // Effacer les erreurs
  void effacerErreur() {
    _error = null;
    notifyListeners();
  }

  // Actualiser les données
  Future<void> actualiser() async {
    await Future.wait([chargerCatalogues(), chargerCataloguesRecents()]);
  }

  // Créer des catalogues de test avec des PDFs fonctionnels
  Future<void> creerCataloguesDeTest() async {
    try {
      // Vérifier si des catalogues existent déjà
      if (_catalogues.isNotEmpty) {
        print('Des catalogues existent déjà, pas besoin de créer des exemples');
        return;
      }

      print('Création de catalogues de test...');

      final cataloguesTest = [
        Catalogue(
          id: '',
          nom: 'Catalogue Produits 2024',
          description: 'Notre catalogue complet de produits pour l\'année 2024',
          type: TypeCatalogue.produits,
          urlPdf:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
          dateCreation: DateTime.now().subtract(const Duration(days: 30)),
          dateModification: DateTime.now().subtract(const Duration(days: 5)),
        ),
        Catalogue(
          id: '',
          nom: 'Promotions Spéciales',
          description: 'Découvrez nos offres promotionnelles exceptionnelles',
          type: TypeCatalogue.promotions,
          urlPdf: 'https://www.africau.edu/images/default/sample.pdf',
          dateCreation: DateTime.now().subtract(const Duration(days: 15)),
          dateModification: DateTime.now().subtract(const Duration(days: 2)),
        ),
        Catalogue(
          id: '',
          nom: 'Nouveautés Innovation 2024',
          description: 'Les dernières innovations et nouveautés de notre gamme',
          type: TypeCatalogue.nouveautes,
          urlPdf:
              'https://www.learningcontainer.com/wp-content/uploads/2019/09/sample-pdf-file.pdf',
          dateCreation: DateTime.now().subtract(const Duration(days: 7)),
          dateModification: DateTime.now().subtract(const Duration(days: 1)),
        ),
      ];

      for (final catalogue in cataloguesTest) {
        await creerCatalogue(catalogue);
      }

      print('Catalogues de test créés avec succès');
    } catch (e) {
      print('Erreur lors de la création des catalogues de test: $e');
    }
  }
}
