import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Widget pour afficher des feedbacks visuels modernes (succès, erreur, avertissement, info)
class ModernFeedback extends StatelessWidget {
  final String message;
  final ModernFeedbackType type;
  final IconData? customIcon;
  final VoidCallback? onAction;
  final String? actionLabel;
  final Duration? duration;
  final bool showProgress;

  const ModernFeedback({
    super.key,
    required this.message,
    required this.type,
    this.customIcon,
    this.onAction,
    this.actionLabel,
    this.duration,
    this.showProgress = false,
  });

  factory ModernFeedback.success({
    required String message,
    VoidCallback? onAction,
    String? actionLabel,
    Duration? duration,
  }) {
    return ModernFeedback(
      message: message,
      type: ModernFeedbackType.success,
      onAction: onAction,
      actionLabel: actionLabel,
      duration: duration,
    );
  }

  factory ModernFeedback.error({
    required String message,
    VoidCallback? onAction,
    String? actionLabel,
    Duration? duration,
  }) {
    return ModernFeedback(
      message: message,
      type: ModernFeedbackType.error,
      onAction: onAction,
      actionLabel: actionLabel,
      duration: duration,
    );
  }

  factory ModernFeedback.warning({
    required String message,
    VoidCallback? onAction,
    String? actionLabel,
    Duration? duration,
  }) {
    return ModernFeedback(
      message: message,
      type: ModernFeedbackType.warning,
      onAction: onAction,
      actionLabel: actionLabel,
      duration: duration,
    );
  }

  factory ModernFeedback.info({
    required String message,
    VoidCallback? onAction,
    String? actionLabel,
    Duration? duration,
  }) {
    return ModernFeedback(
      message: message,
      type: ModernFeedbackType.info,
      onAction: onAction,
      actionLabel: actionLabel,
      duration: duration,
    );
  }

  factory ModernFeedback.loading({
    required String message,
    Duration? duration,
  }) {
    return ModernFeedback(
      message: message,
      type: ModernFeedbackType.info,
      duration: duration,
      showProgress: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final config = _getTypeConfig();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: config.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: config.borderColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: config.shadowColor,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: config.iconBackgroundColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child:
                showProgress
                    ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation(config.iconColor),
                      ),
                    )
                    : Icon(
                      customIcon ?? config.icon,
                      color: config.iconColor,
                      size: 20,
                    ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: config.textColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          if (onAction != null && actionLabel != null)
            TextButton(
              onPressed: onAction,
              style: TextButton.styleFrom(
                foregroundColor: config.actionColor,
                textStyle: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              child: Text(actionLabel!),
            ),
        ],
      ),
    );
  }

  _FeedbackConfig _getTypeConfig() {
    switch (type) {
      case ModernFeedbackType.success:
        return _FeedbackConfig(
          backgroundColor: const Color(0xFFF0FDF4),
          borderColor: const Color(0xFF22C55E),
          shadowColor: const Color(0xFF22C55E).withOpacity(0.1),
          iconBackgroundColor: const Color(0xFF22C55E).withOpacity(0.1),
          iconColor: const Color(0xFF22C55E),
          textColor: const Color(0xFF15803D),
          actionColor: const Color(0xFF22C55E),
          icon: Icons.check_circle,
        );
      case ModernFeedbackType.error:
        return _FeedbackConfig(
          backgroundColor: const Color(0xFFFEF2F2),
          borderColor: const Color(0xFFEF4444),
          shadowColor: const Color(0xFFEF4444).withOpacity(0.1),
          iconBackgroundColor: const Color(0xFFEF4444).withOpacity(0.1),
          iconColor: const Color(0xFFEF4444),
          textColor: const Color(0xFFDC2626),
          actionColor: const Color(0xFFEF4444),
          icon: Icons.error,
        );
      case ModernFeedbackType.warning:
        return _FeedbackConfig(
          backgroundColor: const Color(0xFFFFFBEB),
          borderColor: const Color(0xFFF59E0B),
          shadowColor: const Color(0xFFF59E0B).withOpacity(0.1),
          iconBackgroundColor: const Color(0xFFF59E0B).withOpacity(0.1),
          iconColor: const Color(0xFFF59E0B),
          textColor: const Color(0xFFD97706),
          actionColor: const Color(0xFFF59E0B),
          icon: Icons.warning,
        );
      case ModernFeedbackType.info:
        return _FeedbackConfig(
          backgroundColor: const Color(0xFFEFF6FF),
          borderColor: const Color(0xFF3B82F6),
          shadowColor: const Color(0xFF3B82F6).withOpacity(0.1),
          iconBackgroundColor: const Color(0xFF3B82F6).withOpacity(0.1),
          iconColor: const Color(0xFF3B82F6),
          textColor: const Color(0xFF1D4ED8),
          actionColor: const Color(0xFF3B82F6),
          icon: Icons.info,
        );
    }
  }

  /// Affiche un feedback sous forme de SnackBar moderne
  static void showSnackBar(
    BuildContext context, {
    required String message,
    required ModernFeedbackType type,
    VoidCallback? onAction,
    String? actionLabel,
    Duration? duration,
  }) {
    HapticFeedback.lightImpact();

    final feedback = ModernFeedback(
      message: message,
      type: type,
      onAction: onAction,
      actionLabel: actionLabel,
      duration: duration,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: feedback,
        backgroundColor: Colors.transparent,
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.zero,
        padding: EdgeInsets.zero,
        duration: duration ?? const Duration(seconds: 4),
      ),
    );
  }

  /// Affiche un dialogue de feedback moderne
  static void showDialog(
    BuildContext context, {
    required String title,
    required String message,
    required ModernFeedbackType type,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    String? confirmLabel,
    String? cancelLabel,
  }) {
    HapticFeedback.mediumImpact();

    final config =
        type == ModernFeedbackType.success
            ? _FeedbackConfig(
              backgroundColor: const Color(0xFFF0FDF4),
              borderColor: const Color(0xFF22C55E),
              shadowColor: const Color(0xFF22C55E).withOpacity(0.1),
              iconBackgroundColor: const Color(0xFF22C55E).withOpacity(0.1),
              iconColor: const Color(0xFF22C55E),
              textColor: const Color(0xFF15803D),
              actionColor: const Color(0xFF22C55E),
              icon: Icons.check_circle,
            )
            : type == ModernFeedbackType.error
            ? _FeedbackConfig(
              backgroundColor: const Color(0xFFFEF2F2),
              borderColor: const Color(0xFFEF4444),
              shadowColor: const Color(0xFFEF4444).withOpacity(0.1),
              iconBackgroundColor: const Color(0xFFEF4444).withOpacity(0.1),
              iconColor: const Color(0xFFEF4444),
              textColor: const Color(0xFFDC2626),
              actionColor: const Color(0xFFEF4444),
              icon: Icons.error,
            )
            : type == ModernFeedbackType.warning
            ? _FeedbackConfig(
              backgroundColor: const Color(0xFFFFFBEB),
              borderColor: const Color(0xFFF59E0B),
              shadowColor: const Color(0xFFF59E0B).withOpacity(0.1),
              iconBackgroundColor: const Color(0xFFF59E0B).withOpacity(0.1),
              iconColor: const Color(0xFFF59E0B),
              textColor: const Color(0xFFD97706),
              actionColor: const Color(0xFFF59E0B),
              icon: Icons.warning,
            )
            : _FeedbackConfig(
              backgroundColor: const Color(0xFFEFF6FF),
              borderColor: const Color(0xFF3B82F6),
              shadowColor: const Color(0xFF3B82F6).withOpacity(0.1),
              iconBackgroundColor: const Color(0xFF3B82F6).withOpacity(0.1),
              iconColor: const Color(0xFF3B82F6),
              textColor: const Color(0xFF1D4ED8),
              actionColor: const Color(0xFF3B82F6),
              icon: Icons.info,
            );

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'Fermer',
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return AlertDialog(
          backgroundColor: config.backgroundColor,
          surfaceTintColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: config.borderColor, width: 1),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: config.iconBackgroundColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(config.icon, color: config.iconColor, size: 24),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: config.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: TextStyle(color: config.textColor, fontSize: 16),
          ),
          actions: [
            if (onCancel != null)
              TextButton(
                onPressed: onCancel,
                child: Text(
                  cancelLabel ?? 'Annuler',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            if (onConfirm != null)
              FilledButton(
                onPressed: onConfirm,
                style: FilledButton.styleFrom(
                  backgroundColor: config.actionColor,
                ),
                child: Text(
                  confirmLabel ?? 'OK',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: animation.drive(
            Tween(
              begin: 0.8,
              end: 1.0,
            ).chain(CurveTween(curve: Curves.easeOutBack)),
          ),
          child: FadeTransition(opacity: animation, child: child),
        );
      },
    );
  }
}

/// Types de feedback disponibles
enum ModernFeedbackType { success, error, warning, info }

/// Configuration visuelle pour chaque type de feedback
class _FeedbackConfig {
  final Color backgroundColor;
  final Color borderColor;
  final Color shadowColor;
  final Color iconBackgroundColor;
  final Color iconColor;
  final Color textColor;
  final Color actionColor;
  final IconData icon;

  _FeedbackConfig({
    required this.backgroundColor,
    required this.borderColor,
    required this.shadowColor,
    required this.iconBackgroundColor,
    required this.iconColor,
    required this.textColor,
    required this.actionColor,
    required this.icon,
  });
}

/// Extension pour utiliser ModernFeedback facilement
extension ModernFeedbackExtension on BuildContext {
  void showSuccessFeedback(
    String message, {
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    ModernFeedback.showSnackBar(
      this,
      message: message,
      type: ModernFeedbackType.success,
      onAction: onAction,
      actionLabel: actionLabel,
    );
  }

  void showErrorFeedback(
    String message, {
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    ModernFeedback.showSnackBar(
      this,
      message: message,
      type: ModernFeedbackType.error,
      onAction: onAction,
      actionLabel: actionLabel,
    );
  }

  void showWarningFeedback(
    String message, {
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    ModernFeedback.showSnackBar(
      this,
      message: message,
      type: ModernFeedbackType.warning,
      onAction: onAction,
      actionLabel: actionLabel,
    );
  }

  void showInfoFeedback(
    String message, {
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    ModernFeedback.showSnackBar(
      this,
      message: message,
      type: ModernFeedbackType.info,
      onAction: onAction,
      actionLabel: actionLabel,
    );
  }

  void showLoadingFeedback(String message) {
    ModernFeedback.showSnackBar(
      this,
      message: message,
      type: ModernFeedbackType.info,
    );
  }
}
