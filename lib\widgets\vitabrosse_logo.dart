import 'package:flutter/material.dart';

class VitaBrosseLogo extends StatelessWidget {
  final double height;
  final bool showText;

  const VitaBrosseLogo({super.key, this.height = 40, this.showText = true});
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return ConstrainedBox(
          constraints: BoxConstraints(maxWidth: constraints.maxWidth),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                'assets/images/vitabrosse_logo.png',
                height: height,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  // Si l'image ne peut pas être chargée, afficher un logo texte
                  return Container(
                    height: height,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Center(
                      child: RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'Vita',
                              style: TextStyle(
                                fontSize: height * 0.6,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF1F2937),
                              ),
                            ),
                            TextSpan(
                              text: 'BROSSE',
                              style: TextStyle(
                                fontSize: height * 0.6,
                                fontWeight: FontWeight.w400,
                                color: const Color(0xFF1F2937),
                              ),
                            ),
                            TextSpan(
                              text: '®',
                              style: TextStyle(
                                fontSize: height * 0.3,
                                fontWeight: FontWeight.normal,
                                color: const Color(0xFF1F2937),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
              if (showText) ...[
                const SizedBox(width: 8),
                // Utiliser Flexible pour éviter l'overflow
                Flexible(
                  child: Text(
                    'Gestion Commerciale',
                    style: TextStyle(
                      fontSize: height * 0.4,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF6B7280),
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
