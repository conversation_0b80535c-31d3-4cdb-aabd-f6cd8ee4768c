import '../models/commande.dart';
import '../services/firebase_service.dart';

class CommandeService {
  // Créer une nouvelle commande avec ses items
  Future<String> creerCommande(Commande commande) async {
    try {
      final docRef = await FirebaseService.commandes.add(commande.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('Erreur lors de la création de la commande: $e');
    }
  }

  // Obtenir toutes les commandes
  Future<List<Commande>> obtenirToutesLesCommandes() async {
    try {
      final snapshot =
          await FirebaseService.commandes
              .orderBy('dateCommande', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Commande.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des commandes: $e');
    }
  }

  // Obtenir une commande par ID
  Future<Commande?> obtenirCommandeParId(String id) async {
    try {
      final doc = await FirebaseService.commandes.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return Commande.fromMap({...data, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Erreur lors de la récupération de la commande: $e');
    }
  }

  // Obtenir les commandes d'un client
  Future<List<Commande>> obtenirCommandesParClient(String clientId) async {
    try {
      final snapshot =
          await FirebaseService.commandes
              .where('clientId', isEqualTo: clientId)
              .orderBy('dateCommande', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Commande.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des commandes du client: $e',
      );
    }
  }

  // Mettre à jour le statut d'une commande
  Future<void> mettreAJourStatut(
    String commandeId,
    String nouveauStatut,
  ) async {
    try {
      await FirebaseService.commandes.doc(commandeId).update({
        'statut': nouveauStatut,
      });
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du statut: $e');
    }
  }

  // Obtenir les commandes par statut
  Future<List<Commande>> obtenirCommandesParStatut(String statut) async {
    try {
      final snapshot =
          await FirebaseService.commandes
              .where('statut', isEqualTo: statut)
              .orderBy('dateCommande', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Commande.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des commandes par statut: $e',
      );
    }
  }

  // Compter le nombre total de commandes
  Future<int> obtenirNombreCommandes() async {
    try {
      final snapshot = await FirebaseService.commandes.get();
      return snapshot.docs.length;
    } catch (e) {
      throw Exception('Erreur lors du comptage des commandes: $e');
    }
  }

  // Obtenir les statistiques des commandes
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      final snapshot = await FirebaseService.commandes.get();
      final commandes =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return Commande.fromMap({...data, 'id': doc.id});
          }).toList();

      final total = commandes.length;
      final enCours = commandes.where((c) => c.statut == 'en_cours').length;
      final livrees = commandes.where((c) => c.statut == 'livree').length;
      final annulees = commandes.where((c) => c.statut == 'annulee').length;

      final chiffreAffaires = commandes.fold<double>(
        0,
        (sum, c) => sum + c.montantTotal,
      );

      return {
        'total': total,
        'enCours': enCours,
        'livrees': livrees,
        'annulees': annulees,
        'chiffreAffaires': chiffreAffaires,
      };
    } catch (e) {
      throw Exception('Erreur lors de la récupération des statistiques: $e');
    }
  }
}
