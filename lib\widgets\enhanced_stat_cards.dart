import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/firebase_client_provider.dart';
import '../providers/commande_provider.dart';
import '../providers/produit_provider.dart';
import 'professional_ui_components.dart';

class EnhancedStatCard extends StatefulWidget {
  final String title;
  final IconData icon;
  final Color primaryColor;
  final Future<Map<String, dynamic>> Function() dataFuture;
  final String Function(Map<String, dynamic>) valueExtractor;
  final String Function(Map<String, dynamic>) subtitleExtractor;
  final String Function(Map<String, dynamic>)? trendExtractor;

  const EnhancedStatCard({
    super.key,
    required this.title,
    required this.icon,
    required this.primaryColor,
    required this.dataFuture,
    required this.valueExtractor,
    required this.subtitleExtractor,
    this.trendExtractor,
  });

  @override
  State<EnhancedStatCard> createState() => _EnhancedStatCardState();
}

class _EnhancedStatCardState extends State<EnhancedStatCard> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _fadeAnimation, _pulseAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white,
                    widget.primaryColor.withOpacity(0.02),
                  ],
                ),
                border: Border.all(
                  color: widget.primaryColor.withOpacity(0.1),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: widget.primaryColor.withOpacity(0.08),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.04),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () {
                    // Add haptic feedback or navigation
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: FutureBuilder<Map<String, dynamic>>(
                      future: widget.dataFuture(),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.waiting) {
                          return _buildLoadingState();
                        }
                        
                        if (snapshot.hasError) {
                          return _buildErrorState();
                        }
                        
                        if (!snapshot.hasData) {
                          return _buildEmptyState();
                        }
                        
                        return _buildDataState(snapshot.data!);
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: widget.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                widget.icon,
                size: 20,
                color: widget.primaryColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF6B7280),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        const ModernLoadingIndicator(size: 20),
      ],
    );
  }

  Widget _buildErrorState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.error_outline,
                size: 20,
                color: Colors.red,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF6B7280),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        const Text(
          'Erreur',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        const Text(
          'Impossible de charger',
          style: TextStyle(
            fontSize: 11,
            color: Color(0xFF9CA3AF),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                widget.icon,
                size: 20,
                color: Colors.grey,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF6B7280),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        const Text(
          '0',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF9CA3AF),
          ),
        ),
        const Text(
          'Aucune donnée',
          style: TextStyle(
            fontSize: 11,
            color: Color(0xFF9CA3AF),
          ),
        ),
      ],
    );
  }

  Widget _buildDataState(Map<String, dynamic> data) {
    final value = widget.valueExtractor(data);
    final subtitle = widget.subtitleExtractor(data);
    final trend = widget.trendExtractor?.call(data);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: widget.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: widget.primaryColor.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  widget.icon,
                  size: 20,
                  color: widget.primaryColor,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF6B7280),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (trend != null) ...[
                    const SizedBox(height: 2),
                    _buildTrendIndicator(trend),
                  ],
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          value,
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF1F2937),
            shadows: [
              Shadow(
                color: widget.primaryColor.withOpacity(0.1),
                offset: const Offset(0, 1),
                blurRadius: 2,
              ),
            ],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: const TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.w500,
            color: Color(0xFF9CA3AF),
          ),
        ),
      ],
    );
  }

  Widget _buildTrendIndicator(String trend) {
    final isPositive = trend.startsWith('+');
    final isNegative = trend.startsWith('-');
    
    if (!isPositive && !isNegative) {
      return const SizedBox.shrink();
    }
    
    return Row(
      children: [
        Icon(
          isPositive ? Icons.trending_up : Icons.trending_down,
          size: 12,
          color: isPositive ? const Color(0xFF10B981) : const Color(0xFFEF4444),
        ),
        const SizedBox(width: 2),
        Text(
          trend,
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w600,
            color: isPositive ? const Color(0xFF10B981) : const Color(0xFFEF4444),
          ),
        ),
      ],
    );
  }
}

class DashboardStatCards extends StatefulWidget {
  const DashboardStatCards({super.key});

  @override
  State<DashboardStatCards> createState() => _DashboardStatCardsState();
}

class _DashboardStatCardsState extends State<DashboardStatCards> {
  late Future<Map<String, dynamic>> _clientStatsFuture;
  late Future<Map<String, dynamic>> _productStatsFuture;
  late Future<Map<String, dynamic>> _orderStatsFuture;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    _clientStatsFuture = Provider.of<FirebaseClientProvider>(context, listen: false).obtenirStatistiques();
    _productStatsFuture = Provider.of<ProduitProvider>(context, listen: false).obtenirStatistiques();
    _orderStatsFuture = Provider.of<CommandeProvider>(context, listen: false).obtenirStatistiques();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    
    final cards = [
      EnhancedStatCard(
        title: 'Clients Actifs',
        icon: Icons.people_rounded,
        primaryColor: const Color(0xFF3B82F6),
        dataFuture: () => _clientStatsFuture,
        valueExtractor: (data) => '${data['totalClients'] ?? 0}',
        subtitleExtractor: (data) => 'Total enregistrés',
        trendExtractor: (data) {
          final nouveaux = data['nouveauxClients'] ?? 0;
          return nouveaux > 0 ? '+$nouveaux ce mois' : '';
        },
      ),
      EnhancedStatCard(
        title: 'Produits',
        icon: Icons.inventory_2_rounded,
        primaryColor: const Color(0xFF10B981),
        dataFuture: () => _productStatsFuture,
        valueExtractor: (data) => '${data['nombreTotal'] ?? 0}',
        subtitleExtractor: (data) => 'En stock',
        trendExtractor: (data) {
          final enRupture = data['enRupture'] ?? 0;
          return enRupture > 0 ? '-$enRupture rupture' : '+Stock OK';
        },
      ),
      EnhancedStatCard(
        title: 'Commandes',
        icon: Icons.shopping_cart_rounded,
        primaryColor: const Color(0xFFF59E0B),
        dataFuture: () => _orderStatsFuture,
        valueExtractor: (data) => '${data['total'] ?? 0}',
        subtitleExtractor: (data) => 'Total traitées',
        trendExtractor: (data) {
          final enAttente = data['enAttente'] ?? 0;
          return enAttente > 0 ? '$enAttente en attente' : '';
        },
      ),
      EnhancedStatCard(
        title: 'Chiffre d\'Affaires',
        icon: Icons.trending_up_rounded,
        primaryColor: const Color(0xFF8B5CF6),
        dataFuture: () => _orderStatsFuture,
        valueExtractor: (data) {
          final ca = (data['chiffreAffaires'] as double?) ?? 0.0;
          return '${(ca / 1000).toStringAsFixed(1)}k DT';
        },
        subtitleExtractor: (data) => 'Revenus totaux',
        trendExtractor: (data) {
          final croissance = data['croissanceMensuelle'] ?? 0.0;
          if (croissance > 0) {
            return '+${croissance.toStringAsFixed(1)}%';
          } else if (croissance < 0) {
            return '${croissance.toStringAsFixed(1)}%';
          }
          return '';
        },
      ),
    ];

    if (isSmallScreen) {
      return SizedBox(
        height: 140,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 4),
          itemCount: cards.length,
          separatorBuilder: (context, index) => const SizedBox(width: 12),
          itemBuilder: (context, index) {
            return SizedBox(
              width: screenWidth < 360 ? 140 : 160,
              child: cards[index],
            );
          },
        ),
      );
    } else {
      return GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: screenWidth > 800 ? 4 : 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: screenWidth > 800 ? 2.0 : 2.2,
        children: cards,
      );
    }
  }
}