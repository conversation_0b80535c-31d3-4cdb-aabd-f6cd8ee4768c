import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:io' show Platform;

/// Cross-platform layout utilities for consistent behavior across iOS, Android, and Web
class CrossPlatformLayoutUtils {
  
  /// Gets platform-specific padding for horizontal layouts
  static EdgeInsets getPlatformPadding({
    double mobile = 16.0,
    double tablet = 24.0,
    double desktop = 32.0,
  }) {
    if (kIsWeb) {
      return EdgeInsets.all(desktop);
    }
    
    final screenWidth = _getScreenWidth();
    if (screenWidth > 900) {
      return EdgeInsets.all(desktop);
    } else if (screenWidth > 600) {
      return EdgeInsets.all(tablet);
    } else {
      return EdgeInsets.all(mobile);
    }
  }

  /// Gets platform-specific spacing for horizontal layouts
  static double getPlatformSpacing({
    double mobile = 12.0,
    double tablet = 16.0,
    double desktop = 20.0,
  }) {
    if (kIsWeb) {
      return desktop;
    }
    
    final screenWidth = _getScreenWidth();
    if (screenWidth > 900) {
      return desktop;
    } else if (screenWidth > 600) {
      return tablet;
    } else {
      return mobile;
    }
  }

  /// Gets platform-specific card elevation
  static double getPlatformCardElevation({
    double mobile = 2.0,
    double tablet = 3.0,
    double desktop = 4.0,
  }) {
    if (kIsWeb) {
      return desktop;
    }
    
    if (!kIsWeb && Platform.isIOS) {
      return mobile * 0.8; // iOS prefers lower elevation
    }
    
    final screenWidth = _getScreenWidth();
    if (screenWidth > 900) {
      return desktop;
    } else if (screenWidth > 600) {
      return tablet;
    } else {
      return mobile;
    }
  }

  /// Gets platform-specific border radius
  static BorderRadius getPlatformBorderRadius({
    double mobile = 12.0,
    double tablet = 14.0,
    double desktop = 16.0,
  }) {
    if (kIsWeb) {
      return BorderRadius.circular(desktop);
    }
    
    if (!kIsWeb && Platform.isIOS) {
      return BorderRadius.circular(mobile * 1.2); // iOS prefers more rounded corners
    }
    
    final screenWidth = _getScreenWidth();
    if (screenWidth > 900) {
      return BorderRadius.circular(desktop);
    } else if (screenWidth > 600) {
      return BorderRadius.circular(tablet);
    } else {
      return BorderRadius.circular(mobile);
    }
  }

  /// Gets platform-specific animation duration
  static Duration getPlatformAnimationDuration({
    Duration mobile = const Duration(milliseconds: 200),
    Duration tablet = const Duration(milliseconds: 250),
    Duration desktop = const Duration(milliseconds: 300),
  }) {
    if (kIsWeb) {
      return desktop;
    }
    
    if (!kIsWeb && Platform.isIOS) {
      return mobile; // iOS prefers faster animations
    }
    
    final screenWidth = _getScreenWidth();
    if (screenWidth > 900) {
      return desktop;
    } else if (screenWidth > 600) {
      return tablet;
    } else {
      return mobile;
    }
  }

  /// Determines if hover effects should be enabled
  static bool shouldEnableHoverEffects() {
    return kIsWeb || (!kIsWeb && (Platform.isWindows || Platform.isMacOS || Platform.isLinux));
  }

  /// Gets platform-specific scroll physics
  static ScrollPhysics getPlatformScrollPhysics() {
    if (kIsWeb) {
      return const ClampingScrollPhysics();
    }
    
    if (!kIsWeb && Platform.isIOS) {
      return const BouncingScrollPhysics();
    }
    
    return const ClampingScrollPhysics();
  }

  /// Gets platform-specific text scaling factor
  static double getPlatformTextScaleFactor() {
    if (kIsWeb) {
      return 1.0;
    }
    
    if (!kIsWeb && Platform.isIOS) {
      return 1.0; // iOS handles text scaling automatically
    }
    
    return 1.0;
  }

  /// Gets platform-specific minimum touch target size
  static double getPlatformMinTouchTarget() {
    if (kIsWeb) {
      return 44.0; // Web accessibility guidelines
    }
    
    if (!kIsWeb && Platform.isIOS) {
      return 44.0; // iOS Human Interface Guidelines
    }
    
    return 48.0; // Android Material Design Guidelines
  }

  /// Creates platform-specific horizontal layout configuration
  static HorizontalLayoutConfig createPlatformConfig(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final isMediumScreen = screenWidth < 900;
    
    return HorizontalLayoutConfig(
      padding: getPlatformPadding(),
      spacing: getPlatformSpacing(),
      cardElevation: getPlatformCardElevation(),
      borderRadius: getPlatformBorderRadius(),
      animationDuration: getPlatformAnimationDuration(),
      enableHoverEffects: shouldEnableHoverEffects(),
      scrollPhysics: getPlatformScrollPhysics(),
      textScaleFactor: getPlatformTextScaleFactor(),
      minTouchTarget: getPlatformMinTouchTarget(),
      isSmallScreen: isSmallScreen,
      isMediumScreen: isMediumScreen,
      isLargeScreen: !isSmallScreen && !isMediumScreen,
    );
  }

  /// Adapts widget for platform-specific behavior
  static Widget adaptForPlatform({
    required Widget child,
    Widget? iosChild,
    Widget? androidChild,
    Widget? webChild,
  }) {
    if (kIsWeb && webChild != null) {
      return webChild;
    }
    
    if (!kIsWeb) {
      if (Platform.isIOS && iosChild != null) {
        return iosChild;
      }
      if (Platform.isAndroid && androidChild != null) {
        return androidChild;
      }
    }
    
    return child;
  }

  /// Creates platform-specific safe area
  static Widget wrapWithPlatformSafeArea({
    required Widget child,
    bool top = true,
    bool bottom = true,
    bool left = true,
    bool right = true,
  }) {
    if (kIsWeb) {
      // Web doesn't need safe area
      return child;
    }
    
    return SafeArea(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
      child: child,
    );
  }

  /// Gets current screen width (fallback for when context is not available)
  static double _getScreenWidth() {
    // This is a fallback - in real usage, always prefer MediaQuery.of(context)
    return 400.0; // Default mobile width
  }

  /// Validates layout consistency across platforms
  static bool validateCrossPlatformConsistency(Widget widget) {
    // This would be implemented with platform-specific testing
    // For now, return true as a placeholder
    return true;
  }
}

/// Configuration class for horizontal layouts
class HorizontalLayoutConfig {
  final EdgeInsets padding;
  final double spacing;
  final double cardElevation;
  final BorderRadius borderRadius;
  final Duration animationDuration;
  final bool enableHoverEffects;
  final ScrollPhysics scrollPhysics;
  final double textScaleFactor;
  final double minTouchTarget;
  final bool isSmallScreen;
  final bool isMediumScreen;
  final bool isLargeScreen;

  const HorizontalLayoutConfig({
    required this.padding,
    required this.spacing,
    required this.cardElevation,
    required this.borderRadius,
    required this.animationDuration,
    required this.enableHoverEffects,
    required this.scrollPhysics,
    required this.textScaleFactor,
    required this.minTouchTarget,
    required this.isSmallScreen,
    required this.isMediumScreen,
    required this.isLargeScreen,
  });
}

/// Platform-specific theme extensions
class PlatformThemeExtensions {
  
  /// Creates platform-specific card theme
  static CardTheme createPlatformCardTheme() {
    return CardTheme(
      elevation: CrossPlatformLayoutUtils.getPlatformCardElevation(),
      shape: RoundedRectangleBorder(
        borderRadius: CrossPlatformLayoutUtils.getPlatformBorderRadius(),
      ),
      margin: CrossPlatformLayoutUtils.getPlatformPadding(),
    );
  }

  /// Creates platform-specific input decoration theme
  static InputDecorationTheme createPlatformInputTheme() {
    return InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: CrossPlatformLayoutUtils.getPlatformBorderRadius(),
      ),
      contentPadding: CrossPlatformLayoutUtils.getPlatformPadding(),
    );
  }

  /// Creates platform-specific elevated button theme
  static ElevatedButtonThemeData createPlatformButtonTheme() {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: CrossPlatformLayoutUtils.getPlatformCardElevation(),
        shape: RoundedRectangleBorder(
          borderRadius: CrossPlatformLayoutUtils.getPlatformBorderRadius(),
        ),
        padding: CrossPlatformLayoutUtils.getPlatformPadding(),
        animationDuration: CrossPlatformLayoutUtils.getPlatformAnimationDuration(),
      ),
    );
  }
}

/// Platform-specific responsive breakpoints
class PlatformBreakpoints {
  static const double mobileMax = 600.0;
  static const double tabletMax = 900.0;
  static const double desktopMax = 1200.0;
  
  // iOS-specific breakpoints (slightly different)
  static const double iosPhoneMax = 414.0;
  static const double iosPadMax = 1024.0;
  
  // Android-specific breakpoints
  static const double androidPhoneMax = 600.0;
  static const double androidTabletMax = 960.0;
  
  // Web-specific breakpoints
  static const double webSmallMax = 768.0;
  static const double webMediumMax = 1024.0;
  static const double webLargeMax = 1440.0;

  /// Gets platform-specific breakpoints
  static Map<String, double> getPlatformBreakpoints() {
    if (kIsWeb) {
      return {
        'small': webSmallMax,
        'medium': webMediumMax,
        'large': webLargeMax,
      };
    }
    
    if (!kIsWeb && Platform.isIOS) {
      return {
        'phone': iosPhoneMax,
        'pad': iosPadMax,
      };
    }
    
    return {
      'phone': androidPhoneMax,
      'tablet': androidTabletMax,
    };
  }
}

/// Testing utilities for cross-platform consistency
class CrossPlatformTestingUtils {
  
  /// Tests layout on different screen sizes
  static Future<bool> testResponsiveLayout(Widget widget) async {
    // This would be implemented with widget testing
    // Testing on various screen sizes and orientations
    return true;
  }

  /// Tests platform-specific behaviors
  static Future<bool> testPlatformBehaviors(Widget widget) async {
    // This would be implemented with platform-specific testing
    return true;
  }

  /// Validates accessibility across platforms
  static Future<bool> testCrossPlatformAccessibility(Widget widget) async {
    // This would be implemented with accessibility testing tools
    return true;
  }
}
