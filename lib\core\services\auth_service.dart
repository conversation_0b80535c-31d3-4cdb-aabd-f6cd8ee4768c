import 'http_service.dart';
import 'secure_storage_service.dart';
import 'logging_service.dart';

/// Modèle pour les réponses d'authentification
class AuthResponse {
  final String accessToken;
  final String refreshToken;
  final String userId;
  final String email;
  final Map<String, dynamic>? userData;

  AuthResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.userId,
    required this.email,
    this.userData,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String,
      userId: json['user_id'] as String,
      email: json['email'] as String,
      userData: json['user_data'] as Map<String, dynamic>?,
    );
  }
}

/// Service d'authentification avancé avec gestion des tokens
class AuthService {
  /// Connecte un utilisateur
  static Future<AuthResponse> login({
    required String email,
    required String password,
  }) async {
    try {
      LoggingService.userAction('LOGIN_ATTEMPT', {'email': email});

      final response = await HttpService.post('/auth/login', {
        'email': email,
        'password': password,
      });

      final authResponse = AuthResponse.fromJson(response);

      // Stockage sécurisé des tokens
      await Future.wait([
        SecureStorageService.saveAccessToken(authResponse.accessToken),
        SecureStorageService.saveRefreshToken(authResponse.refreshToken),
        SecureStorageService.saveUserInfo(
          email: authResponse.email,
          userId: authResponse.userId,
        ),
      ]);

      LoggingService.userAction('LOGIN_SUCCESS', {
        'user_id': authResponse.userId,
      });
      return authResponse;
    } catch (e) {
      LoggingService.error('Login failed', error: e);
      rethrow;
    }
  }

  /// Inscrit un nouvel utilisateur
  static Future<AuthResponse> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? company,
  }) async {
    try {
      LoggingService.userAction('REGISTER_ATTEMPT', {'email': email});

      final response = await HttpService.post('/auth/register', {
        'email': email,
        'password': password,
        'first_name': firstName,
        'last_name': lastName,
        if (company != null) 'company': company,
      });

      final authResponse = AuthResponse.fromJson(response);

      // Stockage sécurisé des tokens
      await Future.wait([
        SecureStorageService.saveAccessToken(authResponse.accessToken),
        SecureStorageService.saveRefreshToken(authResponse.refreshToken),
        SecureStorageService.saveUserInfo(
          email: authResponse.email,
          userId: authResponse.userId,
        ),
      ]);

      LoggingService.userAction('REGISTER_SUCCESS', {
        'user_id': authResponse.userId,
      });
      return authResponse;
    } catch (e) {
      LoggingService.error('Registration failed', error: e);
      rethrow;
    }
  }

  /// Rafraîchit le token d'accès
  static Future<String> refreshToken() async {
    try {
      final refreshToken = await SecureStorageService.getRefreshToken();
      if (refreshToken == null) {
        throw Exception('No refresh token available');
      }

      final response = await HttpService.post('/auth/refresh', {
        'refresh_token': refreshToken,
      });

      final newAccessToken = response['access_token'] as String;
      await SecureStorageService.saveAccessToken(newAccessToken);

      LoggingService.info('Token refreshed successfully');
      return newAccessToken;
    } catch (e) {
      LoggingService.error('Token refresh failed', error: e);
      // Si le refresh échoue, déconnecter l'utilisateur
      await logout();
      rethrow;
    }
  }

  /// Déconnecte l'utilisateur
  static Future<void> logout() async {
    try {
      final userId = await SecureStorageService.getUserId();

      // Tentative de déconnexion côté serveur
      try {
        await HttpService.post('/auth/logout', {});
      } catch (e) {
        LoggingService.warning(
          'Server logout failed, continuing with local logout',
          error: e,
        );
      }

      // Suppression locale des tokens
      await SecureStorageService.logout();

      LoggingService.userAction('LOGOUT', {'user_id': userId});
    } catch (e) {
      LoggingService.error('Logout failed', error: e);
      rethrow;
    }
  }

  /// Vérifie si l'utilisateur est connecté
  static Future<bool> isLoggedIn() async {
    try {
      return await SecureStorageService.isLoggedIn();
    } catch (e) {
      LoggingService.error('Failed to check login status', error: e);
      return false;
    }
  }

  /// Obtient le token d'accès actuel (avec refresh automatique si expiré)
  static Future<String?> getValidAccessToken() async {
    try {
      String? token = await SecureStorageService.getAccessToken();

      if (token == null) {
        return null;
      }

      // Vérifier si le token est valide en faisant un appel de test
      try {
        await HttpService.get(
          '/auth/validate',
          headers: {'Authorization': 'Bearer $token'},
        );
        return token;
      } catch (e) {
        // Token expiré, tenter un refresh
        LoggingService.info('Token expired, attempting refresh');
        return await refreshToken();
      }
    } catch (e) {
      LoggingService.error('Failed to get valid access token', error: e);
      return null;
    }
  }

  /// Réinitialise le mot de passe
  static Future<void> resetPassword(String email) async {
    try {
      await HttpService.post('/auth/reset-password', {'email': email});

      LoggingService.userAction('PASSWORD_RESET_REQUEST', {'email': email});
    } catch (e) {
      LoggingService.error('Password reset failed', error: e);
      rethrow;
    }
  }

  /// Change le mot de passe
  static Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final token = await getValidAccessToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      await HttpService.post(
        '/auth/change-password',
        {'current_password': currentPassword, 'new_password': newPassword},
        headers: {'Authorization': 'Bearer $token'},
      );

      LoggingService.userAction('PASSWORD_CHANGED', {});
    } catch (e) {
      LoggingService.error('Password change failed', error: e);
      rethrow;
    }
  }

  /// Récupère les informations du profil utilisateur
  static Future<Map<String, dynamic>> getUserProfile() async {
    try {
      final token = await getValidAccessToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final response = await HttpService.get(
        '/user/profile',
        headers: {'Authorization': 'Bearer $token'},
      );

      return response;
    } catch (e) {
      LoggingService.error('Failed to get user profile', error: e);
      rethrow;
    }
  }

  /// Met à jour le profil utilisateur
  static Future<void> updateUserProfile(
    Map<String, dynamic> profileData,
  ) async {
    try {
      final token = await getValidAccessToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      await HttpService.put(
        '/user/profile',
        profileData,
        headers: {'Authorization': 'Bearer $token'},
      );

      LoggingService.userAction('PROFILE_UPDATED', profileData);
    } catch (e) {
      LoggingService.error('Failed to update user profile', error: e);
      rethrow;
    }
  }
}
