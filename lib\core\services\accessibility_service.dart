import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

/// Service pour gérer l'accessibilité de l'application
class AccessibilityService {
  /// Annonce un message aux technologies d'assistance
  static void announce(String message, {TextDirection? textDirection}) {
    // Utilise la méthode announce de Semantics widget
    // Pour l'instant, on peut utiliser HapticFeedback comme alternative
    HapticFeedback.selectionClick();
  }

  /// Vérifie si les animations sont réduites dans les paramètres système
  static bool areAnimationsReduced(BuildContext context) {
    return MediaQuery.of(context).disableAnimations;
  }

  /// Vérifie si le mode contraste élevé est activé
  static bool isHighContrast(BuildContext context) {
    return MediaQuery.of(context).highContrast;
  }

  /// Obtient le facteur d'échelle de police système
  static double getTextScaleFactor(BuildContext context) {
    return MediaQuery.of(context).textScaleFactor;
  }

  /// Vérifie si le texte en gras est préféré dans les paramètres système
  static bool isBoldTextEnabled(BuildContext context) {
    return MediaQuery.of(context).boldText;
  }

  /// Vibration haptic pour le feedback tactile
  static void provideLightImpact() {
    HapticFeedback.lightImpact();
  }

  static void provideMediumImpact() {
    HapticFeedback.mediumImpact();
  }

  static void provideHeavyImpact() {
    HapticFeedback.heavyImpact();
  }

  static void provideSelectionClick() {
    HapticFeedback.selectionClick();
  }

  /// Widget wrapper pour l'accessibilité améliorée
  static Widget makeAccessible({
    required Widget child,
    required String label,
    String? hint,
    String? value,
    bool excludeSemantics = false,
    bool button = false,
    bool header = false,
    bool selected = false,
    VoidCallback? onTap,
  }) {
    if (excludeSemantics) {
      return ExcludeSemantics(child: child);
    }

    return Semantics(
      label: label,
      hint: hint,
      value: value,
      button: button,
      header: header,
      selected: selected,
      onTap: onTap,
      child: child,
    );
  }

  /// Constantes pour les contrastes couleurs accessibles
  static const double minimumContrastRatio = 4.5;
  static const double enhancedContrastRatio = 7.0;

  /// Calcule le ratio de contraste entre deux couleurs
  static double calculateContrastRatio(Color foreground, Color background) {
    final fgLuminance = _calculateLuminance(foreground);
    final bgLuminance = _calculateLuminance(background);

    final lighter = fgLuminance > bgLuminance ? fgLuminance : bgLuminance;
    final darker = fgLuminance > bgLuminance ? bgLuminance : fgLuminance;

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calcule la luminance d'une couleur
  static double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent(color.red / 255.0);
    final g = _linearizeColorComponent(color.green / 255.0);
    final b = _linearizeColorComponent(color.blue / 255.0);

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linéarise un composant de couleur pour le calcul de luminance
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return math.pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// Vérifie si deux couleurs ont un contraste suffisant
  static bool hasAccessibleContrast(
    Color foreground,
    Color background, {
    bool enhanced = false,
  }) {
    final ratio = calculateContrastRatio(foreground, background);
    final minRatio = enhanced ? enhancedContrastRatio : minimumContrastRatio;
    return ratio >= minRatio;
  }

  /// Ajuste automatiquement la couleur pour améliorer le contraste
  static Color adjustColorForContrast(
    Color foreground,
    Color background, {
    bool enhanced = false,
  }) {
    if (hasAccessibleContrast(foreground, background, enhanced: enhanced)) {
      return foreground;
    }

    // Si le contraste n'est pas suffisant, on assombrit ou éclaircit
    final bgLuminance = _calculateLuminance(background);

    if (bgLuminance > 0.5) {
      // Fond clair, on assombrit le premier plan
      return _darkenColor(foreground);
    } else {
      // Fond sombre, on éclaircit le premier plan
      return _lightenColor(foreground);
    }
  }

  /// Assombrit une couleur
  static Color _darkenColor(Color color) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness * 0.8).clamp(0.0, 1.0)).toColor();
  }

  /// Éclaircit une couleur
  static Color _lightenColor(Color color) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness * 1.2).clamp(0.0, 1.0)).toColor();
  }
}

/// Extension pour ajouter des propriétés d'accessibilité aux widgets
extension AccessibilityExtension on Widget {
  /// Ajoute des propriétés sémantiques à un widget
  Widget withAccessibility({
    required String label,
    String? hint,
    String? value,
    bool button = false,
    bool header = false,
    bool selected = false,
    VoidCallback? onTap,
  }) {
    return AccessibilityService.makeAccessible(
      child: this,
      label: label,
      hint: hint,
      value: value,
      button: button,
      header: header,
      selected: selected,
      onTap: onTap,
    );
  }

  /// Exclut un widget des technologies d'assistance
  Widget excludeFromSemantics() {
    return ExcludeSemantics(child: this);
  }
}
