import 'package:flutter/foundation.dart';
import '../models/commande.dart';
import '../models/commande_item.dart';
import '../models/produit.dart';
import '../services/firebase_service.dart';

// Classe temporaire pour diagnostiquer le problème
class CommandeService {
  Future<String> creerCommande(Commande commande) async {
    try {
      final docRef = await FirebaseService.commandes.add(commande.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('Erreur lors de la création de la commande: $e');
    }
  }

  Future<List<Commande>> obtenirToutesLesCommandes() async {
    try {
      final snapshot =
          await FirebaseService.commandes
              .orderBy('dateCommande', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Commande.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des commandes: $e');
    }
  }

  Future<List<Commande>> obtenirCommandesParClient(String clientId) async {
    try {
      final snapshot =
          await FirebaseService.commandes
              .where('clientId', isEqualTo: clientId)
              .orderBy('dateCommande', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Commande.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des commandes: $e');
    }
  }

  Future<void> mettreAJourStatut(
    String commandeId,
    String nouveauStatut,
  ) async {
    try {
      await FirebaseService.commandes.doc(commandeId).update({
        'statut': nouveauStatut,
      });
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du statut: $e');
    }
  }

  Future<List<Commande>> obtenirCommandesParStatut(String statut) async {
    try {
      final snapshot =
          await FirebaseService.commandes
              .where('statut', isEqualTo: statut)
              .orderBy('dateCommande', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Commande.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des commandes par statut: $e',
      );
    }
  }

  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      print('CommandeService: Début obtenirStatistiques');

      final snapshot = await FirebaseService.commandes.get();
      print('CommandeService: ${snapshot.docs.length} documents trouvés');

      final commandes = <Commande>[];

      for (final doc in snapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          final commande = Commande.fromMap({...data, 'id': doc.id});
          commandes.add(commande);
        } catch (e) {
          print('CommandeService: Erreur lors du parsing d\'une commande: $e');
          // Continue avec les autres commandes
        }
      }

      print(
        'CommandeService: ${commandes.length} commandes parsées avec succès',
      );

      final total = commandes.length;
      final enCours =
          commandes
              .where(
                (c) =>
                    c.statut == StatutCommande.enPreparation ||
                    c.statut == StatutCommande.confirmee ||
                    c.statut == StatutCommande.expediee,
              )
              .length;
      final livrees =
          commandes.where((c) => c.statut == StatutCommande.livree).length;
      final annulees =
          commandes.where((c) => c.statut == StatutCommande.annulee).length;

      final chiffreAffaires = commandes
          .where((c) => c.statut != StatutCommande.annulee)
          .fold<double>(0, (sum, c) => sum + c.montantTotal);

      final result = {
        'total': total,
        'enCours': enCours,
        'livrees': livrees,
        'annulees': annulees,
        'chiffreAffaires': chiffreAffaires,
      };

      print('CommandeService: Résultat: $result');

      return result;
    } catch (e) {
      print('CommandeService: Erreur: $e');
      // Retourner des données vides en cas d'erreur
      return {
        'total': 0,
        'enCours': 0,
        'livrees': 0,
        'annulees': 0,
        'chiffreAffaires': 0.0,
      };
    }
  }
}

class CommandeProvider with ChangeNotifier {
  final CommandeService _commandeService = CommandeService();

  List<Commande> _commandes = [];
  final List<CommandeItem> _panier = [];
  bool _isLoading = false;
  String? _error;

  List<Commande> get commandes => _commandes;
  List<CommandeItem> get panier => _panier;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Calculer le total du panier
  double get totalPanier =>
      _panier.fold(0.0, (total, item) => total + item.sousTotal);

  // Nombre d'articles dans le panier
  int get nombreArticlesPanier =>
      _panier.fold(0, (total, item) => total + item.quantite);

  // Charger toutes les commandes
  Future<void> chargerCommandes() async {
    _setLoading(true);
    try {
      _commandes = await _commandeService.obtenirToutesLesCommandes();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des commandes: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Charger les commandes d'un client
  Future<void> chargerCommandesClient(String clientId) async {
    _setLoading(true);
    try {
      _commandes = await _commandeService.obtenirCommandesParClient(clientId);
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des commandes du client: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Ajouter un produit au panier
  void ajouterAuPanier(Produit produit, int quantite) {
    // Vérifier si le produit est déjà dans le panier
    final index = _panier.indexWhere((item) => item.produitId == produit.id);

    if (index != -1) {
      // Mettre à jour la quantité
      final item = _panier[index];
      final nouvelleQuantite = item.quantite + quantite;
      _panier[index] = CommandeItem.fromProduit(
        produitId: produit.id!,
        nomProduit: produit.nom,
        codeProduit: produit.code,
        prixUnitaire: produit.prix,
        quantite: nouvelleQuantite,
      );
    } else {
      // Ajouter un nouvel item
      _panier.add(
        CommandeItem.fromProduit(
          produitId: produit.id!,
          nomProduit: produit.nom,
          codeProduit: produit.code,
          prixUnitaire: produit.prix,
          quantite: quantite,
        ),
      );
    }

    notifyListeners();
  }

  // Modifier la quantité d'un item dans le panier
  void modifierQuantitePanier(String produitId, int nouvelleQuantite) {
    if (nouvelleQuantite <= 0) {
      retirerDuPanier(produitId);
      return;
    }

    final index = _panier.indexWhere((item) => item.produitId == produitId);
    if (index != -1) {
      final item = _panier[index];
      _panier[index] = CommandeItem.fromProduit(
        produitId: item.produitId,
        nomProduit: item.nomProduit,
        codeProduit: item.codeProduit,
        prixUnitaire: item.prixUnitaire,
        quantite: nouvelleQuantite,
      );
      notifyListeners();
    }
  }

  // Retirer un produit du panier
  void retirerDuPanier(String produitId) {
    _panier.removeWhere((item) => item.produitId == produitId);
    notifyListeners();
  }

  // Vider le panier
  void viderPanier() {
    _panier.clear();
    notifyListeners();
  }

  // Créer une commande directement (pour les représentants commerciaux)
  Future<bool> creerCommande(Commande commande) async {
    try {
      final commandeId = await _commandeService.creerCommande(commande);

      // Ajouter la nouvelle commande à la liste
      final nouvelleCommande = commande.copyWith(id: commandeId);
      _commandes.insert(0, nouvelleCommande);

      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la création de la commande: $e';
      notifyListeners();
      return false;
    }
  }

  // Créer une commande à partir du panier (gardé pour compatibilité)
  Future<bool> creerCommandeDepuisPanier(
    String clientId, {
    String? notes,
  }) async {
    if (_panier.isEmpty) {
      _error = 'Le panier est vide';
      notifyListeners();
      return false;
    }

    try {
      final commande = Commande(
        clientId: clientId,
        dateCommande: DateTime.now(),
        montantTotal: totalPanier,
        notes: notes,
        items: _panier,
      );

      final commandeId = await _commandeService.creerCommande(commande);

      // Ajouter la nouvelle commande à la liste
      final nouvelleCommande = commande.copyWith(id: commandeId);
      _commandes.insert(0, nouvelleCommande);

      // Vider le panier
      viderPanier();

      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la création de la commande: $e';
      notifyListeners();
      return false;
    }
  }

  // Mettre à jour le statut d'une commande
  Future<bool> mettreAJourStatut(
    String commandeId,
    StatutCommande nouveauStatut,
  ) async {
    try {
      await _commandeService.mettreAJourStatut(
        commandeId,
        _getStatutString(nouveauStatut),
      );

      final index = _commandes.indexWhere((c) => c.id == commandeId);
      if (index != -1) {
        _commandes[index] = _commandes[index].copyWith(statut: nouveauStatut);
      }

      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la mise à jour du statut: $e';
      notifyListeners();
      return false;
    }
  }

  // Annuler une commande
  Future<bool> annulerCommande(String commandeId) async {
    try {
      // Utiliser la méthode mettreAJourStatut pour annuler une commande
      final success = await mettreAJourStatut(
        commandeId,
        StatutCommande.annulee,
      );
      return success;
    } catch (e) {
      _error = 'Erreur lors de l\'annulation de la commande: $e';
      notifyListeners();
      return false;
    }
  }

  // Filtrer les commandes par statut
  Future<void> filtrerParStatut(StatutCommande? statut) async {
    _setLoading(true);
    try {
      if (statut == null) {
        _commandes = await _commandeService.obtenirToutesLesCommandes();
      } else {
        final statutString = _getStatutString(statut);
        _commandes = await _commandeService.obtenirCommandesParStatut(
          statutString,
        );
      }
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du filtrage: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Obtenir une commande par ID
  Commande? obtenirCommandeParId(String id) {
    try {
      return _commandes.firstWhere((commande) => commande.id == id);
    } catch (e) {
      return null;
    }
  }

  // Effacer l'erreur
  void effacerErreur() {
    _error = null;
    notifyListeners();
  }

  // Méthode privée pour gérer le loading
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Convertir StatutCommande en string
  String _getStatutString(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return 'en_attente';
      case StatutCommande.confirmee:
        return 'confirmee';
      case StatutCommande.enPreparation:
        return 'en_preparation';
      case StatutCommande.expediee:
        return 'expediee';
      case StatutCommande.livree:
        return 'livree';
      case StatutCommande.annulee:
        return 'annulee';
    }
  }

  // Obtenir les statistiques des commandes
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      print('CommandeProvider: Appel obtenirStatistiques');
      final statistiques = await _commandeService.obtenirStatistiques();
      print('CommandeProvider: Statistiques reçues: $statistiques');
      return statistiques;
    } catch (e) {
      print('CommandeProvider: Erreur: $e');
      // Retourner des données vides en cas d'erreur
      return {
        'total': 0,
        'enCours': 0,
        'livrees': 0,
        'annulees': 0,
        'chiffreAffaires': 0.0,
      };
    }
  }
}
