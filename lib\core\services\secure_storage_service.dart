import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../services/logging_service.dart';

/// Service de stockage sécurisé pour les données sensibles
class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Clés de stockage
  static const String _keyAccessToken = 'access_token';
  static const String _keyRefreshToken = 'refresh_token';
  static const String _keyUserEmail = 'user_email';
  static const String _keyUserId = 'user_id';
  static const String _keyLastLogin = 'last_login';
  static const String _keyBiometricEnabled = 'biometric_enabled';

  /// Stocke le token d'accès
  static Future<void> saveAccessToken(String token) async {
    try {
      await _storage.write(key: _keyAccessToken, value: token);
      LoggingService.info('Access token saved securely');
    } catch (e) {
      LoggingService.error('Failed to save access token', error: e);
      rethrow;
    }
  }

  /// Récupère le token d'accès
  static Future<String?> getAccessToken() async {
    try {
      return await _storage.read(key: _keyAccessToken);
    } catch (e) {
      LoggingService.error('Failed to read access token', error: e);
      return null;
    }
  }

  /// Stocke le token de rafraîchissement
  static Future<void> saveRefreshToken(String token) async {
    try {
      await _storage.write(key: _keyRefreshToken, value: token);
      LoggingService.info('Refresh token saved securely');
    } catch (e) {
      LoggingService.error('Failed to save refresh token', error: e);
      rethrow;
    }
  }

  /// Récupère le token de rafraîchissement
  static Future<String?> getRefreshToken() async {
    try {
      return await _storage.read(key: _keyRefreshToken);
    } catch (e) {
      LoggingService.error('Failed to read refresh token', error: e);
      return null;
    }
  }

  /// Stocke les informations utilisateur
  static Future<void> saveUserInfo({
    required String email,
    required String userId,
  }) async {
    try {
      await Future.wait([
        _storage.write(key: _keyUserEmail, value: email),
        _storage.write(key: _keyUserId, value: userId),
        _storage.write(
          key: _keyLastLogin,
          value: DateTime.now().toIso8601String(),
        ),
      ]);
      LoggingService.info('User info saved securely');
    } catch (e) {
      LoggingService.error('Failed to save user info', error: e);
      rethrow;
    }
  }

  /// Récupère l'email utilisateur
  static Future<String?> getUserEmail() async {
    try {
      return await _storage.read(key: _keyUserEmail);
    } catch (e) {
      LoggingService.error('Failed to read user email', error: e);
      return null;
    }
  }

  /// Récupère l'ID utilisateur
  static Future<String?> getUserId() async {
    try {
      return await _storage.read(key: _keyUserId);
    } catch (e) {
      LoggingService.error('Failed to read user ID', error: e);
      return null;
    }
  }

  /// Récupère la date de dernière connexion
  static Future<DateTime?> getLastLogin() async {
    try {
      final dateString = await _storage.read(key: _keyLastLogin);
      return dateString != null ? DateTime.parse(dateString) : null;
    } catch (e) {
      LoggingService.error('Failed to read last login', error: e);
      return null;
    }
  }

  /// Active/désactive l'authentification biométrique
  static Future<void> setBiometricEnabled(bool enabled) async {
    try {
      await _storage.write(
        key: _keyBiometricEnabled,
        value: enabled.toString(),
      );
      LoggingService.info('Biometric setting updated: $enabled');
    } catch (e) {
      LoggingService.error('Failed to save biometric setting', error: e);
      rethrow;
    }
  }

  /// Vérifie si l'authentification biométrique est activée
  static Future<bool> isBiometricEnabled() async {
    try {
      final value = await _storage.read(key: _keyBiometricEnabled);
      return value == 'true';
    } catch (e) {
      LoggingService.error('Failed to read biometric setting', error: e);
      return false;
    }
  }

  /// Vérifie si l'utilisateur est connecté
  static Future<bool> isLoggedIn() async {
    final token = await getAccessToken();
    return token != null && token.isNotEmpty;
  }

  /// Déconnecte l'utilisateur (supprime tous les tokens)
  static Future<void> logout() async {
    try {
      await Future.wait([
        _storage.delete(key: _keyAccessToken),
        _storage.delete(key: _keyRefreshToken),
        _storage.delete(key: _keyUserEmail),
        _storage.delete(key: _keyUserId),
      ]);
      LoggingService.info('User logged out, tokens cleared');
    } catch (e) {
      LoggingService.error('Failed to clear tokens during logout', error: e);
      rethrow;
    }
  }

  /// Supprime toutes les données stockées
  static Future<void> clearAll() async {
    try {
      await _storage.deleteAll();
      LoggingService.info('All secure storage cleared');
    } catch (e) {
      LoggingService.error('Failed to clear all secure storage', error: e);
      rethrow;
    }
  }

  /// Stocke une valeur personnalisée de manière sécurisée
  static Future<void> store(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
      LoggingService.debug('Custom value stored: $key');
    } catch (e) {
      LoggingService.error('Failed to store custom value', error: e);
      rethrow;
    }
  }

  /// Récupère une valeur personnalisée
  static Future<String?> retrieve(String key) async {
    try {
      return await _storage.read(key: key);
    } catch (e) {
      LoggingService.error('Failed to retrieve custom value', error: e);
      return null;
    }
  }

  /// Supprime une valeur personnalisée
  static Future<void> remove(String key) async {
    try {
      await _storage.delete(key: key);
      LoggingService.debug('Custom value removed: $key');
    } catch (e) {
      LoggingService.error('Failed to remove custom value', error: e);
      rethrow;
    }
  }
}
