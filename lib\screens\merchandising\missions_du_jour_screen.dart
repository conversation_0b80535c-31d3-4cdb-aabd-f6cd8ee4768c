import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/mission.dart';
import '../../providers/mission_provider.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'creer_rapport_screen.dart';

class MissionsDuJourScreen extends StatefulWidget {
  const MissionsDuJourScreen({super.key});

  @override
  State<MissionsDuJourScreen> createState() => _MissionsDuJourScreenState();
}

class _MissionsDuJourScreenState extends State<MissionsDuJourScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerMissions();
    });
  }

  void _chargerMissions() {
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );
    missionProvider.chargerMissions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<MissionProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(color: Color(0xFF10B981)),
              ),
            );
          }

          final missionsDuJour = provider.missionsDuJour;
          final missionsEnRetard = provider.missionsEnRetard;

          return RefreshIndicator(
            onRefresh: () async => _chargerMissions(),
            color: const Color(0xFF10B981),
            child: CustomScrollView(
              slivers: [
                SliverAppBar(
                  expandedHeight:
                      MediaQuery.of(context).size.width < 600 ? 120 : 140,
                  floating: false,
                  pinned: true,
                  backgroundColor: Colors.white,
                  surfaceTintColor: Colors.white,
                  elevation: 0,
                  scrolledUnderElevation: 2,
                  leading: Builder(
                    builder:
                        (context) => IconButton(
                          icon: const Icon(
                            Icons.arrow_back,
                            color: Color(0xFF1E293B),
                            size: 28,
                          ),
                          tooltip: 'Retour',
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                  ),
                  actions: [
                    IconButton(
                      icon: const Icon(
                        Icons.refresh,
                        color: Color(0xFF1E293B),
                        size: 28,
                      ),
                      tooltip: 'Actualiser',
                      onPressed: _chargerMissions,
                    ),
                    const SizedBox(width: 8),
                  ],
                  flexibleSpace: FlexibleSpaceBar(
                    titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
                    title: Row(
                      children: [
                        VitaBrosseLogo(
                          height:
                              MediaQuery.of(context).size.width < 600 ? 24 : 28,
                          showText: false,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Missions du jour',
                                style: TextStyle(
                                  fontWeight: FontWeight.w700,
                                  color: const Color(0xFF1F2937),
                                  fontSize:
                                      MediaQuery.of(context).size.width < 600
                                          ? 18
                                          : 20,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                'Gestion des missions',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.grey.shade600,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white,
                            const Color(0xFF10B981).withValues(alpha: 0.05),
                            const Color(0xFF059669).withValues(alpha: 0.05),
                          ],
                          stops: const [0.0, 0.7, 1.0],
                        ),
                      ),
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Résumé
                        Row(
                          children: [
                            Expanded(
                              child: _buildStatCard(
                                'Missions du jour',
                                missionsDuJour.length.toString(),
                                Icons.today,
                                Colors.blue,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _buildStatCard(
                                'En retard',
                                missionsEnRetard.length.toString(),
                                Icons.schedule,
                                Colors.red,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 24),

                        // Missions en retard
                        if (missionsEnRetard.isNotEmpty) ...[
                          const Text(
                            'Missions en retard',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(height: 12),
                          ...missionsEnRetard.map(
                            (mission) => _buildMissionCard(mission, true),
                          ),
                          const SizedBox(height: 24),
                        ],

                        // Missions du jour
                        const Text(
                          'Missions d\'aujourd\'hui',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),

                        if (missionsDuJour.isEmpty)
                          Center(
                            child: Column(
                              children: [
                                const SizedBox(height: 32),
                                Icon(
                                  Icons.assignment_outlined,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Aucune mission pour aujourd\'hui',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          )
                        else
                          ...missionsDuJour.map(
                            (mission) => _buildMissionCard(mission, false),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMissionCard(Mission mission, bool isEnRetard) {
    Color prioriteColor;
    switch (mission.priorite) {
      case 'urgente':
        prioriteColor = Colors.red;
        break;
      case 'haute':
        prioriteColor = Colors.orange;
        break;
      case 'normale':
        prioriteColor = Colors.blue;
        break;
      case 'faible':
        prioriteColor = Colors.green;
        break;
      default:
        prioriteColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          width: 4,
          height: double.infinity,
          color: isEnRetard ? Colors.red : prioriteColor,
        ),
        title: Text(
          mission.titre,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(mission.clientNom),
            const SizedBox(height: 4),
            Text(
              mission.description,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.flag, size: 16, color: prioriteColor),
                const SizedBox(width: 4),
                Text(
                  mission.priorite.toUpperCase(),
                  style: TextStyle(
                    color: prioriteColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: isEnRetard ? Colors.red : Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '${mission.dateEcheance.day}/${mission.dateEcheance.month}',
                  style: TextStyle(
                    color: isEnRetard ? Colors.red : Colors.grey[600],
                    fontSize: 12,
                    fontWeight:
                        isEnRetard ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: _getStatutColor(mission.statut).withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getStatutText(mission.statut),
            style: TextStyle(
              color: _getStatutColor(mission.statut),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        onTap: () {
          // Navigate to create report for this mission
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CreerRapportScreen(missionId: mission.id),
            ),
          );
        },
      ),
    );
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'termine':
        return const Color(0xFF10B981).withValues(alpha: 0.1);
      case 'en_cours':
        return const Color(0xFFF59E0B).withValues(alpha: 0.1);
      case 'en_attente':
        return const Color(0xFF64748B).withValues(alpha: 0.1);
      default:
        return const Color(0xFF64748B).withValues(alpha: 0.1);
    }
  }

  String _getStatutText(String statut) {
    switch (statut) {
      case 'termine':
        return 'Terminée';
      case 'en_cours':
        return 'En cours';
      case 'en_attente':
        return 'En attente';
      default:
        return 'Inconnu';
    }
  }

  Color _getPrioriteColor(String priorite) {
    switch (priorite) {
      case 'urgente':
        return const Color(0xFFEF4444);
      case 'haute':
        return const Color(0xFFF59E0B);
      case 'normale':
        return const Color(0xFF3B82F6);
      case 'faible':
        return const Color(0xFF10B981);
      default:
        return const Color(0xFF64748B);
    }
  }

  Color _getStatutTextColor(String statut) {
    switch (statut) {
      case 'termine':
        return const Color(0xFF10B981);
      case 'en_cours':
        return const Color(0xFFF59E0B);
      case 'en_attente':
        return const Color(0xFF64748B);
      default:
        return const Color(0xFF64748B);
    }
  }
}
