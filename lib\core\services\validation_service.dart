import '../config/app_config.dart';

/// Service de validation centralisé pour l'application
class ValidationService {
  ValidationService._();

  // Validation des emails
  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'L\'email est obligatoire';
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      caseSensitive: false,
    );

    if (!emailRegex.hasMatch(email)) {
      return 'Veuillez saisir un email valide';
    }

    return null;
  }

  // Validation des mots de passe
  static String? validatePassword(String? password, {bool isSignup = false}) {
    if (password == null || password.isEmpty) {
      return 'Le mot de passe est obligatoire';
    }

    if (isSignup && password.length < AppConfig.minPasswordLength) {
      return 'Le mot de passe doit contenir au moins ${AppConfig.minPasswordLength} caractères';
    }

    // Pour la connexion, on accepte tous les mots de passe existants
    if (!isSignup) {
      return null;
    }

    // Règles supplémentaires pour l'inscription
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      return 'Le mot de passe doit contenir au moins une majuscule';
    }

    if (!RegExp(r'[a-z]').hasMatch(password)) {
      return 'Le mot de passe doit contenir au moins une minuscule';
    }

    if (!RegExp(r'[0-9]').hasMatch(password)) {
      return 'Le mot de passe doit contenir au moins un chiffre';
    }

    return null;
  }

  // Validation des noms (nom, prénom, etc.)
  static String? validateName(String? name, {String fieldName = 'nom'}) {
    if (name == null || name.isEmpty) {
      return 'Le $fieldName est obligatoire';
    }

    final cleanName = name.trim();
    if (cleanName.isEmpty) {
      return 'Le $fieldName est obligatoire';
    }

    if (cleanName.length > AppConfig.maxNameLength) {
      return 'Le $fieldName ne peut pas dépasser ${AppConfig.maxNameLength} caractères';
    } // Vérifier qu'il ne contient que des lettres, espaces et quelques caractères spéciaux
    if (!RegExp(r'^[a-zA-ZÀ-ÿ\s\-\. ]+$').hasMatch(cleanName)) {
      return 'Le $fieldName contient des caractères non autorisés';
    }

    return null;
  }

  // Validation des numéros de téléphone
  static String? validatePhone(String? phone) {
    if (phone == null || phone.isEmpty) {
      return 'Le numéro de téléphone est obligatoire';
    }

    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\.\(\)]'), '');

    if (cleanPhone.length < 8 || cleanPhone.length > 15) {
      return 'Le numéro de téléphone doit contenir entre 8 et 15 chiffres';
    }

    if (!RegExp(r'^\+?[0-9]+$').hasMatch(cleanPhone)) {
      return 'Le numéro de téléphone ne doit contenir que des chiffres';
    }

    return null;
  }

  // Validation optionnelle des numéros de téléphone
  static String? validateOptionalPhone(String? phone) {
    if (phone == null || phone.isEmpty) {
      return null; // Optionnel
    }

    return validatePhone(phone);
  }

  // Validation des prix
  static String? validatePrice(String? price) {
    if (price == null || price.isEmpty) {
      return 'Le prix est obligatoire';
    }

    // Remplacer la virgule par un point pour la conversion
    final cleanPrice = price.replaceAll(',', '.');

    final parsedPrice = double.tryParse(cleanPrice);
    if (parsedPrice == null) {
      return 'Veuillez saisir un prix valide';
    }

    if (parsedPrice < 0) {
      return 'Le prix ne peut pas être négatif';
    }

    if (parsedPrice > AppConfig.maxPrice) {
      return 'Le prix ne peut pas dépasser ${AppConfig.maxPrice}';
    }

    // Vérifier le nombre de décimales (max 2)
    final decimalPart =
        cleanPrice.split('.').length > 1 ? cleanPrice.split('.')[1] : '';

    if (decimalPart.length > 2) {
      return 'Le prix ne peut pas avoir plus de 2 décimales';
    }

    return null;
  }

  // Validation des quantités
  static String? validateQuantity(String? quantity) {
    if (quantity == null || quantity.isEmpty) {
      return 'La quantité est obligatoire';
    }

    final parsedQuantity = int.tryParse(quantity);
    if (parsedQuantity == null) {
      return 'Veuillez saisir une quantité valide';
    }

    if (parsedQuantity <= 0) {
      return 'La quantité doit être supérieure à 0';
    }

    if (parsedQuantity > AppConfig.maxQuantity) {
      return 'La quantité ne peut pas dépasser ${AppConfig.maxQuantity}';
    }

    return null;
  }

  // Validation des descriptions
  static String? validateDescription(String? description) {
    if (description == null || description.isEmpty) {
      return null; // Description optionnelle
    }

    final cleanDescription = description.trim();
    if (cleanDescription.length > AppConfig.maxDescriptionLength) {
      return 'La description ne peut pas dépasser ${AppConfig.maxDescriptionLength} caractères';
    }

    return null;
  }

  // Validation des codes (code client, code produit, etc.)
  static String? validateCode(String? code, {String fieldName = 'code'}) {
    if (code == null || code.isEmpty) {
      return 'Le $fieldName est obligatoire';
    }

    final cleanCode = code.trim().toUpperCase();
    if (cleanCode.isEmpty) {
      return 'Le $fieldName est obligatoire';
    }

    if (cleanCode.length < AppConfig.minCodeLength ||
        cleanCode.length > AppConfig.maxCodeLength) {
      return 'Le $fieldName doit contenir entre ${AppConfig.minCodeLength} et ${AppConfig.maxCodeLength} caractères';
    }

    if (!RegExp(r'^[A-Z0-9]+$').hasMatch(cleanCode)) {
      return 'Le $fieldName ne doit contenir que des lettres et des chiffres';
    }

    return null;
  }

  // Validation des adresses
  static String? validateAddress(String? address) {
    if (address == null || address.isEmpty) {
      return null; // Adresse optionnelle
    }

    final cleanAddress = address.trim();
    if (cleanAddress.length > AppConfig.maxAddressLength) {
      return 'L\'adresse ne peut pas dépasser ${AppConfig.maxAddressLength} caractères';
    }

    return null;
  }

  // Validation des codes postaux
  static String? validatePostalCode(String? postalCode) {
    if (postalCode == null || postalCode.isEmpty) {
      return null; // Code postal optionnel
    }

    final cleanCode = postalCode.replaceAll(RegExp(r'\s'), '');

    if (!RegExp(r'^[0-9]{4,10}$').hasMatch(cleanCode)) {
      return 'Le code postal doit contenir entre 4 et 10 chiffres';
    }

    return null;
  }

  // Validation des villes
  static String? validateCity(String? city) {
    if (city == null || city.isEmpty) {
      return null; // Ville optionnelle
    }

    return validateName(city, fieldName: 'ville');
  }

  // Validation des matricules fiscaux
  static String? validateMatriculeFiscal(String? matricule) {
    if (matricule == null || matricule.isEmpty) {
      return null; // Matricule optionnel
    }

    final cleanMatricule = matricule.replaceAll(RegExp(r'[\s\-]'), '');

    if (cleanMatricule.length < 6 || cleanMatricule.length > 20) {
      return 'Le matricule fiscal doit contenir entre 6 et 20 caractères';
    }

    if (!RegExp(r'^[A-Z0-9]+$').hasMatch(cleanMatricule.toUpperCase())) {
      return 'Le matricule fiscal ne doit contenir que des lettres et des chiffres';
    }

    return null;
  }
}
