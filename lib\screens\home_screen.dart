import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../providers/firebase_client_provider.dart';
import '../providers/produit_provider.dart';
import '../providers/commande_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/vitabrosse_logo.dart';
import '../widgets/professional_ui_components.dart';
import '../widgets/revenue_chart.dart';
import '../widgets/dashboard_charts.dart';
import '../widgets/enhanced_stat_cards.dart';
import '../widgets/dashboard_quick_actions.dart';

import 'clients/clients_screen.dart';
import 'produits/produits_screen.dart';
import 'commandes/commandes_screen.dart';
import 'devis/devis_screen.dart';
import 'merchandising/merchandising_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const DashboardTab(),
    const ClientsScreen(),
    const ProduitsScreen(),
    const CommandesScreen(),
    const DevisScreen(),
    const MerchandisingScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Utiliser addPostFrameCallback pour éviter setState pendant build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerDonnees();
      _preloadStatistics();
    });
  }

  Future<void> _chargerDonnees() async {
    if (!mounted) return;

    try {
      final clientProvider =
          Provider.of<FirebaseClientProvider>(context, listen: false);
      final produitProvider =
          Provider.of<ProduitProvider>(context, listen: false);
      final commandeProvider =
          Provider.of<CommandeProvider>(context, listen: false);

      // Charger les données en parallèle
      await Future.wait([
        clientProvider.loadClients(),
        produitProvider.chargerProduits(),
        commandeProvider.chargerCommandes(),
      ]);
    } catch (e) {
      print('Erreur lors du chargement des données: $e');
    }
  }

  Future<void> _preloadStatistics() async {
    if (!mounted) return;

    try {
      final clientProvider =
          Provider.of<FirebaseClientProvider>(context, listen: false);
      final produitProvider =
          Provider.of<ProduitProvider>(context, listen: false);
      final commandeProvider =
          Provider.of<CommandeProvider>(context, listen: false);

      // Précharger les statistiques
      await Future.wait([
        clientProvider.obtenirStatistiques(),
        produitProvider.obtenirStatistiques(),
        commandeProvider.obtenirStatistiques(),
      ]);
    } catch (e) {
      print('Erreur lors du préchargement des statistiques: $e');
    }
  }

  /// Robust logout method that handles all edge cases
  Future<void> _performLogout(
      BuildContext context, AuthProvider authProvider) async {
    print('🔓 Logout button pressed - Starting robust logout process');

    // Prevent multiple logout attempts
    if (authProvider.isLoading) {
      print('🔄 Logout already in progress, ignoring');
      return;
    }

    // Store context references early to avoid issues
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    bool dialogShown = false;

    try {
      // Close drawer first if it's open
      if (Scaffold.of(context).isDrawerOpen) {
        navigator.pop();
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // Verify context is still valid
      if (!mounted || !context.mounted) {
        print('❌ Context no longer valid, aborting logout');
        return;
      }

      print('🚀 Starting logout process...');

      // Show loading dialog with better error handling
      try {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext dialogContext) => PopScope(
            canPop: false, // Prevent back button
            child: const Dialog(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(width: 20),
                    Text('Déconnexion en cours...'),
                  ],
                ),
              ),
            ),
          ),
        );
        dialogShown = true;
        print('✅ Loading dialog shown');
      } catch (dialogError) {
        print('❌ Failed to show loading dialog: $dialogError');
      }

      // Small delay to ensure dialog is rendered
      await Future.delayed(const Duration(milliseconds: 200));

      // Perform the actual logout
      print('🔄 Calling authProvider.logout()...');
      await authProvider.logout();
      print('✅ authProvider.logout() completed successfully');

      // Close loading dialog if it was shown
      if (dialogShown && mounted && context.mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
          print('✅ Loading dialog closed');
        } catch (popError) {
          print('❌ Error closing loading dialog: $popError');
        }
      }

      // Add delay to ensure logout is fully processed
      await Future.delayed(const Duration(milliseconds: 300));

      print(
          '✅ Logout completed successfully - AuthWrapper will handle navigation');
    } catch (e) {
      print('❌ Error during logout process: $e');

      // Ensure loading dialog is closed
      if (dialogShown && mounted && context.mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (popError) {
          print('❌ Error closing dialog after error: $popError');
        }
      }

      // Show error message if context is still valid
      if (mounted && context.mounted) {
        try {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la déconnexion: $e'),
              backgroundColor: const Color(0xFFEF4444),
              duration: const Duration(seconds: 4),
              action: SnackBarAction(
                label: 'Réessayer',
                textColor: Colors.white,
                onPressed: () => _performLogout(context, authProvider),
              ),
            ),
          );
        } catch (snackBarError) {
          print('❌ Error showing error snackbar: $snackBarError');
        }
      }

      // Force logout as last resort
      try {
        print('🔄 Attempting force logout as fallback...');
        // Try logout again as a fallback
        await authProvider.logout();
      } catch (forceError) {
        print('❌ Force logout also failed: $forceError');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _selectedIndex,
          onTap: (index) {
            final previousIndex = _selectedIndex;
            setState(() {
              _selectedIndex = index;
            });

            // Only refresh clients if we're switching TO the clients tab from another tab
            if (index == 1 && previousIndex != 1) {
              // Clients tab
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  final provider = context.read<FirebaseClientProvider>();
                  // Only refresh if the list is empty or hasn't been loaded
                  if (provider.clients.isEmpty) {
                    provider.refreshClients();
                  }
                }
              });
            }
          },
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.dashboard_outlined),
              activeIcon: Icon(Icons.dashboard),
              label: 'Tableau de bord',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people_outline),
              activeIcon: Icon(Icons.people),
              label: 'Clients',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.inventory_2_outlined),
              activeIcon: Icon(Icons.inventory_2),
              label: 'Produits',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.shopping_cart_outlined),
              activeIcon: Icon(Icons.shopping_cart),
              label: 'Commandes',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.description_outlined),
              activeIcon: Icon(Icons.description),
              label: 'Devis',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.store_outlined),
              activeIcon: Icon(Icons.store),
              label: 'Merchandising',
            ),
          ],
        ),
      ),
    );
  }
}

class DashboardTab extends StatefulWidget {
  const DashboardTab({super.key});

  @override
  State<DashboardTab> createState() => _DashboardTabState();
}

class _DashboardTabState extends State<DashboardTab> {
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    // Trigger initial data loading
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  Future<void> _loadInitialData() async {
    if (_hasInitialized || !mounted) return;

    try {
      final clientProvider = Provider.of<FirebaseClientProvider>(
        context,
        listen: false,
      );
      final productProvider = Provider.of<ProduitProvider>(
        context,
        listen: false,
      );
      final orderProvider = Provider.of<CommandeProvider>(
        context,
        listen: false,
      );

      // Load data if not already loaded
      List<Future> loadingTasks = [];

      if (clientProvider.clients.isEmpty && !clientProvider.isLoading) {
        loadingTasks.add(clientProvider.loadClients());
      }
      if (productProvider.produits.isEmpty && !productProvider.isLoading) {
        loadingTasks.add(productProvider.chargerProduits());
      }
      if (orderProvider.commandes.isEmpty && !orderProvider.isLoading) {
        loadingTasks.add(orderProvider.chargerCommandes());
      }

      if (loadingTasks.isNotEmpty && mounted) {
        await Future.wait(loadingTasks);
      }

      if (mounted) {
        _hasInitialized = true;
      }
    } catch (e) {
      // Silently handle errors if widget is disposed
      if (mounted) {
        print('Error loading initial data: $e');
      }
    }
  }

  Future<void> _refreshData() async {
    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );
    final productProvider = Provider.of<ProduitProvider>(
      context,
      listen: false,
    );
    final orderProvider = Provider.of<CommandeProvider>(context, listen: false);

    await Future.wait([
      clientProvider.loadClients(),
      productProvider.chargerProduits(),
      orderProvider.chargerCommandes(),
    ]);
  }

  Widget _buildDrawer() {
    return Drawer(
      backgroundColor: const Color(0xFFF8FAFC),
      child: SafeArea(
        child: Column(
          children: [
            // Profile Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0)],
                ),
              ),
              child: Consumer<AuthProvider>(
                builder: (context, authProvider, _) {
                  final userEmail = authProvider.currentUser ?? '';
                  final userName = userEmail.isNotEmpty
                      ? userEmail.split('@')[0].replaceAll('.', ' ')
                      : 'Utilisateur';

                  return Column(
                    children: [
                      // Logo
                      const VitaBrosseLogo(height: 40, showText: false),
                      const SizedBox(height: 16),

                      // User Avatar
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            userName.isNotEmpty
                                ? userName[0].toUpperCase()
                                : 'U',
                            style: const TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF3B82F6),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // User Info
                      Text(
                        userName.toUpperCase(),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: Color(0xFF1E293B),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        userEmail,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF64748B),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),

            const SizedBox(height: 24),

            // Menu Items
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // Settings
                    Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(
                              0xFF3B82F6,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.settings_outlined,
                            color: Color(0xFF3B82F6),
                            size: 20,
                          ),
                        ),
                        title: const Text(
                          'Paramètres',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1E293B),
                          ),
                        ),
                        trailing: const Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: Color(0xFF64748B),
                        ),
                        onTap: () {
                          Navigator.pop(context);
                          // TODO: Navigate to settings
                        },
                      ),
                    ),

                    const Spacer(),

                    // Logout Button
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(bottom: 24),
                      child: Consumer<AuthProvider>(
                        builder: (context, authProvider, _) {
                          return ElevatedButton.icon(
                            onPressed: () =>
                                _performLogout(context, authProvider),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFEF4444),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            icon: const Icon(Icons.logout_rounded, size: 20),
                            label: const Text(
                              'Déconnexion',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Robust logout method that handles all edge cases
  Future<void> _performLogout(
      BuildContext context, AuthProvider authProvider) async {
    print('🔓 Logout button pressed - Starting robust logout process');

    // Prevent multiple logout attempts
    if (authProvider.isLoading) {
      print('🔄 Logout already in progress, ignoring');
      return;
    }

    // Store context references early to avoid issues
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    bool dialogShown = false;

    try {
      // Close drawer first if it's open
      if (Scaffold.of(context).isDrawerOpen) {
        navigator.pop();
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // Verify context is still valid
      if (!mounted || !context.mounted) {
        print('❌ Context no longer valid, aborting logout');
        return;
      }

      print('🚀 Starting logout process...');

      // Show loading dialog with better error handling
      try {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext dialogContext) => PopScope(
            canPop: false, // Prevent back button
            child: const Dialog(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(width: 20),
                    Text('Déconnexion en cours...'),
                  ],
                ),
              ),
            ),
          ),
        );
        dialogShown = true;
        print('✅ Loading dialog shown');
      } catch (dialogError) {
        print('❌ Failed to show loading dialog: $dialogError');
      }

      // Small delay to ensure dialog is rendered
      await Future.delayed(const Duration(milliseconds: 200));

      // Perform the actual logout
      print('🔄 Calling authProvider.logout()...');
      await authProvider.logout();
      print('✅ authProvider.logout() completed successfully');

      // Close loading dialog if it was shown
      if (dialogShown && mounted && context.mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
          print('✅ Loading dialog closed');
        } catch (popError) {
          print('❌ Error closing loading dialog: $popError');
        }
      }

      // Add delay to ensure logout is fully processed
      await Future.delayed(const Duration(milliseconds: 300));

      print(
          '✅ Logout completed successfully - AuthWrapper will handle navigation');
    } catch (e) {
      print('❌ Error during logout process: $e');

      // Ensure loading dialog is closed
      if (dialogShown && mounted && context.mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (popError) {
          print('❌ Error closing dialog after error: $popError');
        }
      }

      // Show error message if context is still valid
      if (mounted && context.mounted) {
        try {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la déconnexion: $e'),
              backgroundColor: const Color(0xFFEF4444),
              duration: const Duration(seconds: 4),
              action: SnackBarAction(
                label: 'Réessayer',
                textColor: Colors.white,
                onPressed: () => _performLogout(context, authProvider),
              ),
            ),
          );
        } catch (snackBarError) {
          print('❌ Error showing error snackbar: $snackBarError');
        }
      }

      // Force logout as last resort
      try {
        print('🔄 Attempting force logout as fallback...');
        // Try logout again as a fallback
        await authProvider.logout();
      } catch (forceError) {
        print('❌ Force logout also failed: $forceError');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: _buildDrawer(),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight:
                  MediaQuery.of(context).size.width < 600 ? 120 : 140,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              elevation: 0,
              scrolledUnderElevation: 2,
              leading: Builder(
                builder: (context) => IconButton(
                  icon: const Icon(
                    Icons.menu,
                    color: Color(0xFF1E293B),
                    size: 28,
                  ),
                  onPressed: () => Scaffold.of(context).openDrawer(),
                ),
              ),
              flexibleSpace: FlexibleSpaceBar(
                titlePadding: EdgeInsets.only(
                  left: MediaQuery.of(context).size.width < 600 ? 16 : 24,
                  bottom: 16,
                ),
                title: Row(
                  children: [
                    const VitaBrosseLogo(height: 28, showText: false),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'VitaBrosse Pro',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: Color(0xFF1F2937),
                              fontSize: 22,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 2),
                          Text(
                            'Tableau de bord',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(
                  MediaQuery.of(context).size.width < 480 ? 16.0 : 20.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Bienvenue !',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF1F2937),
                            fontSize: MediaQuery.of(context).size.width < 360
                                ? 20
                                : MediaQuery.of(context).size.width < 480
                                    ? 22
                                    : 24,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Voici un aperçu de votre activité commerciale',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Colors.grey.shade600,
                            fontSize: MediaQuery.of(context).size.width < 360
                                ? 13
                                : MediaQuery.of(context).size.width < 480
                                    ? 14
                                    : 16,
                          ),
                    ),
                    SizedBox(
                      height: MediaQuery.of(context).size.width < 480 ? 24 : 32,
                    ),

                    // Enhanced Statistics Cards
                    const DashboardStatCards(),

                    SizedBox(
                      height: MediaQuery.of(context).size.width < 480 ? 32 : 40,
                    ),

                    // Charts Section
                    const SectionHeader(
                      title: 'Analyses & Tendances',
                      subtitle:
                          'Vue d\'ensemble de votre performance commerciale',
                    ),

                    const SizedBox(height: 20),

                    // Revenue Distribution Chart
                    ProfessionalCard(
                      hasShadow: true,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF6366F1)
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.pie_chart_rounded,
                                    size: 20,
                                    color: Color(0xFF6366F1),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Répartition du Chiffre d\'Affaires',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFF1F2937),
                                        ),
                                      ),
                                      Text(
                                        'Distribution par client principal',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF6B7280),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const RevenueChart(),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Monthly Trend and Order Status Row
                    MediaQuery.of(context).size.width < 600
                        ? Column(
                            children: [
                              _buildMonthlyTrendCard(),
                              const SizedBox(height: 16),
                              _buildOrderStatusCard(),
                            ],
                          )
                        : Row(
                            children: [
                              Expanded(child: _buildMonthlyTrendCard()),
                              const SizedBox(width: 16),
                              Expanded(child: _buildOrderStatusCard()),
                            ],
                          ),

                    const SizedBox(height: 24),

                    // Top Products Chart
                    _buildTopProductsCard(),

                    const SizedBox(height: 32),

                    // Quick Actions Section
                    const DashboardQuickActions(),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlyTrendCard() {
    return ProfessionalCard(
      hasShadow: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF10B981).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.trending_up_rounded,
                    size: 20,
                    color: Color(0xFF10B981),
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Évolution Mensuelle',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                      Text(
                        'Tendance des revenus sur 6 mois',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const MonthlyRevenueChart(),
        ],
      ),
    );
  }

  Widget _buildOrderStatusCard() {
    return ProfessionalCard(
      hasShadow: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF59E0B).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.donut_small_rounded,
                    size: 20,
                    color: Color(0xFFF59E0B),
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Statut des Commandes',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                      Text(
                        'Répartition par état',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const OrderStatusChart(),
        ],
      ),
    );
  }

  Widget _buildTopProductsCard() {
    return ProfessionalCard(
      hasShadow: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.star_rounded,
                    size: 20,
                    color: Color(0xFF8B5CF6),
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Produits les Plus Vendus',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                      Text(
                        'Top 5 des meilleures ventes',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const TopProductsChart(),
        ],
      ),
    );
  }
}
