# 📋 Documentation - Application Admin React
## VitaBrosse Pro - Interface d'Administration

### 🎯 Vue d'ensemble
Cette documentation décrit l'architecture et les fonctionnalités de l'application admin React pour VitaBrosse Pro, une solution complète de gestion commerciale et merchandising.

---

## 🏗️ Architecture Technique

### Stack Technologique
```
Frontend: React 18+ + TypeScript
UI Framework: Material-UI (MUI) v5 / Ant Design
State Management: Redux Toolkit + RTK Query
Routing: React Router v6
HTTP Client: Axios
Authentication: JWT + React Context
Charts: Recharts / Chart.js
Tables: React Table / Ag-Grid
Forms: React Hook Form + Yup
Styling: Styled Components / Tailwind CSS
```

### Structure du Projet
```
admin-react/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── components/           # Composants réutilisables
│   │   ├── common/          # Composants génériques
│   │   ├── layout/          # Layout components
│   │   ├── forms/           # Formulaires
│   │   └── charts/          # Graphiques
│   ├── pages/               # Pages principales
│   │   ├── dashboard/       # Tableau de bord
│   │   ├── missions/        # Gestion des missions
│   │   ├── clients/         # Gestion des clients
│   │   ├── commandes/       # Gestion des commandes
│   │   ├── merchandising/   # Merchandising
│   │   └── analytics/       # Analyses
│   ├── services/            # Services API
│   ├── store/              # Redux store
│   ├── hooks/              # Custom hooks
│   ├── utils/              # Utilitaires
│   ├── types/              # Types TypeScript
│   └── App.tsx
├── package.json
└── README.md
```

---

## 🔐 Authentification & Autorisation

### Système d'Authentification
```typescript
// types/auth.ts
export interface User {
  id: string;
  email: string;
  nom: string;
  prenom: string;
  role: 'admin' | 'commercial' | 'merchandiser';
  permissions: string[];
  dateCreation: Date;
  derniereConnexion?: Date;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}
```

### Service d'Authentification
```typescript
// services/authService.ts
import axios from 'axios';
import { LoginRequest, AuthResponse } from '../types/auth';

class AuthService {
  private baseURL = process.env.REACT_APP_API_URL;

  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await axios.post(`${this.baseURL}/auth/login`, credentials);
    
    // Stocker les tokens
    localStorage.setItem('accessToken', response.data.accessToken);
    localStorage.setItem('refreshToken', response.data.refreshToken);
    
    return response.data;
  }

  async logout(): Promise<void> {
    await axios.post(`${this.baseURL}/auth/logout`);
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }

  async refreshToken(): Promise<string> {
    const refreshToken = localStorage.getItem('refreshToken');
    const response = await axios.post(`${this.baseURL}/auth/refresh`, {
      refreshToken
    });
    
    localStorage.setItem('accessToken', response.data.accessToken);
    return response.data.accessToken;
  }

  getAccessToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }
}

export default new AuthService();
```

---

## 📊 Modèles de Données

### Modèle Mission
```typescript
// types/mission.ts
export interface Mission {
  id: string;
  titre: string;
  description: string;
  magasinId: string;
  magasinNom: string;
  merchandiserId: string;
  commercialId: string;
  dateCreation: Date;
  dateEcheance: Date;
  statut: 'en_attente' | 'en_cours' | 'terminee' | 'annulee';
  priorite: 'faible' | 'normale' | 'haute' | 'urgente';
  taches: string[];
  notes?: string;
  parametres?: Record<string, any>;
}

export interface CreateMissionRequest {
  titre: string;
  description: string;
  magasinId: string;
  merchandiserId: string;
  dateEcheance: Date;
  priorite: Mission['priorite'];
  taches: string[];
  notes?: string;
}
```

### Modèle Client
```typescript
// types/client.ts
export interface Client {
  id: string;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  adresse: string;
  dateCreation: Date;
  codeClient?: string;
  matriculeFiscal?: string;
  categorie?: string;
  modeReglement?: string;
}

export interface CreateClientRequest {
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  adresse: string;
  codeClient?: string;
  matriculeFiscal?: string;
  categorie?: string;
  modeReglement?: string;
}
```

### Modèle Commande
```typescript
// types/commande.ts
export interface Commande {
  id: string;
  clientId: string;
  dateCommande: Date;
  statut: 'en_attente' | 'confirmee' | 'en_preparation' | 'expediee' | 'livree' | 'annulee';
  montantTotal: number;
  notes?: string;
  items: CommandeItem[];
}

export interface CommandeItem {
  id: string;
  produitId: string;
  nomProduit: string;
  prixUnitaire: number;
  quantite: number;
  remise: number;
  total: number;
}
```

---

## 🛠️ Services API

### Service Mission
```typescript
// services/missionService.ts
import { apiClient } from './apiClient';
import { Mission, CreateMissionRequest } from '../types/mission';

class MissionService {
  async getAllMissions(): Promise<Mission[]> {
    const response = await apiClient.get('/missions');
    return response.data;
  }

  async getMissionById(id: string): Promise<Mission> {
    const response = await apiClient.get(`/missions/${id}`);
    return response.data;
  }

  async createMission(mission: CreateMissionRequest): Promise<Mission> {
    const response = await apiClient.post('/missions', mission);
    return response.data;
  }

  async updateMission(id: string, mission: Partial<Mission>): Promise<Mission> {
    const response = await apiClient.put(`/missions/${id}`, mission);
    return response.data;
  }

  async deleteMission(id: string): Promise<void> {
    await apiClient.delete(`/missions/${id}`);
  }

  async getMissionsByMerchandiser(merchandiserId: string): Promise<Mission[]> {
    const response = await apiClient.get(`/missions/merchandiser/${merchandiserId}`);
    return response.data;
  }

  async getMissionsByStatus(status: Mission['statut']): Promise<Mission[]> {
    const response = await apiClient.get(`/missions/status/${status}`);
    return response.data;
  }

  async updateMissionStatus(id: string, status: Mission['statut']): Promise<Mission> {
    const response = await apiClient.patch(`/missions/${id}/status`, { status });
    return response.data;
  }
}

export default new MissionService();
```

### Configuration API Client
```typescript
// services/apiClient.ts
import axios from 'axios';
import authService from './authService';

export const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Interceptor pour ajouter le token d'authentification
apiClient.interceptors.request.use(
  (config) => {
    const token = authService.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Interceptor pour gérer le refresh token
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const newToken = await authService.refreshToken();
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        authService.logout();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);
```

---

## 📋 Pages Principales

### 1. Dashboard (Tableau de Bord)
```typescript
// pages/dashboard/Dashboard.tsx
import React from 'react';
import { Grid, Card, CardContent, Typography } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { StatisticsCard } from '../../components/dashboard/StatisticsCard';
import { MissionChart } from '../../components/charts/MissionChart';
import { RecentActivities } from '../../components/dashboard/RecentActivities';
import dashboardService from '../../services/dashboardService';

export const Dashboard: React.FC = () => {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: dashboardService.getStatistics,
  });

  if (isLoading) return <div>Chargement...</div>;

  return (
    <Grid container spacing={3}>
      {/* Statistiques principales */}
      <Grid item xs={12} md={3}>
        <StatisticsCard
          title="Missions Totales"
          value={stats?.totalMissions || 0}
          icon="assignment"
          color="primary"
        />
      </Grid>
      
      <Grid item xs={12} md={3}>
        <StatisticsCard
          title="Missions En Cours"
          value={stats?.missionsEnCours || 0}
          icon="schedule"
          color="warning"
        />
      </Grid>
      
      <Grid item xs={12} md={3}>
        <StatisticsCard
          title="Missions Terminées"
          value={stats?.missionsTerminees || 0}
          icon="check_circle"
          color="success"
        />
      </Grid>
      
      <Grid item xs={12} md={3}>
        <StatisticsCard
          title="Clients Actifs"
          value={stats?.clientsActifs || 0}
          icon="people"
          color="info"
        />
      </Grid>

      {/* Graphiques */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Évolution des Missions
            </Typography>
            <MissionChart data={stats?.missionTrends || []} />
          </CardContent>
        </Card>
      </Grid>

      {/* Activités récentes */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Activités Récentes
            </Typography>
            <RecentActivities activities={stats?.recentActivities || []} />
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};
```

### 2. Gestion des Missions
```typescript
// pages/missions/MissionList.tsx
import React, { useState } from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Dialog,
} from '@mui/material';
import { Edit, Delete, Add, Visibility } from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Mission } from '../../types/mission';
import missionService from '../../services/missionService';
import { MissionForm } from '../../components/missions/MissionForm';
import { MissionDetail } from '../../components/missions/MissionDetail';

export const MissionList: React.FC = () => {
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDetailOpen, setIsDetailOpen] = useState(false);

  const queryClient = useQueryClient();

  const { data: missions, isLoading } = useQuery({
    queryKey: ['missions'],
    queryFn: missionService.getAllMissions,
  });

  const deleteMutation = useMutation({
    mutationFn: missionService.deleteMission,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['missions'] });
    },
  });

  const getStatusColor = (status: Mission['statut']) => {
    const colors = {
      'en_attente': 'warning',
      'en_cours': 'info',
      'terminee': 'success',
      'annulee': 'error',
    };
    return colors[status] as any;
  };

  const getPriorityColor = (priorite: Mission['priorite']) => {
    const colors = {
      'faible': 'default',
      'normale': 'primary',
      'haute': 'warning',
      'urgente': 'error',
    };
    return colors[priorite] as any;
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette mission ?')) {
      deleteMutation.mutate(id);
    }
  };

  if (isLoading) return <div>Chargement...</div>;

  return (
    <Paper>
      <div style={{ padding: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <h2>Gestion des Missions</h2>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setIsFormOpen(true)}
        >
          Nouvelle Mission
        </Button>
      </div>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Titre</TableCell>
              <TableCell>Magasin</TableCell>
              <TableCell>Merchandiser</TableCell>
              <TableCell>Date d'échéance</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Priorité</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {missions?.map((mission) => (
              <TableRow key={mission.id}>
                <TableCell>{mission.titre}</TableCell>
                <TableCell>{mission.magasinNom}</TableCell>
                <TableCell>{mission.merchandiserId}</TableCell>
                <TableCell>
                  {new Date(mission.dateEcheance).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <Chip
                    label={mission.statut.replace('_', ' ')}
                    color={getStatusColor(mission.statut)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={mission.priorite}
                    color={getPriorityColor(mission.priorite)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    onClick={() => {
                      setSelectedMission(mission);
                      setIsDetailOpen(true);
                    }}
                  >
                    <Visibility />
                  </IconButton>
                  <IconButton
                    onClick={() => {
                      setSelectedMission(mission);
                      setIsFormOpen(true);
                    }}
                  >
                    <Edit />
                  </IconButton>
                  <IconButton onClick={() => handleDelete(mission.id)}>
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Formulaire de création/modification */}
      <Dialog
        open={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <MissionForm
          mission={selectedMission}
          onClose={() => {
            setIsFormOpen(false);
            setSelectedMission(null);
          }}
        />
      </Dialog>

      {/* Détail de la mission */}
      <Dialog
        open={isDetailOpen}
        onClose={() => setIsDetailOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <MissionDetail
          mission={selectedMission}
          onClose={() => {
            setIsDetailOpen(false);
            setSelectedMission(null);
          }}
        />
      </Dialog>
    </Paper>
  );
};
```

### 3. Gestion des Clients
```typescript
// pages/clients/ClientList.tsx
import React, { useState } from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  TextField,
  InputAdornment,
  Dialog,
} from '@mui/material';
import { Search, Add, Edit, Delete } from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Client } from '../../types/client';
import clientService from '../../services/clientService';
import { ClientForm } from '../../components/clients/ClientForm';

export const ClientList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);

  const queryClient = useQueryClient();

  const { data: clients, isLoading } = useQuery({
    queryKey: ['clients'],
    queryFn: clientService.getAllClients,
  });

  const deleteMutation = useMutation({
    mutationFn: clientService.deleteClient,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clients'] });
    },
  });

  const filteredClients = clients?.filter(client =>
    client.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce client ?')) {
      deleteMutation.mutate(id);
    }
  };

  if (isLoading) return <div>Chargement...</div>;

  return (
    <Paper>
      <div style={{ padding: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2>Gestion des Clients</h2>
        <div style={{ display: 'flex', gap: '16px' }}>
          <TextField
            placeholder="Rechercher un client..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setIsFormOpen(true)}
          >
            Nouveau Client
          </Button>
        </div>
      </div>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Nom</TableCell>
              <TableCell>Prénom</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Téléphone</TableCell>
              <TableCell>Code Client</TableCell>
              <TableCell>Date de création</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredClients?.map((client) => (
              <TableRow key={client.id}>
                <TableCell>{client.nom}</TableCell>
                <TableCell>{client.prenom}</TableCell>
                <TableCell>{client.email}</TableCell>
                <TableCell>{client.telephone}</TableCell>
                <TableCell>{client.codeClient || '-'}</TableCell>
                <TableCell>
                  {new Date(client.dateCreation).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <IconButton
                    onClick={() => {
                      setSelectedClient(client);
                      setIsFormOpen(true);
                    }}
                  >
                    <Edit />
                  </IconButton>
                  <IconButton onClick={() => handleDelete(client.id)}>
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Formulaire de création/modification */}
      <Dialog
        open={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <ClientForm
          client={selectedClient}
          onClose={() => {
            setIsFormOpen(false);
            setSelectedClient(null);
          }}
        />
      </Dialog>
    </Paper>
  );
};
```

---

## 🔧 Composants Utilitaires

### Composant de Statistiques
```typescript
// components/dashboard/StatisticsCard.tsx
import React from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';
import { Icon } from '@mui/material';

interface StatisticsCardProps {
  title: string;
  value: number;
  icon: string;
  color: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
}

export const StatisticsCard: React.FC<StatisticsCardProps> = ({
  title,
  value,
  icon,
  color,
}) => {
  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center">
          <Box flexGrow={1}>
            <Typography color="textSecondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h4" component="h2">
              {value.toLocaleString()}
            </Typography>
          </Box>
          <Icon color={color} sx={{ fontSize: 40 }}>
            {icon}
          </Icon>
        </Box>
      </CardContent>
    </Card>
  );
};
```

### Composant de Navigation
```typescript
// components/layout/Navigation.tsx
import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Divider,
  Typography,
  Box,
} from '@mui/material';
import {
  Dashboard,
  Assignment,
  People,
  ShoppingCart,
  Store,
  Analytics,
  Settings,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

const drawerWidth = 240;

const menuItems = [
  { text: 'Tableau de Bord', icon: <Dashboard />, path: '/dashboard' },
  { text: 'Missions', icon: <Assignment />, path: '/missions' },
  { text: 'Clients', icon: <People />, path: '/clients' },
  { text: 'Commandes', icon: <ShoppingCart />, path: '/commandes' },
  { text: 'Magasins', icon: <Store />, path: '/magasins' },
  { text: 'Analyses', icon: <Analytics />, path: '/analytics' },
  { text: 'Paramètres', icon: <Settings />, path: '/settings' },
];

export const Navigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" noWrap>
          VitaBrosse Pro
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Administration
        </Typography>
      </Box>
      <Divider />
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => navigate(item.path)}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Drawer>
  );
};
```

---

## 📊 Gestion d'État avec Redux

### Store Configuration
```typescript
// store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import authSlice from './slices/authSlice';
import missionSlice from './slices/missionSlice';
import clientSlice from './slices/clientSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    missions: missionSlice,
    clients: clientSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
```

### Auth Slice
```typescript
// store/slices/authSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { User, LoginRequest } from '../../types/auth';
import authService from '../../services/authService';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Async thunks
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: LoginRequest) => {
    const response = await authService.login(credentials);
    return response.user;
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async () => {
    await authService.logout();
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Erreur de connexion';
      })
      .addCase(logout.fulfilled, (state) => {
        state.user = null;
        state.isAuthenticated = false;
      });
  },
});

export const { clearError } = authSlice.actions;
export default authSlice.reducer;
```

---

## 🎨 Thème et Styling

### Configuration du Thème
```typescript
// theme/theme.ts
import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#dc004e',
      light: '#ff5983',
      dark: '#9a0036',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
  },
});
```

---

## 🔄 Hooks Personnalisés

### useApi Hook
```typescript
// hooks/useApi.ts
import { useState, useEffect } from 'react';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export function useApi<T>(
  apiFunction: () => Promise<T>,
  dependencies: any[] = []
): UseApiState<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: true,
    error: null,
  });

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));
        const result = await apiFunction();
        
        if (isMounted) {
          setState({ data: result, loading: false, error: null });
        }
      } catch (error) {
        if (isMounted) {
          setState({
            data: null,
            loading: false,
            error: error instanceof Error ? error.message : 'Une erreur est survenue',
          });
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, dependencies);

  return state;
}
```

### useLocalStorage Hook
```typescript
// hooks/useLocalStorage.ts
import { useState, useEffect } from 'react';

export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T) => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T) => {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}
```

---

## 🚀 Déploiement et Configuration

### Configuration Environnement
```bash
# .env.development
REACT_APP_API_URL=http://localhost:3001/api
REACT_APP_APP_NAME=VitaBrosse Pro Admin
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# .env.production
REACT_APP_API_URL=https://api.vitabrosse.com/api
REACT_APP_APP_NAME=VitaBrosse Pro Admin
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=production
```

### Scripts Package.json
```json
{
  "name": "vitabrosse-admin",
  "version": "1.0.0",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject",
    "lint": "eslint src --ext .ts,.tsx",
    "lint:fix": "eslint src --ext .ts,.tsx --fix",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "@mui/material": "^5.15.0",
    "@mui/icons-material": "^5.15.0",
    "@reduxjs/toolkit": "^2.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "react-redux": "^9.0.0",
    "react-hook-form": "^7.47.0",
    "react-query": "^3.39.0",
    "axios": "^1.6.0",
    "recharts": "^2.8.0",
    "yup": "^1.3.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "typescript": "^4.9.0",
    "eslint": "^8.52.0",
    "@typescript-eslint/eslint-plugin": "^6.10.0",
    "@typescript-eslint/parser": "^6.10.0"
  }
}
```

---

## 🔒 Sécurité

### Bonnes Pratiques
1. **Authentification JWT** avec refresh token
2. **Validation côté client et serveur**
3. **Chiffrement des données sensibles**
4. **HTTPS obligatoire en production**
5. **Gestion des permissions par rôle**
6. **Audit logs pour toutes les actions**

### Exemple de Middleware de Sécurité
```typescript
// middleware/authMiddleware.ts
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

export const authMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ message: 'Token manquant' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ message: 'Token invalide' });
  }
};
```

---

## 📱 Responsive Design

### Breakpoints
```typescript
// theme/breakpoints.ts
export const breakpoints = {
  xs: 0,
  sm: 600,
  md: 960,
  lg: 1280,
  xl: 1920,
};

// Usage dans les composants
const useStyles = makeStyles((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    [theme.breakpoints.up('md')]: {
      flexDirection: 'row',
    },
  },
}));
```

---

## 📊 Monitoring et Analytics

### Service de Monitoring
```typescript
// services/monitoringService.ts
class MonitoringService {
  trackPageView(page: string) {
    // Google Analytics, Mixpanel, etc.
    console.log(`Page view: ${page}`);
  }

  trackEvent(event: string, properties?: Record<string, any>) {
    console.log(`Event: ${event}`, properties);
  }

  trackError(error: Error, context?: string) {
    console.error(`Error in ${context}:`, error);
    // Sentry, LogRocket, etc.
  }
}

export default new MonitoringService();
```

---

## 🚀 Optimisations Performance

### Lazy Loading
```typescript
// App.tsx
import React, { Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import { CircularProgress } from '@mui/material';

// Lazy loading des composants
const Dashboard = React.lazy(() => import('./pages/dashboard/Dashboard'));
const MissionList = React.lazy(() => import('./pages/missions/MissionList'));
const ClientList = React.lazy(() => import('./pages/clients/ClientList'));

export const App: React.FC = () => {
  return (
    <Suspense fallback={<CircularProgress />}>
      <Routes>
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/missions" element={<MissionList />} />
        <Route path="/clients" element={<ClientList />} />
      </Routes>
    </Suspense>
  );
};
```

---

## 📋 API Endpoints

### Endpoints Principaux
```
GET    /api/missions                 - Liste des missions
POST   /api/missions                 - Créer une mission
GET    /api/missions/:id             - Détail d'une mission
PUT    /api/missions/:id             - Modifier une mission
DELETE /api/missions/:id             - Supprimer une mission

GET    /api/clients                  - Liste des clients
POST   /api/clients                  - Créer un client
GET    /api/clients/:id              - Détail d'un client
PUT    /api/clients/:id              - Modifier un client
DELETE /api/clients/:id              - Supprimer un client

GET    /api/commandes                - Liste des commandes
POST   /api/commandes                - Créer une commande
GET    /api/commandes/:id            - Détail d'une commande
PUT    /api/commandes/:id            - Modifier une commande
DELETE /api/commandes/:id            - Supprimer une commande

GET    /api/dashboard/stats          - Statistiques dashboard
GET    /api/analytics/reports        - Rapports d'analyse
```

---

## 🎯 Conclusion

Cette documentation fournit une base solide pour développer l'application admin React pour VitaBrosse Pro. L'architecture proposée est scalable, maintenable et suit les meilleures pratiques de développement React/TypeScript.

### Prochaines Étapes
1. **Setup du projet** avec Create React App + TypeScript
2. **Configuration du routing** et de l'authentification
3. **Implémentation des services API**
4. **Développement des composants UI**
5. **Intégration avec le backend**
6. **Tests et déploiement**

---

*Documentation générée le 16 juillet 2025 pour VitaBrosse Pro*
