import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../providers/commande_provider.dart';
import '../providers/produit_provider.dart';

/// Monthly Revenue Trend Chart
class MonthlyRevenueChart extends StatefulWidget {
  const MonthlyRevenueChart({super.key});

  @override
  State<MonthlyRevenueChart> createState() => _MonthlyRevenueChartState();
}

class _MonthlyRevenueChartState extends State<MonthlyRevenueChart> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  List<FlSpot> spots = [];
  double maxY = 100; // Initialize with a safe default value
  bool isDataLoaded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final commandeProvider = Provider.of<CommandeProvider>(context, listen: false);
    await commandeProvider.chargerCommandes();
    
    final commandes = commandeProvider.commandes;
    Map<int, double> monthlyRevenue = {};
    
    // Initialize last 6 months
    final now = DateTime.now();
    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      monthlyRevenue[month.month] = 0.0;
    }
    
    // Calculate monthly revenue
    for (final commande in commandes) {
      if (commande.statut.name != 'annulee') {
        final month = commande.dateCommande.month;
        if (monthlyRevenue.containsKey(month)) {
          monthlyRevenue[month] = (monthlyRevenue[month] ?? 0.0) + commande.montantTotal;
        }
      }
    }
    
    // Convert to FlSpot list
    spots = monthlyRevenue.entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value / 1000);
    }).toList();
    
    maxY = spots.isEmpty ? 100 : spots.map((spot) => spot.y).reduce((a, b) => a > b ? a : b) * 1.2;
    // Ensure maxY is never 0 to avoid division by zero
    if (maxY <= 0) maxY = 100;
    
    isDataLoaded = true;
    _animationController.forward();
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      child: !isDataLoaded
          ? const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6366F1)),
              ),
            )
          : AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                // Ensure we have safe values for the chart
                final safeMaxY = (maxY > 0 ? maxY : 100.0).toDouble();
                final safeInterval = safeMaxY / 4;
                
                return LineChart(
                  LineChartData(
                    gridData: FlGridData(
                      show: true,
                      drawVerticalLine: false,
                      horizontalInterval: safeInterval,
                      getDrawingHorizontalLine: (value) {
                        return FlLine(
                          color: Colors.grey.shade200,
                          strokeWidth: 1,
                        );
                      },
                    ),
                    titlesData: FlTitlesData(
                      show: true,
                      rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                      topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 30,
                          interval: 1,
                          getTitlesWidget: (double value, TitleMeta meta) {
                            const months = ['', 'Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'];
                            return Text(
                              months[value.toInt()],
                              style: const TextStyle(
                                color: Color(0xFF6B7280),
                                fontWeight: FontWeight.w500,
                                fontSize: 10,
                              ),
                            );
                          },
                        ),
                      ),
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          interval: safeInterval,
                          reservedSize: 40,
                          getTitlesWidget: (double value, TitleMeta meta) {
                            return Text(
                              '${value.toInt()}k',
                              style: const TextStyle(
                                color: Color(0xFF6B7280),
                                fontWeight: FontWeight.w500,
                                fontSize: 10,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    borderData: FlBorderData(show: false),
                    minX: spots.isEmpty ? 1 : spots.first.x,
                    maxX: spots.isEmpty ? 12 : spots.last.x,
                    minY: 0,
                    maxY: safeMaxY,
              lineBarsData: [
                LineChartBarData(
                  spots: spots.map((spot) => FlSpot(spot.x, spot.y * _animation.value)).toList(),
                  isCurved: true,
                  gradient: const LinearGradient(
                    colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                  ),
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(
                    show: true,
                    getDotPainter: (spot, percent, barData, index) {
                      return FlDotCirclePainter(
                        radius: 4,
                        color: Colors.white,
                        strokeWidth: 2,
                        strokeColor: const Color(0xFF6366F1),
                      );
                    },
                  ),
                  belowBarData: BarAreaData(
                    show: true,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0xFF6366F1).withOpacity(0.3),
                        const Color(0xFF6366F1).withOpacity(0.05),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

/// Order Status Distribution Chart
class OrderStatusChart extends StatefulWidget {
  const OrderStatusChart({super.key});

  @override
  State<OrderStatusChart> createState() => _OrderStatusChartState();
}

class _OrderStatusChartState extends State<OrderStatusChart> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  Map<String, int> statusCounts = {};
  int touchedIndex = -1;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final commandeProvider = Provider.of<CommandeProvider>(context, listen: false);
    await commandeProvider.chargerCommandes();
    
    final commandes = commandeProvider.commandes;
    statusCounts = {
      'En attente': 0,
      'Confirmée': 0,
      'Expédiée': 0,
      'Livrée': 0,
      'Annulée': 0,
    };
    
    for (final commande in commandes) {
      final status = _getStatusDisplayName(commande.statut.name);
      statusCounts[status] = (statusCounts[status] ?? 0) + 1;
    }
    
    _animationController.forward();
    if (mounted) setState(() {});
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'en_attente': return 'En attente';
      case 'confirmee': return 'Confirmée';
      case 'expediee': return 'Expédiée';
      case 'livree': return 'Livrée';
      case 'annulee': return 'Annulée';
      default: return 'En attente';
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'En attente': return const Color(0xFFF59E0B);
      case 'Confirmée': return const Color(0xFF3B82F6);
      case 'Expédiée': return const Color(0xFF8B5CF6);
      case 'Livrée': return const Color(0xFF10B981);
      case 'Annulée': return const Color(0xFFEF4444);
      default: return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    final total = statusCounts.values.fold(0, (sum, count) => sum + count);
    if (total == 0) {
      return Container(
        height: 200,
        child: const Center(
          child: Text(
            'Aucune commande disponible',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Row(
            children: [
              Expanded(
                flex: 2,
                child: PieChart(
                  PieChartData(
                    pieTouchData: PieTouchData(
                      touchCallback: (FlTouchEvent event, pieTouchResponse) {
                        setState(() {
                          if (!event.isInterestedForInteractions ||
                              pieTouchResponse == null ||
                              pieTouchResponse.touchedSection == null) {
                            touchedIndex = -1;
                            return;
                          }
                          touchedIndex = pieTouchResponse.touchedSection!.touchedSectionIndex;
                        });
                      },
                    ),
                    borderData: FlBorderData(show: false),
                    sectionsSpace: 2,
                    centerSpaceRadius: 30,
                    sections: statusCounts.entries.where((entry) => entry.value > 0).toList().asMap().entries.map((mapEntry) {
                      final index = mapEntry.key;
                      final entry = mapEntry.value;
                      final isTouched = index == touchedIndex;
                      final radius = isTouched ? 35.0 : 30.0;
                      final percentage = (entry.value / total * 100);
                      
                      return PieChartSectionData(
                        color: _getStatusColor(entry.key),
                        value: entry.value.toDouble() * _animation.value,
                        title: isTouched ? '${percentage.toStringAsFixed(1)}%' : '',
                        radius: radius,
                        titleStyle: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: statusCounts.entries.where((entry) => entry.value > 0).map((entry) {
                    final percentage = (entry.value / total * 100);
                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: _getStatusColor(entry.key),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              entry.key,
                              style: const TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF374151),
                              ),
                            ),
                          ),
                          Text(
                            '${(entry.value * _animation.value).toInt()}',
                            style: const TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F2937),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

/// Top Products Chart
class TopProductsChart extends StatefulWidget {
  const TopProductsChart({super.key});

  @override
  State<TopProductsChart> createState() => _TopProductsChartState();
}

class _TopProductsChartState extends State<TopProductsChart> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  List<Map<String, dynamic>> topProducts = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final commandeProvider = Provider.of<CommandeProvider>(context, listen: false);
    final produitProvider = Provider.of<ProduitProvider>(context, listen: false);
    
    await Future.wait([
      commandeProvider.chargerCommandes(),
      produitProvider.chargerProduits(),
    ]);
    
    final commandes = commandeProvider.commandes;
    final produits = produitProvider.produits;
    
    Map<String, int> productSales = {};
    Map<String, String> productNames = {};
    
    // Initialize product names
    for (final produit in produits) {
      if (produit.id != null) {
        productNames[produit.id!] = produit.nom;
        productSales[produit.id!] = 0;
      }
    }
    
    // Count sales
    for (final commande in commandes) {
      if (commande.statut.name != 'annulee') {
        for (final item in commande.items) {
          final productId = item.produitId;
          if (productSales.containsKey(productId)) {
            productSales[productId] = (productSales[productId] ?? 0) + item.quantite;
          }
        }
      }
    }
    
    // Get top 5 products
    final sortedProducts = productSales.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    topProducts = sortedProducts.take(5).map((entry) => {
      'name': productNames[entry.key] ?? 'Produit inconnu',
      'sales': entry.value,
    }).toList();
    
    _animationController.forward();
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (topProducts.isEmpty) {
      return Container(
        height: 200,
        child: const Center(
          child: Text(
            'Aucune donnée de vente disponible',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    final maxSales = topProducts.first['sales'] as int;
    
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Column(
            children: topProducts.asMap().entries.map((entry) {
              final index = entry.key;
              final product = entry.value;
              final sales = product['sales'] as int;
              final name = product['name'] as String;
              final percentage = sales / maxSales;
              
              final colors = [
                const Color(0xFF6366F1),
                const Color(0xFF8B5CF6),
                const Color(0xFF10B981),
                const Color(0xFFF59E0B),
                const Color(0xFFEF4444),
              ];
              
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            name,
                            style: const TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF374151),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          '${(sales * _animation.value).toInt()} ventes',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: colors[index % colors.length],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Container(
                      height: 6,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: percentage * _animation.value,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                colors[index % colors.length],
                                colors[index % colors.length].withOpacity(0.7),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          );
        },
      ),
    );
  }
}
