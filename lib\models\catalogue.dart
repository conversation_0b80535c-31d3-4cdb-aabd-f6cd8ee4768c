import 'package:cloud_firestore/cloud_firestore.dart';

enum TypeCatalogue {
  produits,
  promotions,
  nouveautes,
  documentation,
  formation,
}

class Catalogue {
  final String id;
  final String nom;
  final String description;
  final TypeCatalogue type;
  final String? urlPdf;
  final String? cheminLocal;
  final String? imagePreview;
  final DateTime dateCreation;
  final DateTime? dateModification;
  final bool isActive;
  final Map<String, dynamic>? metadata;

  Catalogue({
    required this.id,
    required this.nom,
    required this.description,
    required this.type,
    this.urlPdf,
    this.cheminLocal,
    this.imagePreview,
    required this.dateCreation,
    this.dateModification,
    this.isActive = true,
    this.metadata,
  });

  factory Catalogue.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Catalogue(
      id: doc.id,
      nom: data['nom'] ?? '',
      description: data['description'] ?? '',
      type: TypeCatalogue.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => TypeCatalogue.produits,
      ),
      urlPdf: data['urlPdf'],
      cheminLocal: data['cheminLocal'],
      imagePreview: data['imagePreview'],
      dateCreation: (data['dateCreation'] as Timestamp).toDate(),
      dateModification:
          data['dateModification'] != null
              ? (data['dateModification'] as Timestamp).toDate()
              : null,
      isActive: data['isActive'] ?? true,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'nom': nom,
      'description': description,
      'type': type.name,
      'urlPdf': urlPdf,
      'cheminLocal': cheminLocal,
      'imagePreview': imagePreview,
      'dateCreation': Timestamp.fromDate(dateCreation),
      'dateModification':
          dateModification != null
              ? Timestamp.fromDate(dateModification!)
              : null,
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  Catalogue copyWith({
    String? id,
    String? nom,
    String? description,
    TypeCatalogue? type,
    String? urlPdf,
    String? cheminLocal,
    String? imagePreview,
    DateTime? dateCreation,
    DateTime? dateModification,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return Catalogue(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      description: description ?? this.description,
      type: type ?? this.type,
      urlPdf: urlPdf ?? this.urlPdf,
      cheminLocal: cheminLocal ?? this.cheminLocal,
      imagePreview: imagePreview ?? this.imagePreview,
      dateCreation: dateCreation ?? this.dateCreation,
      dateModification: dateModification ?? this.dateModification,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  String get typeLabel {
    switch (type) {
      case TypeCatalogue.produits:
        return 'Catalogue Produits';
      case TypeCatalogue.promotions:
        return 'Catalogue Promotions';
      case TypeCatalogue.nouveautes:
        return 'Catalogue Nouveautés';
      case TypeCatalogue.documentation:
        return 'Documentation';
      case TypeCatalogue.formation:
        return 'Formation';
    }
  }

  String get typeIcon {
    switch (type) {
      case TypeCatalogue.produits:
        return '📦';
      case TypeCatalogue.promotions:
        return '🎯';
      case TypeCatalogue.nouveautes:
        return '✨';
      case TypeCatalogue.documentation:
        return '📚';
      case TypeCatalogue.formation:
        return '🎓';
    }
  }
}
