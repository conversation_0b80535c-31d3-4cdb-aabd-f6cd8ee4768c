import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/produit_provider.dart';
import '../../models/produit.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../../widgets/professional_ui_components.dart';
import 'produit_form_screen.dart';
import 'produit_detail_screen.dart';

class ProduitsScreen extends StatefulWidget {
  const ProduitsScreen({super.key});

  @override
  State<ProduitsScreen> createState() => _ProduitsScreenState();
}

class _ProduitsScreenState extends State<ProduitsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'Toutes';

  @override
  void initState() {
    super.initState();
    // Load products and categories when the screen is first opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final provider = context.read<ProduitProvider>();
        provider.chargerProduits();
        provider.chargerCategories();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          // Reset filters and reload data
          setState(() {
            _selectedCategory = 'Toutes';
            _searchController.clear();
          });
          final provider = context.read<ProduitProvider>();
          await provider.chargerProduits();
          await provider.chargerCategories();
        },
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: isSmallScreen ? 120 : 140,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              elevation: 0,
              scrolledUnderElevation: 2,

              flexibleSpace: FlexibleSpaceBar(
                titlePadding: EdgeInsets.only(left: padding, bottom: 16),
                title: Row(
                  children: [
                    const VitaBrosseLogo(height: 28, showText: false),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Produits',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: const Color(0xFF1F2937),
                              fontSize: isSmallScreen ? 18 : 22,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'produits VitaBrosse',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Colors.grey.shade600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        const Color(0xFF10B981).withValues(alpha: 0.05),
                        const Color(0xFF059669).withValues(alpha: 0.05),
                      ],
                      stops: const [0.0, 0.7, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: isSmallScreen ? 55 : 30,
                        right: isSmallScreen ? 10 : 15,
                        child: Container(
                          width: isSmallScreen ? 60 : 80,
                          height: isSmallScreen ? 60 : 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF10B981).withValues(alpha: 0.1),
                                const Color(0xFF059669).withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                          child: Center(
                            child: Icon(
                              Icons.inventory_2_outlined,
                              size: isSmallScreen ? 24 : 32,
                              color: const Color(0xFF10B981),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.fromLTRB(padding, 16, padding, 8),
                child: Column(
                  children: [
                    // Barre de recherche optimisée
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(
                          isSmallScreen ? 12 : 16,
                        ),
                        border: Border.all(color: Colors.grey.shade200),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Rechercher un produit...',
                          hintStyle: TextStyle(
                            color: Colors.grey.shade500,
                            fontSize: isSmallScreen ? 14 : 16,
                          ),
                          prefixIcon: Icon(
                            Icons.search,
                            color: Colors.grey.shade400,
                            size: isSmallScreen ? 20 : 24,
                          ),
                          suffixIcon:
                              _searchController.text.isNotEmpty
                                  ? IconButton(
                                    icon: Icon(
                                      Icons.clear,
                                      color: Colors.grey.shade400,
                                      size: isSmallScreen ? 20 : 24,
                                    ),
                                    onPressed: () {
                                      _searchController.clear();
                                      setState(() {
                                        _selectedCategory = 'Toutes';
                                      });
                                      context
                                          .read<ProduitProvider>()
                                          .chargerProduits();
                                    },
                                  )
                                  : null,
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: isSmallScreen ? 16 : 20,
                            vertical: isSmallScreen ? 12 : 16,
                          ),
                        ),
                        onChanged: (value) {
                          setState(
                            () {},
                          ); // Update the UI to show/hide clear button
                          if (value.isEmpty) {
                            // If search is empty, apply current category filter
                            final provider = context.read<ProduitProvider>();
                            if (_selectedCategory == 'Toutes') {
                              provider.chargerProduits();
                            } else {
                              provider.filtrerParCategorie(_selectedCategory);
                            }
                          } else {
                            context.read<ProduitProvider>().rechercherProduits(
                              value,
                            );
                          }
                        },
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 12 : 16),
                    // Filtre par catégorie adaptatif
                    Consumer<ProduitProvider>(
                      builder: (context, provider, child) {
                        final categories = ['Toutes', ...provider.categories];
                        return SizedBox(
                          height: isSmallScreen ? 36 : 40,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: categories.length,
                            itemBuilder: (context, index) {
                              final category = categories[index];
                              final isSelected = category == _selectedCategory;
                              return Padding(
                                padding: EdgeInsets.only(
                                  right: isSmallScreen ? 6 : 8,
                                ),
                                child: FilterChip(
                                  label: Text(
                                    category,
                                    style: TextStyle(
                                      fontSize: isSmallScreen ? 12 : 14,
                                    ),
                                  ),
                                  selected: isSelected,
                                  onSelected: (selected) {
                                    setState(() {
                                      _selectedCategory = category;
                                      // Clear search when selecting a category
                                      if (_searchController.text.isNotEmpty) {
                                        _searchController.clear();
                                      }
                                    });
                                    provider.filtrerParCategorie(category);
                                  },
                                  padding: EdgeInsets.symmetric(
                                    horizontal: isSmallScreen ? 8 : 12,
                                    vertical: isSmallScreen ? 2 : 4,
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            // Liste des produits
            Consumer<ProduitProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(strokeWidth: 3),
                          SizedBox(height: 16),
                          Text(
                            'Chargement des produits...',
                            style: TextStyle(color: Colors.grey, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                if (provider.error != null) {
                  return SliverFillRemaining(
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.all(padding),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: EdgeInsets.all(
                                  isSmallScreen ? 16 : 20,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Icon(
                                  Icons.error_outline,
                                  size: isSmallScreen ? 40 : 48,
                                  color: Colors.red.shade400,
                                ),
                              ),
                              SizedBox(height: isSmallScreen ? 16 : 24),
                              Text(
                                'Oups ! Une erreur est survenue',
                                style: Theme.of(
                                  context,
                                ).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF1F2937),
                                  fontSize: isSmallScreen ? 18 : 20,
                                ),
                              ),
                              SizedBox(height: isSmallScreen ? 6 : 8),
                              Text(
                                provider.error!,
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: isSmallScreen ? 14 : 16,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: isSmallScreen ? 16 : 24),
                              FilledButton.icon(
                                onPressed: () {
                                  provider.effacerErreur();
                                  provider.chargerProduits();
                                },
                                icon: const Icon(Icons.refresh),
                                label: const Text('Réessayer'),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }
                if (provider.produits.isEmpty) {
                  return SliverFillRemaining(
                    child: ModernEmptyState(
                      icon: Icons.inventory_2_outlined,
                      title: 'Aucun produit trouvé',
                      subtitle: 'Commencez par ajouter votre premier produit',
                      actionText: 'Ajouter un produit',
                      onAction: () => _naviguerVersFormulaire(context),
                    ),
                  );
                }

                return SliverPadding(
                  padding: EdgeInsets.fromLTRB(padding, 8, padding, 20),
                  sliver: SliverGrid(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: isSmallScreen ? 1 : 2,
                      childAspectRatio: isSmallScreen ? 2.2 : 1.4,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                    ),
                    delegate: SliverChildBuilderDelegate((context, index) {
                      final produit = provider.produits[index];
                      return _buildModernProductCard(
                        context,
                        produit,
                        isSmallScreen,
                      );
                    }, childCount: provider.produits.length),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _naviguerVersFormulaire(context),
        backgroundColor: const Color(0xFF10B981),
        foregroundColor: Colors.white,
        elevation: 6,
        heroTag: "add_product",
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildModernProductCard(
    BuildContext context,
    Produit produit,
    bool isSmallScreen,
  ) {
    return ProfessionalCard(
      onTap: () => _naviguerVersDetail(context, produit),
      child:
          isSmallScreen
              ? _buildMobileLayout(produit)
              : _buildTabletLayout(produit),
    );
  }

  Widget _buildMobileLayout(Produit produit) {
    final isAvailable = produit.estDisponible;

    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // Icon container
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors:
                    isAvailable
                        ? [
                          const Color(0xFF10B981).withValues(alpha: 0.15),
                          const Color(0xFF059669).withValues(alpha: 0.1),
                        ]
                        : [Colors.red.shade100, Colors.red.shade50],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              isAvailable ? Icons.inventory_2_rounded : Icons.warning_rounded,
              color:
                  isAvailable ? const Color(0xFF10B981) : Colors.red.shade600,
              size: 22,
            ),
          ),
          const SizedBox(width: 12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Product name and status row
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        produit.nom,
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF1F2937),
                          height: 1.2,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 3,
                      ),
                      decoration: BoxDecoration(
                        color:
                            isAvailable
                                ? const Color(0xFF10B981)
                                : Colors.red.shade500,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        isAvailable ? 'Dispo' : 'Rupture',
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),

                // Code and price row
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        produit.code,
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      produit.prixFormate,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w700,
                        color:
                            isAvailable
                                ? const Color(0xFF10B981)
                                : Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),

                // Stock info
                Row(
                  children: [
                    Icon(
                      Icons.inventory_outlined,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Stock: ${produit.stock}',
                      style: TextStyle(
                        fontSize: 12,
                        color:
                            produit.stock > 10
                                ? const Color(0xFF10B981)
                                : produit.stock > 0
                                ? Colors.orange.shade700
                                : Colors.red.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 14,
                      color: Colors.grey.shade400,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabletLayout(Produit produit) {
    final isAvailable = produit.estDisponible;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and status
          Row(
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors:
                        isAvailable
                            ? [
                              const Color(0xFF10B981).withValues(alpha: 0.15),
                              const Color(0xFF059669).withValues(alpha: 0.1),
                            ]
                            : [Colors.red.shade100, Colors.red.shade50],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  isAvailable
                      ? Icons.inventory_2_rounded
                      : Icons.warning_rounded,
                  color:
                      isAvailable
                          ? const Color(0xFF10B981)
                          : Colors.red.shade600,
                  size: 22,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 5,
                ),
                decoration: BoxDecoration(
                  color:
                      isAvailable
                          ? const Color(0xFF10B981)
                          : Colors.red.shade500,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  isAvailable ? 'Dispo' : 'Rupture',
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Product name
          Text(
            produit.nom,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1F2937),
              height: 1.3,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 6),

          // Product code
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              produit.code,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const Spacer(),

          // Price and stock row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Price
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Prix',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    produit.prixFormate,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color:
                          isAvailable
                              ? const Color(0xFF10B981)
                              : Colors.grey.shade500,
                    ),
                  ),
                ],
              ),

              // Stock
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Stock',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color:
                          produit.stock > 10
                              ? const Color(0xFF10B981).withValues(alpha: 0.15)
                              : produit.stock > 0
                              ? Colors.orange.withValues(alpha: 0.15)
                              : Colors.red.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      '${produit.stock}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color:
                            produit.stock > 10
                                ? const Color(0xFF10B981)
                                : produit.stock > 0
                                ? Colors.orange.shade700
                                : Colors.red.shade600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _naviguerVersFormulaire(BuildContext context, {Produit? produit}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProduitFormScreen(produit: produit),
      ),
    );
  }

  void _naviguerVersDetail(BuildContext context, Produit produit) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProduitDetailScreen(produit: produit),
      ),
    );
  }
}
