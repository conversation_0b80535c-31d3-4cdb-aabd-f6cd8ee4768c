import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/catalogue.dart';
import '../../services/catalogue_service.dart';

class CataloguePDFViewer extends StatefulWidget {
  final Catalogue catalogue;

  const CataloguePDFViewer({Key? key, required this.catalogue})
    : super(key: key);

  @override
  State<CataloguePDFViewer> createState() => _CataloguePDFViewerState();
}

class _CataloguePDFViewerState extends State<CataloguePDFViewer> {
  bool _isLoading = true;
  String? _error;
  String? _localPath;

  @override
  void initState() {
    super.initState();
    _preparerPDF();
  }

  Future<void> _preparerPDF() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      print('DEBUG: Catalogue nom: ${widget.catalogue.nom}');
      print('DEBUG: Catalogue urlPdf: ${widget.catalogue.urlPdf}');
      print('DEBUG: Catalogue cheminLocal: ${widget.catalogue.cheminLocal}');

      // Vérifier si le PDF est disponible localement
      if (widget.catalogue.cheminLocal != null) {
        print(
          'DEBUG: Vérification du chemin local: ${widget.catalogue.cheminLocal}',
        );
        final isAvailable = await CatalogueService.isPDFAvailableLocally(
          widget.catalogue.cheminLocal!,
        );
        print('DEBUG: PDF disponible localement: $isAvailable');
        if (isAvailable) {
          _localPath = widget.catalogue.cheminLocal;
        }
      }

      // Si pas disponible localement, essayer de le télécharger
      if (_localPath == null && widget.catalogue.urlPdf != null) {
        print(
          'DEBUG: Tentative de téléchargement depuis: ${widget.catalogue.urlPdf}',
        );
        _localPath = await CatalogueService.telechargerPDF(
          widget.catalogue.urlPdf!,
          widget.catalogue.nom,
        );
        print('DEBUG: Chemin local après téléchargement: $_localPath');
      }

      if (_localPath == null && widget.catalogue.urlPdf == null) {
        print('DEBUG: Aucun PDF disponible');
        setState(() {
          _error =
              'Aucun PDF disponible pour ce catalogue.\n\nDétails:\n- URL PDF: ${widget.catalogue.urlPdf ?? "Non définie"}\n- Chemin local: ${widget.catalogue.cheminLocal ?? "Non défini"}';
          _isLoading = false;
        });
        return;
      }

      print('DEBUG: PDF préparé avec succès. Chemin: $_localPath');
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('DEBUG: Erreur lors de la préparation du PDF: $e');
      setState(() {
        _error = 'Erreur lors du chargement du PDF: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.catalogue.nom,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF1F2937),
        elevation: 0,
        surfaceTintColor: Colors.white,
        actions: [
          // Bouton d'ouverture externe
          IconButton(
            icon: const Icon(Icons.open_in_new),
            onPressed: _ouvrirPDFExterne,
            tooltip: 'Ouvrir dans une application externe',
          ),
          // Menu d'options
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'info':
                  _afficherInfos();
                  break;
                case 'telecharger':
                  _telechargerPDF();
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'info',
                    child: Row(
                      children: [
                        Icon(Icons.info),
                        SizedBox(width: 12),
                        Text('Informations'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'telecharger',
                    child: Row(
                      children: [
                        Icon(Icons.download),
                        SizedBox(width: 12),
                        Text('Télécharger'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Chargement du catalogue...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _preparerPDF,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icône du catalogue
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: const Color(0xFF10B981).withOpacity(0.1),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.picture_as_pdf,
              size: 50,
              color: Color(0xFF10B981),
            ),
          ),
          const SizedBox(height: 24),

          // Nom du catalogue
          Text(
            widget.catalogue.nom,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: Color(0xFF1F2937),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          // Type du catalogue
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF10B981).withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              widget.catalogue.typeLabel,
              style: const TextStyle(
                color: Color(0xFF10B981),
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Description
          Text(
            widget.catalogue.description,
            style: const TextStyle(fontSize: 16, color: Color(0xFF6B7280)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),

          // Boutons d'action
          Column(
            children: [
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _ouvrirPDFExterne,
                  icon: const Icon(Icons.open_in_new),
                  label: const Text('Ouvrir le catalogue'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF10B981),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _telechargerPDF,
                  icon: const Icon(Icons.download),
                  label: const Text('Télécharger'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF10B981),
                    side: const BorderSide(color: Color(0xFF10B981)),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _ouvrirPDFExterne() async {
    try {
      print('DEBUG: _ouvrirPDFExterne appelé');
      print('DEBUG: _localPath: $_localPath');
      print('DEBUG: widget.catalogue.urlPdf: ${widget.catalogue.urlPdf}');

      if (_localPath != null) {
        // Pour les fichiers locaux, utiliser l'URL web directement pour éviter FileUriExposedException
        if (widget.catalogue.urlPdf != null) {
          print(
            'DEBUG: Utilisation de l\'URL web pour éviter FileUriExposedException',
          );
          final uri = Uri.parse(widget.catalogue.urlPdf!);
          print('DEBUG: URI web parsé: $uri');

          final canLaunch = await canLaunchUrl(uri);
          print('DEBUG: canLaunchUrl result: $canLaunch');

          if (canLaunch) {
            print('DEBUG: Tentative d\'ouverture du PDF web...');
            await launchUrl(uri, mode: LaunchMode.externalApplication);
            print('DEBUG: PDF ouvert avec succès');
          } else {
            throw 'Impossible d\'ouvrir le PDF web. URI: $uri';
          }
        } else {
          throw 'Fichier local disponible mais pas d\'URL web pour l\'ouvrir';
        }
      } else if (widget.catalogue.urlPdf != null) {
        final uri = Uri.parse(widget.catalogue.urlPdf!);
        print('DEBUG: Utilisation de l\'URL PDF: $uri');

        final canLaunch = await canLaunchUrl(uri);
        print('DEBUG: canLaunchUrl result: $canLaunch');

        if (canLaunch) {
          print('DEBUG: Tentative d\'ouverture du PDF...');
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          print('DEBUG: PDF ouvert avec succès');
        } else {
          throw 'Impossible d\'ouvrir le PDF. URI: $uri';
        }
      } else {
        throw 'Aucune URL disponible pour ouvrir le PDF';
      }
    } catch (e) {
      print('DEBUG: Erreur lors de l\'ouverture du PDF: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Erreur lors de l\'ouverture: ${e.toString().contains('FileUriExposedException') ? 'Problème de sécurité Android - utilisation de l\'URL web' : e}',
            ),
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _telechargerPDF() async {
    if (widget.catalogue.urlPdf != null) {
      try {
        final uri = Uri.parse(widget.catalogue.urlPdf!);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du téléchargement: $e')),
        );
      }
    }
  }

  void _afficherInfos() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Informations du catalogue'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow('Nom', widget.catalogue.nom),
                _buildInfoRow('Type', widget.catalogue.typeLabel),
                _buildInfoRow('Description', widget.catalogue.description),
                _buildInfoRow(
                  'Date de création',
                  widget.catalogue.dateCreation.toString().substring(0, 10),
                ),
                if (widget.catalogue.dateModification != null)
                  _buildInfoRow(
                    'Dernière modification',
                    widget.catalogue.dateModification.toString().substring(
                      0,
                      10,
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
