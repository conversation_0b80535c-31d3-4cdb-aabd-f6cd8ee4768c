import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/mission_provider.dart';
import '../../models/mission.dart';
import '../../providers/auth_provider.dart';
import 'creer_mission_calendrier_screen.dart';
import 'missions_date_screen.dart';

class CalendrierMissionsScreen extends StatefulWidget {
  @override
  _CalendrierMissionsScreenState createState() =>
      _CalendrierMissionsScreenState();
}

class _CalendrierMissionsScreenState extends State<CalendrierMissionsScreen> {
  DateTime _selectedDate = DateTime.now();
  DateTime _currentMonth = DateTime.now();

  @override
  void initState() {
    super.initState();
    _currentMonth = DateTime(_selectedDate.year, _selectedDate.month, 1);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerMissions();
    });
  }

  void _chargerMissions() {
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // Commercial users see all missions, merchandisers see only their missions
    if (authProvider.userType == 'commercial') {
      missionProvider.chargerMissions(); // Load all missions
    } else {
      // For merchandiser users, load only their missions
      // TODO: Get actual merchandiser ID from auth provider
      final merchandiserId = 'merchandiser_123';
      missionProvider.chargerMissionsParMerchandiser(merchandiserId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Calendrier des missions',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _chargerMissions,
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: Consumer<MissionProvider>(
        builder: (context, missionProvider, child) {
          if (missionProvider.isLoading) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Chargement des missions...'),
                ],
              ),
            );
          }

          if (missionProvider.error != null) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
                    const SizedBox(height: 16),
                    Text(
                      'Erreur de chargement',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      missionProvider.error!,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton.icon(
                      onPressed: _chargerMissions,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Réessayer'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          final missions = missionProvider.missions;

          return RefreshIndicator(
            onRefresh: () async => _chargerMissions(),
            child: Column(
              children: [
                // En-tête avec navigation du calendrier
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  child: Column(
                    children: [
                      // Main navigation row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Previous month button
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey[200]!),
                            ),
                            child: IconButton(
                              onPressed: () => _changerMois(-1),
                              icon: const Icon(Icons.chevron_left, size: 20),
                              style: IconButton.styleFrom(
                                foregroundColor: Colors.grey[700],
                                padding: const EdgeInsets.all(8),
                              ),
                            ),
                          ),

                          // Month/Year and mission count
                          Expanded(
                            child: Column(
                              children: [
                                Text(
                                  _getMonthYearString(_currentMonth),
                                  style: const TextStyle(
                                    fontSize: 22,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF1E293B),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(
                                      0xFF10B981,
                                    ).withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${_getMissionsForMonth().length} missions ce mois',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF10B981),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Next month button
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey[200]!),
                            ),
                            child: IconButton(
                              onPressed: () => _changerMois(1),
                              icon: const Icon(Icons.chevron_right, size: 20),
                              style: IconButton.styleFrom(
                                foregroundColor: Colors.grey[700],
                                padding: const EdgeInsets.all(8),
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // Today button and quick actions
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Today button
                          TextButton.icon(
                            onPressed: _allerAujourdhui,
                            icon: const Icon(Icons.today, size: 16),
                            label: const Text('Aujourd\'hui'),
                            style: TextButton.styleFrom(
                              foregroundColor: const Color(0xFF10B981),
                              backgroundColor: const Color(
                                0xFF10B981,
                              ).withValues(alpha: 0.1),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                          ),

                          const SizedBox(width: 12),

                          // Quick create mission button (for commercials only)
                          Consumer<AuthProvider>(
                            builder: (context, authProvider, child) {
                              if (authProvider.userType == 'commercial') {
                                return TextButton.icon(
                                  onPressed: () =>
                                      _creerNouvelleMission(context),
                                  icon: const Icon(Icons.add, size: 16),
                                  label: const Text('Nouvelle'),
                                  style: TextButton.styleFrom(
                                    foregroundColor: const Color(0xFF3B82F6),
                                    backgroundColor: const Color(
                                      0xFF3B82F6,
                                    ).withValues(alpha: 0.1),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                  ),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Jours de la semaine
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 8,
                  ),
                  child: Row(
                    children: ['L', 'M', 'M', 'J', 'V', 'S', 'D'].map((day) {
                      return Expanded(
                        child: Center(
                          child: Text(
                            day,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[700],
                              fontSize: 12,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),

                // Professional Calendar Grid - Use remaining space
                Expanded(
                  child: Container(
                    color: Colors.white,
                    child: _buildCalendarGrid(missions),
                  ),
                ),
              ],
            ),
          );
        },
      ),
      // Show floating action button only for commercial users
      floatingActionButton: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (authProvider.userType == 'commercial') {
            return FloatingActionButton.extended(
              onPressed: () => _creerNouvelleMission(context),
              icon: const Icon(Icons.add),
              label: const Text('Nouvelle mission'),
              backgroundColor: const Color(0xFF10B981),
              foregroundColor: Colors.white,
              heroTag: "add_mission",
            );
          }
          return const SizedBox.shrink(); // Hide for merchandisers
        },
      ),
    );
  }

  Widget _buildCalendar(List<Mission> missions) {
    final firstDayOfMonth = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      1,
    );
    final startDate = firstDayOfMonth.subtract(
      Duration(days: firstDayOfMonth.weekday - 1),
    );

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: 42, // 6 semaines * 7 jours
        itemBuilder: (context, index) {
          final date = startDate.add(Duration(days: index));
          final isCurrentMonth = date.month == _selectedDate.month;
          final isToday = _isSameDay(date, DateTime.now());
          final isSelected = _isSameDay(date, _selectedDate);

          // Trouver les missions pour cette date
          final missionsJour = missions
              .where((mission) => _isSameDay(mission.dateEcheance, date))
              .toList();

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDate = date;
              });
              // Navigate to missions screen for the selected date
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MissionsDateScreen(selectedDate: date),
                ),
              );
            },
            onLongPress: isCurrentMonth
                ? () {
                    // Only allow mission creation for commercial users
                    final authProvider = Provider.of<AuthProvider>(
                      context,
                      listen: false,
                    );
                    if (authProvider.userType == 'commercial') {
                      _creerNouvelleMission(context, date);
                    }
                  }
                : null,
            child: Container(
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.blue
                    : isToday
                        ? Colors.blue.shade50
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isCurrentMonth
                      ? (isSelected ? Colors.blue : Colors.grey.shade200)
                      : Colors.transparent,
                  width: isSelected ? 2 : 1,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: Colors.blue.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Stack(
                children: [
                  Center(
                    child: Text(
                      date.day.toString(),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: isToday || isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                        color: isSelected
                            ? Colors.white
                            : isCurrentMonth
                                ? (isToday ? Colors.blue : Colors.black87)
                                : Colors.grey.shade400,
                      ),
                    ),
                  ),
                  if (missionsJour.isNotEmpty)
                    Positioned(
                      right: 6,
                      top: 6,
                      child: Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: _getMissionDotColor(missionsJour),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  if (missionsJour.length > 1)
                    Positioned(
                      right: 14,
                      top: 6,
                      child: Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: _getMissionDotColor(
                            missionsJour,
                          ).withOpacity(0.7),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Helper methods for status and priority
  String _getStatutText(String statut) {
    switch (statut) {
      case 'en_attente':
        return 'En attente';
      case 'en_cours':
        return 'En cours';
      case 'terminee':
        return 'Terminée';
      case 'annulee':
        return 'Annulée';
      default:
        return statut;
    }
  }

  String _getPrioriteText(String priorite) {
    switch (priorite) {
      case 'urgente':
        return 'URGENT';
      case 'haute':
        return 'HAUTE';
      case 'normale':
        return 'NORMALE';
      case 'basse':
        return 'BASSE';
      default:
        return priorite.toUpperCase();
    }
  }

  // Mission management methods
  void _modifierMission(Mission mission) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => CreerMissionCalendrierScreen(
          selectedDate: mission.dateEcheance,
          missionToEdit: mission,
        ),
      ),
    );

    if (result == true) {
      _chargerMissions();
    }
  }

  void _supprimerMission(Mission mission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer la mission'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer la mission "${mission.titre}" ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _confirmerSuppressionMission(mission);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmerSuppressionMission(Mission mission) async {
    try {
      final missionProvider = Provider.of<MissionProvider>(
        context,
        listen: false,
      );

      final success = await missionProvider.supprimerMission(mission.id);

      if (mounted) {
        try {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text('Mission "${mission.titre}" supprimée avec succès'),
                backgroundColor: Colors.green,
              ),
            );
            _chargerMissions();
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Erreur lors de la suppression de la mission'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } catch (scaffoldError) {
          // If ScaffoldMessenger fails, just reload missions
          _chargerMissions();
        }
      }
    } catch (e) {
      if (mounted) {
        try {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la suppression: $e'),
              backgroundColor: Colors.red,
            ),
          );
        } catch (scaffoldError) {
          // If ScaffoldMessenger fails, silently continue
        }
      }
    }
  }

  // Helper methods for new calendar structure
  List<Mission> _getMissionsForMonth() {
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );
    return missionProvider.missions.where((mission) {
      return mission.dateEcheance.year == _currentMonth.year &&
          mission.dateEcheance.month == _currentMonth.month;
    }).toList();
  }

  Widget _buildCalendarGrid(List<Mission> missions) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Days of week header
          Container(
            height: 40,
            child: Row(
              children: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim']
                  .map(
                    (day) => Expanded(
                      child: Center(
                        child: Text(
                          day,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
          ),
          const SizedBox(height: 8),
          // Calendar grid
          Expanded(
            child: GridView.builder(
              padding: EdgeInsets.zero,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                childAspectRatio: 1.0, // Make boxes more square
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: 42, // 6 weeks * 7 days
              itemBuilder: (context, index) {
                final firstDayOfMonth = DateTime(
                  _currentMonth.year,
                  _currentMonth.month,
                  1,
                );
                final firstDayWeekday = firstDayOfMonth.weekday;
                final daysInMonth = DateTime(
                  _currentMonth.year,
                  _currentMonth.month + 1,
                  0,
                ).day;

                // Calculate the date for this cell
                final dayNumber = index - firstDayWeekday + 2;
                final isCurrentMonth =
                    dayNumber > 0 && dayNumber <= daysInMonth;
                final date = isCurrentMonth
                    ? DateTime(
                        _currentMonth.year,
                        _currentMonth.month,
                        dayNumber,
                      )
                    : DateTime(
                        _currentMonth.year,
                        _currentMonth.month,
                        dayNumber,
                      );

                final isSelected =
                    isCurrentMonth && _isSameDay(date, _selectedDate);
                final isToday =
                    isCurrentMonth && _isSameDay(date, DateTime.now());

                // Missions for this day
                final missionsJour = isCurrentMonth
                    ? missions
                        .where(
                          (mission) => _isSameDay(mission.dateEcheance, date),
                        )
                        .toList()
                    : <Mission>[];

                return GestureDetector(
                  onTap: isCurrentMonth
                      ? () {
                          setState(() {
                            _selectedDate = date;
                          });
                          // Navigate to missions screen for the selected date
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  MissionsDateScreen(selectedDate: date),
                            ),
                          );
                        }
                      : null,
                  onLongPress: isCurrentMonth
                      ? () {
                          // Only allow mission creation for commercial users
                          final authProvider = Provider.of<AuthProvider>(
                            context,
                            listen: false,
                          );
                          if (authProvider.userType == 'commercial') {
                            _creerNouvelleMission(context, date);
                          }
                        }
                      : null,
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected
                          ? const Color(0xFF3B82F6).withValues(alpha: 0.1)
                          : isToday
                              ? const Color(0xFF10B981).withValues(alpha: 0.1)
                              : Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                      border: isSelected
                          ? Border.all(
                              color: const Color(0xFF3B82F6),
                              width: 2,
                            )
                          : isToday
                              ? Border.all(
                                  color: const Color(0xFF10B981),
                                  width: 2,
                                )
                              : Border.all(color: Colors.grey[200]!, width: 1),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: const Color(
                                  0xFF3B82F6,
                                ).withValues(alpha: 0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: Stack(
                      children: [
                        // Day number
                        Center(
                          child: Text(
                            isCurrentMonth ? dayNumber.toString() : '',
                            style: TextStyle(
                              fontSize: 18, // Increased font size
                              fontWeight: isSelected || isToday
                                  ? FontWeight.bold
                                  : FontWeight
                                      .w600, // Made default weight bolder
                              color: isCurrentMonth
                                  ? (isSelected
                                      ? const Color(0xFF3B82F6)
                                      : isToday
                                          ? const Color(0xFF10B981)
                                          : const Color(
                                              0xFF1F2937,
                                            )) // Darker default color
                                  : Colors.transparent,
                            ),
                          ),
                        ),

                        // Mission indicators
                        if (missionsJour.isNotEmpty)
                          Positioned(
                            bottom: 6,
                            left: 0,
                            right: 0,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: 8, // Bigger indicator
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: _getMissionDotColor(missionsJour),
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: _getMissionDotColor(
                                          missionsJour,
                                        ).withValues(alpha: 0.3),
                                        blurRadius: 2,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                ),
                                if (missionsJour.length > 1) ...[
                                  const SizedBox(width: 3),
                                  Container(
                                    width: 6,
                                    height: 6,
                                    decoration: BoxDecoration(
                                      color: _getMissionDotColor(
                                        missionsJour,
                                      ).withValues(alpha: 0.7),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                ],
                                if (missionsJour.length > 2) ...[
                                  const SizedBox(width: 2),
                                  Container(
                                    width: 4,
                                    height: 4,
                                    decoration: BoxDecoration(
                                      color: _getMissionDotColor(
                                        missionsJour,
                                      ).withValues(alpha: 0.5),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                ],
                                if (missionsJour.length > 2) ...[
                                  const SizedBox(width: 2),
                                  Container(
                                    width: 4,
                                    height: 4,
                                    decoration: BoxDecoration(
                                      color: _getMissionDotColor(
                                        missionsJour,
                                      ).withValues(alpha: 0.4),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),

                        // Mission count badge for many missions
                        if (missionsJour.length > 3)
                          Positioned(
                            top: 2,
                            right: 2,
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: _getMissionDotColor(missionsJour),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 16,
                                minHeight: 16,
                              ),
                              child: Text(
                                '${missionsJour.length}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, size: 20),
        style: IconButton.styleFrom(
          foregroundColor: Colors.grey[700],
          padding: const EdgeInsets.all(8),
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return TextButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: TextButton.styleFrom(
        foregroundColor: color,
        backgroundColor: color.withValues(alpha: 0.1),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      ),
    );
  }

  // Enhanced calendar navigation
  void _changerMois(int delta) {
    setState(() {
      _currentMonth = DateTime(
        _currentMonth.year,
        _currentMonth.month + delta,
        1,
      );
      // Don't change selected date, just the month view
    });
  }

  void _allerAujourdhui() {
    final today = DateTime.now();
    setState(() {
      _selectedDate = today;
      _currentMonth = DateTime(today.year, today.month, 1);
    });
  }

  // Create new mission (for commercial users only)
  void _creerNouvelleMission(BuildContext context, [DateTime? date]) async {
    final selectedDate = date ?? _selectedDate;

    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) =>
            CreerMissionCalendrierScreen(selectedDate: selectedDate),
      ),
    );

    if (result == true) {
      // Reload missions if a new mission was created
      _chargerMissions();
    }
  }

  void _afficherDetailsMission(Mission mission) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          mission.titre,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatutColor(
                            mission.statut,
                          ).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          mission.statutAffichage,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: _getStatutColor(mission.statut),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    mission.description,
                    style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                  ),
                  const SizedBox(height: 20),
                  _buildDetailRow('Client', mission.clientNom, Icons.store),
                  _buildDetailRow(
                    'Priorité',
                    mission.priorite,
                    Icons.priority_high,
                  ),
                  _buildDetailRow(
                    'Date d\'échéance',
                    '${mission.dateEcheance.day}/${mission.dateEcheance.month}/${mission.dateEcheance.year}',
                    Icons.calendar_today,
                  ),
                  if (mission.taches.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Tâches à accomplir',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...mission.taches.map(
                      (tache) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 8),
                            Expanded(child: Text(tache)),
                          ],
                        ),
                      ),
                    ),
                  ],
                  if (mission.notes != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Notes',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      mission.notes!,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          Expanded(
            child: Text(value, style: TextStyle(color: Colors.grey[600])),
          ),
        ],
      ),
    );
  }

  Color _getPrioriteColor(String priorite) {
    switch (priorite) {
      case 'urgente':
        return Colors.red;
      case 'haute':
        return Colors.orange;
      case 'normale':
        return Colors.blue;
      case 'faible':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  String _getMonthYearString(DateTime date) {
    const months = [
      'Janvier',
      'Février',
      'Mars',
      'Avril',
      'Mai',
      'Juin',
      'Juillet',
      'Août',
      'Septembre',
      'Octobre',
      'Novembre',
      'Décembre',
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  Color _getMissionDotColor(List<Mission> missions) {
    if (missions.any((m) => m.estEnRetard)) {
      return Colors.red;
    } else if (missions.any((m) => m.priorite == 'urgente')) {
      return Colors.orange;
    } else if (missions.any((m) => m.statut == 'en_cours')) {
      return Colors.blue;
    } else if (missions.any((m) => m.statut == 'terminee')) {
      return Colors.green;
    }
    return Colors.grey;
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'en_attente':
        return Colors.orange;
      case 'en_cours':
        return Colors.blue;
      case 'terminee':
        return Colors.green;
      case 'annulee':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
