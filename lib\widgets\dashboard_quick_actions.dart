import 'package:flutter/material.dart';
import '../screens/clients/clients_screen.dart';
import '../screens/produits/produits_screen.dart';
import '../screens/commandes/commandes_screen.dart';
import '../screens/devis/devis_screen.dart';
import 'professional_ui_components.dart';

class DashboardQuickActions extends StatelessWidget {
  const DashboardQuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    final actions = [
      _QuickActionData(
        title: 'Nouveau Client',
        subtitle: 'Ajouter un client',
        icon: Icons.person_add_rounded,
        color: const Color(0xFF3B82F6),
        onTap: () => _navigateToClients(context),
      ),
      _QuickActionData(
        title: 'Nouvelle Commande',
        subtitle: 'Créer une commande',
        icon: Icons.add_shopping_cart_rounded,
        color: const Color(0xFF10B981),
        onTap: () => _navigateToCommandes(context),
      ),
      _QuickActionData(
        title: 'Nouveau Devis',
        subtitle: 'Générer un devis',
        icon: Icons.description_rounded,
        color: const Color(0xFFF59E0B),
        onTap: () => _navigateToDevis(context),
      ),
      _QuickActionData(
        title: 'Gérer Stock',
        subtitle: 'Produits & inventaire',
        icon: Icons.inventory_rounded,
        color: const Color(0xFF8B5CF6),
        onTap: () => _navigateToProduits(context),
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(
          title: 'Actions Rapides',
          subtitle: 'Accès direct aux fonctionnalités principales',
        ),
        const SizedBox(height: 16),
        isSmallScreen
            ? _buildScrollableActions(actions)
            : _buildGridActions(actions, screenWidth),
      ],
    );
  }

  Widget _buildScrollableActions(List<_QuickActionData> actions) {
    return SizedBox(
      height: 100,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 4),
        itemCount: actions.length,
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          return SizedBox(
            width: 140,
            child: _QuickActionCard(data: actions[index]),
          );
        },
      ),
    );
  }

  Widget _buildGridActions(List<_QuickActionData> actions, double screenWidth) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: screenWidth > 800 ? 4 : 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: screenWidth > 800 ? 2.5 : 2.2,
      children: actions.map((action) => _QuickActionCard(data: action)).toList(),
    );
  }

  void _navigateToClients(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => const ClientsScreen()),
    );
  }

  void _navigateToCommandes(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => const CommandesScreen()),
    );
  }

  void _navigateToDevis(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => const DevisScreen()),
    );
  }

  void _navigateToProduits(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => const ProduitsScreen()),
    );
  }
}

class _QuickActionData {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const _QuickActionData({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}

class _QuickActionCard extends StatefulWidget {
  final _QuickActionData data;

  const _QuickActionCard({required this.data});

  @override
  State<_QuickActionCard> createState() => _QuickActionCardState();
}

class _QuickActionCardState extends State<_QuickActionCard> with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
    _elevationAnimation = Tween<double>(
      begin: 0.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _elevationAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white,
                  widget.data.color.withOpacity( 0.02),
                ],
              ),
              border: Border.all(
                color: widget.data.color.withOpacity( 0.1),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: widget.data.color.withOpacity( 0.1),
                  blurRadius: 10 + _elevationAnimation.value,
                  offset: Offset(0, 4 + _elevationAnimation.value / 2),
                ),
                BoxShadow(
                  color: Colors.black.withOpacity( 0.05),
                  blurRadius: 5 + _elevationAnimation.value / 2,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: widget.data.onTap,
                onHover: (hovered) {
                  setState(() {
                    _isHovered = hovered;
                  });
                  if (hovered) {
                    _hoverController.forward();
                  } else {
                    _hoverController.reverse();
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: widget.data.color.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: _isHovered
                                  ? [
                                      BoxShadow(
                                        color: widget.data.color.withOpacity(0.3),
                                        blurRadius: 6,
                                        offset: const Offset(0, 2),
                                      ),
                                    ]
                                  : null,
                            ),
                            child: Icon(
                              widget.data.icon,
                              size: 20,
                              color: widget.data.color,
                            ),
                          ),
                          const Spacer(),
                          Icon(
                            Icons.arrow_forward_ios_rounded,
                            size: 14,
                            color: widget.data.color.withOpacity(0.6),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Flexible(
                        child: Text(
                          widget.data.title,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF1F2937),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 1),
                      Flexible(
                        child: Text(
                          widget.data.subtitle,
                          style: const TextStyle(
                            fontSize: 10,
                            color: Color(0xFF6B7280),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
