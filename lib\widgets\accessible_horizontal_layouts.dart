import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';

/// Accessible horizontal layout widgets with proper semantic labels and focus management
class AccessibleHorizontalLayouts {
  /// Creates an accessible horizontal statistics row with proper semantics
  static Widget buildAccessibleStatsRow({
    required List<AccessibleStatItem> stats,
    required bool isSmallScreen,
    String? semanticLabel,
  }) {
    final statsWidget =
        isSmallScreen
            ? _buildHorizontalScrollingStats(stats)
            : _buildHorizontalGridStats(stats);

    return Semantics(
      label: semanticLabel ?? 'Statistiques des rapports',
      child: statsWidget,
    );
  }

  static Widget _buildHorizontalScrollingStats(List<AccessibleStatItem> stats) {
    return SizedBox(
      height: 120,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: stats.length,
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          return SizedBox(
            width: 140,
            child: _buildAccessibleStatCard(stats[index], index),
          );
        },
      ),
    );
  }

  static Widget _buildHorizontalGridStats(List<AccessibleStatItem> stats) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children:
            stats.asMap().entries.map((entry) {
              final index = entry.key;
              final stat = entry.value;
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: _buildAccessibleStatCard(stat, index),
                ),
              );
            }).toList(),
      ),
    );
  }

  static Widget _buildAccessibleStatCard(AccessibleStatItem stat, int index) {
    return Semantics(
      label:
          '${stat.label}: ${stat.value}${stat.subtitle != null ? ', ${stat.subtitle}' : ''}',
      value: stat.value,
      sortKey: OrdinalSortKey(index.toDouble()),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: stat.color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: stat.color.withValues(alpha: 0.2)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              stat.icon,
              color: stat.color,
              size: 24,
              semanticLabel: stat.iconSemanticLabel,
            ),
            const SizedBox(height: 8),
            Text(
              stat.value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: stat.color,
              ),
              semanticsLabel: 'Valeur: ${stat.value}',
            ),
            const SizedBox(height: 4),
            Text(
              stat.label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            if (stat.subtitle != null) ...[
              const SizedBox(height: 2),
              Text(
                stat.subtitle!,
                style: TextStyle(fontSize: 10, color: Colors.grey.shade500),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Creates an accessible horizontal card with proper focus management
  static Widget buildAccessibleHorizontalCard({
    required Widget leftContent,
    required Widget rightContent,
    required bool isSmallScreen,
    int leftFlex = 3,
    int rightFlex = 2,
    EdgeInsets? padding,
    VoidCallback? onTap,
    String? semanticLabel,
    String? semanticHint,
    bool excludeSemantics = false,
  }) {
    final cardPadding = padding ?? const EdgeInsets.all(16);

    Widget cardContent;
    if (isSmallScreen) {
      cardContent = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [leftContent, const SizedBox(height: 12), rightContent],
      );
    } else {
      cardContent = IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(flex: leftFlex, child: leftContent),
            const SizedBox(width: 16),
            Expanded(flex: rightFlex, child: rightContent),
          ],
        ),
      );
    }

    return _AccessibleCard(
      onTap: onTap,
      semanticLabel: semanticLabel,
      semanticHint: semanticHint,
      excludeSemantics: excludeSemantics,
      child: Padding(padding: cardPadding, child: cardContent),
    );
  }

  /// Creates an accessible responsive form row with proper focus order
  static Widget buildAccessibleFormRow({
    required List<AccessibleFormField> fields,
    required bool isSmallScreen,
    List<int>? flexValues,
    double spacing = 16,
    String? groupSemanticLabel,
  }) {
    Widget formContent;

    if (isSmallScreen) {
      formContent = Column(
        children:
            fields.asMap().entries.map((entry) {
              final index = entry.key;
              final field = entry.value;
              return Column(
                children: [
                  _wrapFieldWithSemantics(field, index),
                  if (index < fields.length - 1) SizedBox(height: spacing),
                ],
              );
            }).toList(),
      );
    } else {
      final widgets = <Widget>[];
      for (int i = 0; i < fields.length; i++) {
        final field = fields[i];
        final flex = flexValues?[i] ?? 1;

        widgets.add(
          Expanded(flex: flex, child: _wrapFieldWithSemantics(field, i)),
        );

        if (i < fields.length - 1) {
          widgets.add(SizedBox(width: spacing));
        }
      }

      formContent = Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widgets,
      );
    }

    if (groupSemanticLabel != null) {
      return Semantics(label: groupSemanticLabel, child: formContent);
    }

    return formContent;
  }

  static Widget _wrapFieldWithSemantics(AccessibleFormField field, int index) {
    return Semantics(
      label: field.semanticLabel,
      hint: field.semanticHint,
      textField: field.isTextField,
      sortKey: OrdinalSortKey(index.toDouble()),
      child: field.widget,
    );
  }
}

/// Data model for accessible statistics items
class AccessibleStatItem {
  final String label;
  final String value;
  final IconData icon;
  final Color color;
  final String? subtitle;
  final String? iconSemanticLabel;

  const AccessibleStatItem({
    required this.label,
    required this.value,
    required this.icon,
    required this.color,
    this.subtitle,
    this.iconSemanticLabel,
  });
}

/// Data model for accessible form fields
class AccessibleFormField {
  final Widget widget;
  final String semanticLabel;
  final String? semanticHint;
  final bool isTextField;

  const AccessibleFormField({
    required this.widget,
    required this.semanticLabel,
    this.semanticHint,
    this.isTextField = false,
  });
}

/// Accessible card widget with proper focus management
class _AccessibleCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final String? semanticLabel;
  final String? semanticHint;
  final bool excludeSemantics;

  const _AccessibleCard({
    required this.child,
    this.onTap,
    this.semanticLabel,
    this.semanticHint,
    this.excludeSemantics = false,
  });

  @override
  State<_AccessibleCard> createState() => _AccessibleCardState();
}

class _AccessibleCardState extends State<_AccessibleCard> {
  bool _isFocused = false;
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final card = Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        elevation: (_isFocused || _isHovered) ? 4.0 : 2.0,
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(12),
          onFocusChange: (focused) {
            setState(() => _isFocused = focused);
          },
          child: MouseRegion(
            onEnter: (_) => setState(() => _isHovered = true),
            onExit: (_) => setState(() => _isHovered = false),
            child: Container(
              decoration:
                  _isFocused
                      ? BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).primaryColor,
                          width: 2,
                        ),
                      )
                      : null,
              child: widget.child,
            ),
          ),
        ),
      ),
    );

    if (widget.excludeSemantics) {
      return ExcludeSemantics(child: card);
    }

    if (widget.onTap != null) {
      return Semantics(
        button: true,
        label: widget.semanticLabel,
        hint: widget.semanticHint ?? 'Appuyez pour plus de détails',
        onTap: widget.onTap,
        child: card,
      );
    }

    return Semantics(label: widget.semanticLabel, child: card);
  }
}

/// Focus management utilities for horizontal layouts
class HorizontalLayoutFocusManager {
  /// Creates a focus traversal policy for horizontal layouts
  static FocusTraversalPolicy createHorizontalTraversalPolicy() {
    return OrderedTraversalPolicy();
  }

  /// Wraps a widget with proper focus order for horizontal layouts
  static Widget wrapWithFocusOrder({
    required Widget child,
    required double order,
    FocusNode? focusNode,
  }) {
    return FocusTraversalOrder(
      order: NumericFocusOrder(order),
      child:
          focusNode != null ? Focus(focusNode: focusNode, child: child) : child,
    );
  }

  /// Creates semantic announcements for layout changes
  static void announceLayoutChange(BuildContext context, String message) {
    SemanticsService.announce(message, TextDirection.ltr);
  }
}

/// Accessibility testing utilities
class AccessibilityTestingUtils {
  /// Validates that all interactive elements have proper semantic labels
  static bool validateSemanticLabels(Widget widget) {
    // This would be implemented with widget testing
    // For now, return true as a placeholder
    return true;
  }

  /// Checks focus traversal order in horizontal layouts
  static bool validateFocusTraversal(Widget widget) {
    // This would be implemented with widget testing
    // For now, return true as a placeholder
    return true;
  }

  /// Verifies screen reader compatibility
  static bool validateScreenReaderCompatibility(Widget widget) {
    // This would be implemented with accessibility testing tools
    // For now, return true as a placeholder
    return true;
  }
}
