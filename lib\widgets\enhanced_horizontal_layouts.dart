import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Enhanced horizontal layout widgets with professional animations and interactions
class EnhancedHorizontalLayouts {
  
  /// Creates an animated horizontal statistics row with staggered animations
  static Widget buildAnimatedStatsRow({
    required List<StatisticItem> stats,
    required bool isSmallScreen,
    Duration animationDuration = const Duration(milliseconds: 800),
  }) {
    return isSmallScreen 
      ? _buildHorizontalScrollingStats(stats, animationDuration)
      : _buildHorizontalGridStats(stats, animationDuration);
  }

  static Widget _buildHorizontalScrollingStats(
    List<StatisticItem> stats, 
    Duration duration,
  ) {
    return SizedBox(
      height: 120,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: stats.length,
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          return AnimatedStatCard(
            stat: stats[index],
            delay: Duration(milliseconds: index * 150),
            width: 140,
          );
        },
      ),
    );
  }

  static Widget _buildHorizontalGridStats(
    List<StatisticItem> stats, 
    Duration duration,
  ) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: stats.asMap().entries.map((entry) {
          final index = entry.key;
          final stat = entry.value;
          return Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: index == 0 ? 0 : 6),
              child: AnimatedStatCard(
                stat: stat,
                delay: Duration(milliseconds: index * 100),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Creates an enhanced horizontal form layout with smooth transitions
  static Widget buildResponsiveFormRow({
    required List<Widget> fields,
    required bool isSmallScreen,
    List<int>? flexValues,
    double spacing = 16,
  }) {
    if (isSmallScreen) {
      return Column(
        children: fields.asMap().entries.map((entry) {
          final index = entry.key;
          final field = entry.value;
          return Column(
            children: [
              AnimatedSlideIn(
                delay: Duration(milliseconds: index * 100),
                child: field,
              ),
              if (index < fields.length - 1) SizedBox(height: spacing),
            ],
          );
        }).toList(),
      );
    }

    return AnimatedSlideIn(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: fields.asMap().entries.map((entry) {
          final index = entry.key;
          final field = entry.value;
          final flex = flexValues?[index] ?? 1;
          
          return Expanded(
            flex: flex,
            child: field,
          );
        }).expand((widget) => [
          widget,
          if (fields.indexOf(widget) < fields.length - 1) 
            SizedBox(width: spacing),
        ]).toList()..removeLast(),
      ),
    );
  }

  /// Creates an enhanced horizontal card layout with hover effects
  static Widget buildHorizontalCard({
    required Widget leftContent,
    required Widget rightContent,
    required bool isSmallScreen,
    int leftFlex = 3,
    int rightFlex = 2,
    EdgeInsets? padding,
    VoidCallback? onTap,
  }) {
    final cardPadding = padding ?? const EdgeInsets.all(16);
    
    if (isSmallScreen) {
      return EnhancedCard(
        onTap: onTap,
        child: Padding(
          padding: cardPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [leftContent, const SizedBox(height: 12), rightContent],
          ),
        ),
      );
    }

    return EnhancedCard(
      onTap: onTap,
      child: Padding(
        padding: cardPadding,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(flex: leftFlex, child: leftContent),
            const SizedBox(width: 16),
            Expanded(flex: rightFlex, child: rightContent),
          ],
        ),
      ),
    );
  }
}

/// Data model for statistics items
class StatisticItem {
  final String label;
  final String value;
  final IconData icon;
  final Color color;
  final String? subtitle;

  const StatisticItem({
    required this.label,
    required this.value,
    required this.icon,
    required this.color,
    this.subtitle,
  });
}

/// Animated statistics card with professional animations
class AnimatedStatCard extends StatefulWidget {
  final StatisticItem stat;
  final Duration delay;
  final double? width;

  const AnimatedStatCard({
    super.key,
    required this.stat,
    this.delay = Duration.zero,
    this.width,
  });

  @override
  State<AnimatedStatCard> createState() => _AnimatedStatCardState();
}

class _AnimatedStatCardState extends State<AnimatedStatCard>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _hoverAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _hoverAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));

    // Start animation with delay
    Future.delayed(widget.delay, () {
      if (mounted) _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _fadeAnimation, _hoverAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value * _hoverAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: MouseRegion(
              onEnter: (_) => _hoverController.forward(),
              onExit: (_) => _hoverController.reverse(),
              child: Container(
                width: widget.width,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: widget.stat.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: widget.stat.color.withValues(alpha: 0.2),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: widget.stat.color.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      widget.stat.icon,
                      color: widget.stat.color,
                      size: 24,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.stat.value,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: widget.stat.color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.stat.label,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (widget.stat.subtitle != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        widget.stat.subtitle!,
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey.shade500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Enhanced card with hover effects and animations
class EnhancedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsets? margin;

  const EnhancedCard({
    super.key,
    required this.child,
    this.onTap,
    this.margin,
  });

  @override
  State<EnhancedCard> createState() => _EnhancedCardState();
}

class _EnhancedCardState extends State<EnhancedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _elevationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _elevationAnimation = Tween<double>(
      begin: 2.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: widget.margin ?? const EdgeInsets.only(bottom: 12),
            child: Material(
              elevation: _elevationAnimation.value,
              borderRadius: BorderRadius.circular(12),
              color: Colors.white,
              child: InkWell(
                onTap: () {
                  if (widget.onTap != null) {
                    HapticFeedback.lightImpact();
                    widget.onTap!();
                  }
                },
                onTapDown: (_) => _controller.forward(),
                onTapUp: (_) => _controller.reverse(),
                onTapCancel: () => _controller.reverse(),
                borderRadius: BorderRadius.circular(12),
                child: MouseRegion(
                  onEnter: (_) => _controller.forward(),
                  onExit: (_) => _controller.reverse(),
                  child: widget.child,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Animated slide-in widget for form fields
class AnimatedSlideIn extends StatefulWidget {
  final Widget child;
  final Duration delay;
  final Duration duration;

  const AnimatedSlideIn({
    super.key,
    required this.child,
    this.delay = Duration.zero,
    this.duration = const Duration(milliseconds: 500),
  });

  @override
  State<AnimatedSlideIn> createState() => _AnimatedSlideInState();
}

class _AnimatedSlideInState extends State<AnimatedSlideIn>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    Future.delayed(widget.delay, () {
      if (mounted) _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: widget.child,
          ),
        );
      },
    );
  }
}
