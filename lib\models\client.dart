import 'package:cloud_firestore/cloud_firestore.dart';

class Client {
  final String? id; // ID Firebase (String)
  final String? nom; // Optional - for backward compatibility
  final String? prenom; // Optional - for backward compatibility
  final String? nomClient; // Client name (replaces nom + prenom)
  final String email;
  final String? telephone; // Optional - for backward compatibility
  final String? tel; // Primary phone field
  final String? portable; // Mobile phone
  final String? fax; // Fax number
  final String adresse;
  final DateTime? dateCreation; // Optional - for backward compatibility
  final DateTime? createdAt; // Primary creation timestamp
  final DateTime? updatedAt; // Update timestamp
  final String? codeClient;
  final String? matriculeFiscale; // Tax ID field
  final String? categorie;
  final String? modeReglement;
  final String? company; // Company name
  final String? status; // Client status (active/inactive)

  Client({
    this.id,
    this.nom,
    this.prenom,
    this.nomClient,
    required this.email,
    this.telephone,
    this.tel,
    this.portable,
    this.fax,
    required this.adresse,
    this.dateCreation,
    this.createdAt,
    this.updatedAt,
    this.codeClient,
    this.matriculeFiscale,
    this.categorie,
    this.modeReglement,
    this.company,
    this.status,
  });

  // Convertir un Client en Map pour Firebase
  Map<String, dynamic> toFirestore() {
    final Map<String, dynamic> data = {'email': email, 'adresse': adresse};

    // Add optional fields only if they exist
    if (nom != null) data['nom'] = nom;
    if (prenom != null) data['prenom'] = prenom;
    if (nomClient != null) data['nomClient'] = nomClient;
    if (telephone != null) data['telephone'] = telephone;
    if (tel != null) data['tel'] = tel;
    if (portable != null) data['portable'] = portable;
    if (fax != null) data['fax'] = fax;
    if (company != null) data['company'] = company;
    if (status != null) data['status'] = status;
    if (codeClient != null) data['codeClient'] = codeClient;
    if (matriculeFiscale != null) data['matriculeFiscale'] = matriculeFiscale;
    if (categorie != null) data['categorie'] = categorie;
    if (modeReglement != null) data['modeReglement'] = modeReglement;

    // Handle timestamps
    if (dateCreation != null)
      data['dateCreation'] = Timestamp.fromDate(dateCreation!);
    if (createdAt != null) data['createdAt'] = Timestamp.fromDate(createdAt!);
    if (updatedAt != null) data['updatedAt'] = Timestamp.fromDate(updatedAt!);

    return data;
  }

  // Créer un Client à partir d'un document Firebase
  factory Client.fromFirestore(String docId, Map<String, dynamic> data) {
    return Client(
      id: docId,
      nom: data['nom'],
      prenom: data['prenom'],
      nomClient: data['nomClient'],
      email: data['email'] ?? '',
      telephone: data['telephone'],
      tel: data['tel'],
      portable: data['portable'],
      fax: data['fax'],
      adresse: data['adresse'] ?? '',
      dateCreation:
          data['dateCreation'] is Timestamp
              ? (data['dateCreation'] as Timestamp).toDate()
              : null,
      createdAt:
          data['createdAt'] is Timestamp
              ? (data['createdAt'] as Timestamp).toDate()
              : null,
      updatedAt:
          data['updatedAt'] is Timestamp
              ? (data['updatedAt'] as Timestamp).toDate()
              : null,
      codeClient: data['codeClient'],
      matriculeFiscale: data['matriculeFiscale'],
      categorie: data['categorie'],
      modeReglement: data['modeReglement'],
      company: data['company'],
      status: data['status'],
    );
  }

  // Convertir un Client en Map pour affichage ou stockage local
  Map<String, dynamic> toMap() {
    final Map<String, dynamic> data = {'email': email, 'adresse': adresse};

    // Add optional fields only if they exist
    if (id != null) data['id'] = id;
    if (nom != null) data['nom'] = nom;
    if (prenom != null) data['prenom'] = prenom;
    if (nomClient != null) data['nomClient'] = nomClient;
    if (telephone != null) data['telephone'] = telephone;
    if (tel != null) data['tel'] = tel;
    if (portable != null) data['portable'] = portable;
    if (fax != null) data['fax'] = fax;
    if (company != null) data['company'] = company;
    if (status != null) data['status'] = status;
    if (codeClient != null) data['codeClient'] = codeClient;
    if (matriculeFiscale != null) data['matriculeFiscale'] = matriculeFiscale;
    if (categorie != null) data['categorie'] = categorie;
    if (modeReglement != null) data['modeReglement'] = modeReglement;

    // Handle timestamps
    if (dateCreation != null)
      data['dateCreation'] = dateCreation!.toIso8601String();
    if (createdAt != null) data['createdAt'] = createdAt!.toIso8601String();
    if (updatedAt != null) data['updatedAt'] = updatedAt!.toIso8601String();

    return data;
  }

  // Créer un Client à partir d'un Map
  factory Client.fromMap(Map<String, dynamic> map) {
    return Client(
      id: map['id']?.toString(),
      nom: map['nom'],
      prenom: map['prenom'],
      nomClient: map['nomClient'],
      email: map['email'] ?? '',
      telephone: map['telephone'],
      tel: map['tel'],
      portable: map['portable'],
      fax: map['fax'],
      adresse: map['adresse'] ?? '',
      dateCreation:
          map['dateCreation'] is String
              ? DateTime.parse(map['dateCreation'])
              : null,
      createdAt:
          map['createdAt'] is String ? DateTime.parse(map['createdAt']) : null,
      updatedAt:
          map['updatedAt'] is String ? DateTime.parse(map['updatedAt']) : null,
      codeClient: map['codeClient'],
      matriculeFiscale: map['matriculeFiscale'],
      categorie: map['categorie'],
      modeReglement: map['modeReglement'],
      company: map['company'],
      status: map['status'],
    );
  }

  // Méthode copyWith pour créer une copie modifiée
  Client copyWith({
    String? id,
    String? nom,
    String? prenom,
    String? nomClient,
    String? email,
    String? telephone,
    String? tel,
    String? portable,
    String? fax,
    String? adresse,
    DateTime? dateCreation,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? codeClient,
    String? matriculeFiscale,
    String? categorie,
    String? modeReglement,
    String? company,
    String? status,
  }) {
    return Client(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      nomClient: nomClient ?? this.nomClient,
      email: email ?? this.email,
      telephone: telephone ?? this.telephone,
      tel: tel ?? this.tel,
      portable: portable ?? this.portable,
      fax: fax ?? this.fax,
      adresse: adresse ?? this.adresse,
      dateCreation: dateCreation ?? this.dateCreation,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      codeClient: codeClient ?? this.codeClient,
      matriculeFiscale: matriculeFiscale ?? this.matriculeFiscale,
      categorie: categorie ?? this.categorie,
      modeReglement: modeReglement ?? this.modeReglement,
      company: company ?? this.company,
      status: status ?? this.status,
    );
  }

  // Getters pour compatibilité et facilité d'utilisation
  String get nomComplet {
    if (nomClient != null && nomClient!.isNotEmpty) {
      return nomClient!;
    }
    if (nom != null && prenom != null) {
      return '$prenom $nom';
    }
    return nomClient ?? nom ?? prenom ?? 'Client';
  }

  String get primaryPhone {
    return tel ?? telephone ?? portable ?? '';
  }

  DateTime get primaryCreationDate {
    return createdAt ?? dateCreation ?? DateTime.now();
  }

  String get primaryMatriculeFiscale {
    return matriculeFiscale ?? '';
  }

  @override
  String toString() {
    return 'Client{id: $id, nom: $nom, prenom: $prenom, email: $email}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Client && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
