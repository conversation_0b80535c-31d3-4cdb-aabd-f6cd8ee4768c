import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/devis_provider.dart';
import '../../providers/firebase_client_provider.dart';
import '../../providers/produit_provider.dart';
import '../../models/devis.dart';
import '../../models/produit.dart';
import '../../models/client.dart';
import '../../models/devis_item.dart';
import '../../widgets/professional_ui_components.dart';
import '../../widgets/vitabrosse_logo.dart';

class NouveauDevisScreen extends StatefulWidget {
  final Devis? devisAModifier;

  const NouveauDevisScreen({super.key, this.devisAModifier});

  @override
  State<NouveauDevisScreen> createState() => _NouveauDevisScreenState();
}

class _NouveauDevisScreenState extends State<NouveauDevisScreen> {
  final TextEditingController _conditionsController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  final TextEditingController _remiseController = TextEditingController();
  final TextEditingController _tvaController = TextEditingController(
    text: '19',
  );
  final TextEditingController _clientSearchController = TextEditingController();
  final TextEditingController _productSearchController =
      TextEditingController();

  List<DevisItem> _items = [];
  Client? _clientSelectionne;
  DateTime _dateExpiration = DateTime.now().add(const Duration(days: 30));
  bool _isLoading = false;

  // Client search state
  List<Client> _clientsFiltres = [];
  bool _showClientDropdown = false;
  final FocusNode _clientSearchFocus = FocusNode();

  // Product search state
  bool _isSearchingProduct = false;

  @override
  void initState() {
    super.initState();
    _loadData();
    _initializeEditMode();

    // Add focus listener to hide dropdown when focus is lost
    _clientSearchFocus.addListener(() {
      if (!_clientSearchFocus.hasFocus) {
        setState(() {
          _showClientDropdown = false;
        });
      }
    });
  }

  void _initializeEditMode() {
    if (widget.devisAModifier != null) {
      final devis = widget.devisAModifier!;
      // Note: We'll need to fetch the client by ID
      _dateExpiration = devis.dateExpiration;
      _items = List.from(devis.items);
      _conditionsController.text = devis.conditionsValidite;
      _notesController.text = devis.notes ?? '';
      _contactController.text = devis.contactCommercial ?? '';
      _remiseController.text = devis.remisePourcentage.toString();
      _tvaController.text = devis.tauxTva.toString();

      // Load client by ID
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadClientById(devis.clientId);
      });
    }
  }

  Future<void> _loadData() async {
    try {
      final clientProvider = context.read<FirebaseClientProvider>();
      final produitProvider = context.read<ProduitProvider>();

      await Future.wait([
        clientProvider.chargerClients(),
        if (produitProvider.produits.isEmpty) produitProvider.chargerProduits(),
      ]);
    } catch (e) {
      debugPrint('Error loading data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement des données: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadClientById(String clientId) async {
    final clientProvider = context.read<FirebaseClientProvider>();
    final client = clientProvider.clients.firstWhere(
      (c) => c.id == clientId,
      orElse: () => throw Exception('Client not found'),
    );
    setState(() {
      _clientSelectionne = client;
    });
  }

  @override
  void dispose() {
    _conditionsController.dispose();
    _notesController.dispose();
    _contactController.dispose();
    _remiseController.dispose();
    _tvaController.dispose();
    _clientSearchController.dispose();
    _productSearchController.dispose();
    _clientSearchFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 768;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 1,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: const Color(0xFF1F2937)),
              onPressed: () => Navigator.of(context).pop(),
            ),
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(
                left: isSmallScreen ? 56.0 : 60.0, // Add space for back button
                right: isSmallScreen ? 16.0 : 20.0,
                bottom: 16,
              ),
              title: Row(
                children: [
                  const VitaBrosseLogo(height: 28, showText: false),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          widget.devisAModifier != null
                              ? 'Modifier Devis'
                              : 'Nouveau Devis',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 22,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          widget.devisAModifier != null
                              ? 'Modifier la proposition'
                              : 'nouvelle proposition',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF3B82F6).withValues(alpha: 0.05),
                      const Color(0xFF1D4ED8).withValues(alpha: 0.05),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (isSmallScreen) ...[
                    _buildClientSection(isSmallScreen),
                    SizedBox(height: 16),
                    _buildDevisInfo(isSmallScreen),
                    SizedBox(height: 16),
                    _buildArticlesSection(isSmallScreen),
                    SizedBox(height: 16),
                    _buildFinancialSettings(isSmallScreen),
                    SizedBox(height: 16),
                    _buildNotesSection(isSmallScreen),
                  ] else ...[
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 2,
                          child: Column(
                            children: [
                              _buildClientSection(isSmallScreen),
                              SizedBox(height: 24),
                              _buildDevisInfo(isSmallScreen),
                            ],
                          ),
                        ),
                        SizedBox(width: 24),
                        Expanded(
                          flex: 3,
                          child: Column(
                            children: [
                              _buildArticlesSection(isSmallScreen),
                              SizedBox(height: 24),
                              _buildFinancialSettings(isSmallScreen),
                              SizedBox(height: 24),
                              _buildNotesSection(isSmallScreen),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                  SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: PrimaryActionButton(
                      text: widget.devisAModifier != null
                          ? 'Modifier le devis'
                          : 'Enregistrer le devis',
                      icon: widget.devisAModifier != null
                          ? Icons.edit
                          : Icons.save,
                      onPressed: _enregistrerDevis,
                      isLoading: _isLoading,
                      isFullWidth: true,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientSection(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informations Client',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          SizedBox(height: 16),
          Consumer<FirebaseClientProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Client search field
                  TextField(
                    controller: _clientSearchController,
                    focusNode: _clientSearchFocus,
                    decoration: InputDecoration(
                      labelText: 'Rechercher un client',
                      hintText: 'Tapez le nom du client...',
                      prefixIcon: Icon(
                        Icons.search,
                        color: const Color(0xFF3B82F6),
                      ),
                      suffixIcon: _clientSelectionne != null
                          ? IconButton(
                              icon: Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _clientSelectionne = null;
                                  _clientSearchController.clear();
                                  _clientsFiltres.clear();
                                  _showClientDropdown = false;
                                });
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onChanged: _rechercherClients,
                    readOnly: _clientSelectionne != null,
                  ),
                  // Dropdown overlay for search results
                  if (_showClientDropdown)
                    Container(
                      margin: const EdgeInsets.only(top: 2),
                      constraints: BoxConstraints(maxHeight: 200),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: _clientsFiltres.isEmpty
                          ? Padding(
                              padding: const EdgeInsets.all(12),
                              child: Text(
                                'Aucun client trouvé',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontStyle: FontStyle.italic,
                                  fontSize: 14,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              padding: EdgeInsets.zero,
                              itemCount: _clientsFiltres.length,
                              itemBuilder: (context, index) {
                                final client = _clientsFiltres[index];
                                return ListTile(
                                  dense: true,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 4,
                                  ),
                                  title: Text(
                                    client.nomComplet,
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                  subtitle: Text(
                                    client.email,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                  onTap: () {
                                    setState(() {
                                      _clientSelectionne = client;
                                      _clientSearchController.text =
                                          client.nomComplet;
                                      _showClientDropdown = false;
                                      _clientsFiltres.clear();
                                    });
                                    _clientSearchFocus.unfocus();
                                  },
                                );
                              },
                            ),
                    ),
                  if (_clientSelectionne != null) ...[
                    SizedBox(height: 16),
                    Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF3B82F6).withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFF3B82F6).withValues(alpha: 0.2),
                        ),
                      ),
                      child: Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: const Color(0xFF3B82F6),
                            child: Text(
                              _clientSelectionne!.nomComplet
                                  .substring(0, 1)
                                  .toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _clientSelectionne!.nomComplet,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: isSmallScreen ? 14 : 16,
                                  ),
                                ),
                                if (_clientSelectionne!.email.isNotEmpty)
                                  _buildClientInfoRow(
                                    'Email',
                                    _clientSelectionne!.email,
                                    Icons.email,
                                  ),
                                if (_clientSelectionne!.telephone != null &&
                                    _clientSelectionne!.telephone!.isNotEmpty)
                                  _buildClientInfoRow(
                                    'Téléphone',
                                    _clientSelectionne!.telephone!,
                                    Icons.phone,
                                  ),
                                _buildClientInfoRow(
                                  'Adresse',
                                  _clientSelectionne!.adresse,
                                  Icons.location_on,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildClientInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: EdgeInsets.only(top: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDevisInfo(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Date d\'expiration',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          SizedBox(height: 16),
          InkWell(
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: _dateExpiration,
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(
                  const Duration(days: 365),
                ),
              );
              if (date != null) {
                setState(() {
                  _dateExpiration = date;
                });
              }
            },
            child: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: const Color(0xFF7C3AED),
                    size: isSmallScreen ? 16 : 18,
                  ),
                  SizedBox(width: 8),
                  Text(
                    '${_dateExpiration.day.toString().padLeft(2, '0')}/'
                    '${_dateExpiration.month.toString().padLeft(2, '0')}/'
                    '${_dateExpiration.year}',
                    style: TextStyle(
                      color: const Color(0xFF1F2937),
                      fontWeight: FontWeight.w500,
                      fontSize: isSmallScreen ? 14 : 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildArticlesSection(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Articles header
          Text(
            'Articles',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 12),
          // Product search field
          TextField(
            controller: _productSearchController,
            decoration: InputDecoration(
              hintText: 'Rechercher un produit par nom ou code...',
              prefixIcon: Icon(
                Icons.search,
                color: const Color(0xFF3B82F6),
              ),
              suffixIcon: _isSearchingProduct
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onSubmitted: _rechercherEtAjouterProduit,
            textInputAction: TextInputAction.search,
          ),
          const SizedBox(height: 12),
          if (_items.isEmpty)
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Aucun article ajouté',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Commencez par ajouter des articles à votre devis',
                    style: TextStyle(color: Colors.grey[500], fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          else
            Column(
              children: _items.asMap().entries.map((entry) {
                return _buildArticleItem(entry.value, entry.key);
              }).toList(),
            ),
          if (_items.isNotEmpty) ...[
            SizedBox(height: 16),
            _buildFinancialSummary(),
          ],
        ],
      ),
    );
  }

  Widget _buildArticleItem(DevisItem item, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.designation,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  'Ref: ${item.reference}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: Text(
              '${item.quantite} ${item.unite}',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: Text(
              item.prixUnitaireFormate,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: Text(
              item.sousTotalFormate,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(width: 4),
          IconButton(
            onPressed: () => _supprimerArticle(index),
            icon: const Icon(Icons.delete, color: Colors.red, size: 18),
            constraints: BoxConstraints(minWidth: 32, minHeight: 32),
            padding: EdgeInsets.all(4),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummary() {
    // Calculate values following the step-by-step logic
    final sousTotalOriginal = _items.fold(
        0.0, (sum, item) => sum + (item.prixUnitaireHT * item.quantite));
    final sousTotalApresRemiseItems =
        _items.fold(0.0, (sum, item) => sum + item.sousTotalApresRemise);
    final totalRemiseItems = sousTotalOriginal - sousTotalApresRemiseItems;

    final remiseValue = double.tryParse(_remiseController.text) ?? 0.0;
    final remiseGlobale =
        sousTotalApresRemiseItems * remiseValue / 100; // Always percentage
    final totalHT = sousTotalApresRemiseItems - remiseGlobale;
    final tauxTva = double.tryParse(_tvaController.text) ?? 19.0;
    final montantTva = totalHT * tauxTva / 100;
    final totalTTC = totalHT + montantTva;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF3B82F6).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF3B82F6).withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          // Sous-total original
          _buildSummaryRow(
            'ST HT:',
            '${sousTotalOriginal.toStringAsFixed(3)} DT',
            isSubtitle: true,
          ),

          // Individual product remises (if applicable)
          if (totalRemiseItems > 0) ...[
            SizedBox(height: 8),
            _buildSummaryRow(
              'Remise produits:',
              '- ${totalRemiseItems.toStringAsFixed(3)} DT',
              color: Colors.green[600],
              isSubtitle: true,
            ),
            SizedBox(height: 8),
            _buildSummaryRow(
              'ST après remise produits:',
              '${sousTotalApresRemiseItems.toStringAsFixed(3)} DT',
              isSubtitle: true,
            ),
          ],

          // Global remise (if applicable)
          if (remiseGlobale > 0) ...[
            SizedBox(height: 8),
            _buildSummaryRow(
              'Remise globale ($remiseValue%):',
              '- ${remiseGlobale.toStringAsFixed(3)} DT',
              color: const Color(0xFFF59E0B),
              isSubtitle: true,
            ),
          ],

          // Total HT
          if (remiseGlobale > 0 || totalRemiseItems > 0) ...[
            SizedBox(height: 8),
            _buildSummaryRow(
              'Total HT:',
              '${totalHT.toStringAsFixed(3)} DT',
              isSubtitle: true,
            ),
          ],

          // TVA
          SizedBox(height: 8),
          _buildSummaryRow(
            'TVA ($tauxTva%):',
            '${montantTva.toStringAsFixed(3)} DT',
            color: Colors.grey[600],
            isSubtitle: true,
          ),

          // Separator
          SizedBox(height: 12),
          Container(
            height: 1,
            color: const Color(0xFF3B82F6).withValues(alpha: 0.3),
          ),
          SizedBox(height: 12),

          // Total TTC
          _buildSummaryRow(
            'Total TTC:',
            '${totalTTC.toStringAsFixed(3)} DT',
            color: const Color(0xFF3B82F6),
            isBold: true,
            fontSize: 18,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value, {
    Color? color,
    bool isBold = false,
    bool isSubtitle = false,
    double? fontSize,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: fontSize ?? (isSubtitle ? 14 : 16),
            fontWeight: isBold
                ? FontWeight.w700
                : (isSubtitle ? FontWeight.w500 : FontWeight.w600),
            color: color ?? const Color(0xFF1F2937),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: fontSize ?? (isSubtitle ? 14 : 16),
            fontWeight: isBold ? FontWeight.w700 : FontWeight.w600,
            color: color ?? const Color(0xFF1F2937),
          ),
        ),
      ],
    );
  }

  Widget _buildFinancialSettings(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.calculate_outlined,
                  color: const Color(0xFFF59E0B),
                  size: isSmallScreen ? 20 : 24,
                ),
              ),
              SizedBox(width: 12),
              Text(
                'Paramètres Financiers',
                style: TextStyle(
                  fontSize: isSmallScreen ? 16 : 18,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          isSmallScreen
              ? Column(
                  children: [
                    // Remise Section for small screens
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.discount_outlined,
                                color: const Color(0xFFF59E0B),
                                size: 16,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Remise (%)',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF1F2937),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),
                          TextField(
                            controller: _remiseController,
                            style: TextStyle(fontSize: 14),
                            decoration: InputDecoration(
                              hintText: 'Pourcentage de remise',
                              hintStyle: TextStyle(
                                color: Colors.grey[500],
                                fontSize: 14,
                              ),
                              prefixIcon: Icon(
                                Icons.percent,
                                color: const Color(0xFFF59E0B),
                                size: 16,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: const Color(0xFFF59E0B),
                                  width: 2,
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              isDense: true,
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (_) => setState(() {}),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16),
                    // TVA Section for small screens
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.percent,
                                color: const Color(0xFFF59E0B),
                                size: 16,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'TVA (%)',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF1F2937),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),
                          TextField(
                            controller: _tvaController,
                            style: TextStyle(fontSize: 14),
                            decoration: InputDecoration(
                              hintText: 'Taux de TVA en %',
                              hintStyle: TextStyle(
                                color: Colors.grey[500],
                                fontSize: 14,
                              ),
                              prefixIcon: Icon(
                                Icons.percent,
                                color: const Color(0xFFF59E0B),
                                size: 16,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: const Color(0xFFF59E0B),
                                  width: 2,
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              isDense: true,
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (_) => setState(() {}),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              : Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[200]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.discount_outlined,
                                  color: const Color(0xFFF59E0B),
                                  size: 18,
                                ),
                                SizedBox(width: 8),
                                Flexible(
                                  child: Text(
                                    'Remise (%)',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2937),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 12),
                            TextField(
                              controller: _remiseController,
                              style: TextStyle(fontSize: 16),
                              decoration: InputDecoration(
                                hintText: 'Pourcentage de remise',
                                hintStyle: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 16,
                                ),
                                prefixIcon: Icon(
                                  Icons.percent,
                                  color: const Color(0xFFF59E0B),
                                  size: 18,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: Colors.grey[300]!,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: Colors.grey[300]!,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: const Color(0xFFF59E0B),
                                    width: 2,
                                  ),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              keyboardType: TextInputType.number,
                              onChanged: (_) => setState(() {}),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[200]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.percent,
                                  color: const Color(0xFFF59E0B),
                                  size: 18,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'TVA (%)',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2937),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 12),
                            TextField(
                              controller: _tvaController,
                              style: TextStyle(fontSize: 16),
                              decoration: InputDecoration(
                                hintText: 'Taux de TVA en %',
                                hintStyle: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 16,
                                ),
                                prefixIcon: Icon(
                                  Icons.percent,
                                  color: const Color(0xFFF59E0B),
                                  size: 18,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: Colors.grey[300]!,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: Colors.grey[300]!,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: const Color(0xFFF59E0B),
                                    width: 2,
                                  ),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              keyboardType: TextInputType.number,
                              onChanged: (_) => setState(() {}),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notes',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              hintText: 'Ajouter des notes pour ce devis...',
              border: OutlineInputBorder(),
            ),
            maxLines: 4,
          ),
        ],
      ),
    );
  }

  void _rechercherClients(String query) {
    if (query.trim().isEmpty) {
      setState(() {
        _clientsFiltres.clear();
        _showClientDropdown = false;
      });
      return;
    }

    final clientProvider = context.read<FirebaseClientProvider>();

    // Debug: Check if clients are loaded
    debugPrint(
        'Nombre de clients disponibles: ${clientProvider.clients.length}');

    if (clientProvider.clients.isEmpty) {
      setState(() {
        _clientsFiltres.clear();
        _showClientDropdown = false;
      });
      return;
    }

    final queryLower = query.toLowerCase().trim();
    final clientsFiltres = clientProvider.clients
        .where((client) {
          final nomComplet = client.nomComplet.toLowerCase();
          final email = client.email.toLowerCase();
          final tel = client.tel?.toLowerCase() ?? '';

          return nomComplet.contains(queryLower) ||
              email.contains(queryLower) ||
              tel.contains(queryLower);
        })
        .take(10) // Augmenter à 10 résultats
        .toList();

    debugPrint('Résultats trouvés pour "$query": ${clientsFiltres.length}');

    setState(() {
      _clientsFiltres = clientsFiltres;
      _showClientDropdown = true; // Always show dropdown to provide feedback
    });
  }

  void _rechercherEtAjouterProduit(String query) async {
    if (query.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez entrer un nom ou code de produit'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSearchingProduct = true;
    });

    try {
      final produitProvider = context.read<ProduitProvider>();

      // Ensure products are loaded
      if (produitProvider.produits.isEmpty) {
        await produitProvider.chargerProduits();
      }

      final queryLower = query.toLowerCase();
      final produitsCorrespondants = produitProvider.produits.where((produit) {
        return produit.nom.toLowerCase().contains(queryLower) ||
            produit.code.toLowerCase().contains(queryLower);
      }).toList();

      setState(() {
        _isSearchingProduct = false;
      });

      if (produitsCorrespondants.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Aucun produit trouvé pour "$query"'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      if (produitsCorrespondants.length == 1) {
        // Si un seul produit trouvé, l'ajouter directement
        final produit = produitsCorrespondants.first;
        _productSearchController.clear();
        _afficherDialogueQuantite(produit);
      } else {
        // Si plusieurs produits trouvés, afficher une liste de sélection
        _afficherListeProduits(produitsCorrespondants, query);
      }
    } catch (e) {
      setState(() {
        _isSearchingProduct = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la recherche: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _afficherListeProduits(List<Produit> produits, String query) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Produits trouvés pour "$query"'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: produits.length,
            itemBuilder: (context, index) {
              final produit = produits[index];
              final isAvailable = produit.stock > 0;

              return ListTile(
                title: Text(produit.nom),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Code: ${produit.code}'),
                    Text('Prix: ${produit.prix.toStringAsFixed(2)} DT'),
                    Text(
                      'Stock: ${produit.stock}',
                      style: TextStyle(
                        color: isAvailable ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
                trailing: isAvailable
                    ? FilledButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _productSearchController.clear();
                          _afficherDialogueQuantite(produit);
                        },
                        style: FilledButton.styleFrom(
                          backgroundColor: const Color(0xFF3B82F6),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Sélectionner',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      )
                    : Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red.shade100,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Rupture',
                          style: TextStyle(
                            color: Colors.red.shade700,
                            fontSize: 12,
                          ),
                        ),
                      ),
                enabled: isAvailable,
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _afficherDialogueQuantite(Produit produit) {
    final quantiteController = TextEditingController(text: '1');
    final prixController = TextEditingController(text: produit.prix.toString());
    final remiseController = TextEditingController(text: '0');
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.add_shopping_cart,
                  color: const Color(0xFF3B82F6),
                  size: isSmallScreen ? 20 : 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Ajouter un article',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 16 : 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2937),
                  ),
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: isSmallScreen ? double.maxFinite : 400,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        produit.nom,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Code: ${produit.code}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                // Quantity
                Text(
                  'Quantité',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: isSmallScreen ? 12 : 14,
                    color: const Color(0xFF374151),
                  ),
                ),
                const SizedBox(height: 6),
                TextField(
                  controller: quantiteController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'Entrez la quantité',
                    hintStyle:
                        TextStyle(fontSize: 12, color: Colors.grey.shade500),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 12),
                ),
                const SizedBox(height: 16),
                // Prix unitaire
                Text(
                  'Prix unitaire HT (DT)',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: isSmallScreen ? 12 : 14,
                    color: const Color(0xFF374151),
                  ),
                ),
                const SizedBox(height: 6),
                TextField(
                  controller: prixController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    hintText: 'Prix unitaire',
                    hintStyle:
                        TextStyle(fontSize: 12, color: Colors.grey.shade500),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 12),
                ),
                const SizedBox(height: 16),
                // Remise
                Text(
                  'Remise (optionnelle)',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: isSmallScreen ? 12 : 14,
                    color: const Color(0xFF374151),
                  ),
                ),
                const SizedBox(height: 6),
                TextField(
                  controller: remiseController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    hintText: '0',
                    suffixText: '%',
                    hintStyle:
                        TextStyle(fontSize: 12, color: Colors.grey.shade500),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      side: const BorderSide(color: Color(0xFF3B82F6)),
                      foregroundColor: const Color(0xFF3B82F6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Annuler',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: FilledButton(
                    onPressed: () {
                      final quantite =
                          int.tryParse(quantiteController.text) ?? 0;
                      final prix = double.tryParse(prixController.text) ?? 0.0;
                      final remiseValue =
                          double.tryParse(remiseController.text) ?? 0.0;

                      if (quantite > 0 && prix > 0) {
                        Navigator.pop(context);
                        _ajouterItem(produit, quantite, prix, remiseValue);
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Veuillez entrer une quantité et un prix valides'),
                            backgroundColor: Color(0xFFF59E0B),
                          ),
                        );
                      }
                    },
                    style: FilledButton.styleFrom(
                      backgroundColor: const Color(0xFF3B82F6),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Ajouter',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _ajouterItem(
      Produit produit, int quantite, double prix, double remiseValue) {
    if (produit.id != null && quantite > 0 && prix > 0) {
      final item = DevisItem(
        produitId: produit.id!,
        reference: produit.code,
        designation: produit.nom,
        quantite: quantite,
        unite: 'pièce',
        prixUnitaireHT: prix,
        remise: remiseValue,
        remiseEnPourcentage: true, // Always percentage
      );

      setState(() {
        _items.add(item);
      });
    }
  }

  void _supprimerArticle(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  void _enregistrerDevis() async {
    if (_clientSelectionne == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez sélectionner un client')),
      );
      return;
    }

    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez ajouter au moins un article')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final devis = Devis(
        id: widget.devisAModifier?.id, // Keep existing ID if editing
        numero: widget.devisAModifier?.numero ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        clientId: _clientSelectionne!.id!,
        dateCreation: widget.devisAModifier?.dateCreation ?? DateTime.now(),
        dateExpiration: _dateExpiration,
        conditionsValidite: _conditionsController.text,
        items: _items,
        remisePourcentage: double.tryParse(_remiseController.text) ?? 0.0,
        remiseMontant: 0.0, // Always use percentage, never fixed amount
        tauxTva: double.tryParse(_tvaController.text) ?? 19.0,
        notes: _notesController.text,
        contactCommercial:
            _contactController.text.isEmpty ? null : _contactController.text,
      );

      bool success;
      String message;

      if (widget.devisAModifier != null) {
        // Mode modification
        success = await context.read<DevisProvider>().modifierDevis(devis);
        message = success
            ? 'Devis modifié avec succès'
            : 'Erreur lors de la modification';
      } else {
        // Mode création
        success = await context.read<DevisProvider>().creerDevis(devis);
        message =
            success ? 'Devis créé avec succès' : 'Erreur lors de la création';
      }

      if (success && mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(message), backgroundColor: Colors.green),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

class _ProductSearchField extends StatefulWidget {
  final List<Produit> products;
  final Produit? selectedProduct;
  final Function(Produit?) onProductSelected;

  const _ProductSearchField({
    required this.products,
    required this.selectedProduct,
    required this.onProductSelected,
  });

  @override
  State<_ProductSearchField> createState() => _ProductSearchFieldState();
}

class _ProductSearchFieldState extends State<_ProductSearchField> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<Produit> _filteredProducts = [];
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _filteredProducts = widget.products;
    if (widget.selectedProduct != null) {
      _searchController.text = widget.selectedProduct!.nom;
    }
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus) {
      _showSearchDropdown();
    } else {
      Future.delayed(Duration(milliseconds: 150), () {
        _hideSearchDropdown();
      });
    }
  }

  void _filterProducts(String query) {
    setState(() {
      if (widget.products.isEmpty) {
        _filteredProducts = [];
      } else if (query.isEmpty) {
        _filteredProducts = widget.products;
      } else {
        _filteredProducts = widget.products.where((product) {
          return product.nom.toLowerCase().contains(query.toLowerCase()) ||
              product.code.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
    _updateOverlay();
  }

  void _showSearchDropdown() {
    if (_overlayEntry != null) return;

    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideSearchDropdown() {
    _removeOverlay();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var size = renderBox.size;
    var offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        top: offset.dy + size.height,
        width: size.width,
        child: Material(
          elevation: 4,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            constraints: BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: _filteredProducts.isEmpty
                ? Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      'Aucun produit trouvé',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    itemCount: _filteredProducts.length,
                    itemBuilder: (context, index) {
                      final product = _filteredProducts[index];
                      return InkWell(
                        onTap: () {
                          _selectProduct(product);
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: index < _filteredProducts.length - 1
                                    ? Colors.grey[200]!
                                    : Colors.transparent,
                                width: 1,
                              ),
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      product.nom,
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    Text(
                                      'Code: ${product.code}',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.grey[600],
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                              Text(
                                '${product.prix} DT',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF3B82F6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ),
      ),
    );
  }

  void _selectProduct(Produit product) {
    setState(() {
      _searchController.text = product.nom;
    });
    widget.onProductSelected(product);
    _hideSearchDropdown();
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _searchController,
      focusNode: _focusNode,
      decoration: InputDecoration(
        hintText: 'Rechercher un produit...',
        hintStyle: TextStyle(fontSize: 12, color: Colors.grey[500]),
        prefixIcon: Icon(Icons.search, size: 16, color: Colors.grey[600]),
        suffixIcon: widget.selectedProduct != null
            ? IconButton(
                icon: Icon(Icons.clear, size: 16),
                onPressed: () {
                  setState(() {
                    _searchController.clear();
                  });
                  widget.onProductSelected(null);
                  _filterProducts('');
                },
                constraints: BoxConstraints(minWidth: 24, minHeight: 24),
                padding: EdgeInsets.all(4),
              )
            : null,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        isDense: true,
      ),
      style: TextStyle(fontSize: 12),
      onChanged: _filterProducts,
    );
  }
}
