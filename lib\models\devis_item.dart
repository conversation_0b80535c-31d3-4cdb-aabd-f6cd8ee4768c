class DevisItem {
  final int? id;
  final String? devisId; // Nullable car sera défini lors de la sauvegarde
  final String produitId;
  final String reference;
  final String designation;
  final int quantite;
  final String unite;
  final double prixUnitaireHT;
  final double remise;
  final bool remiseEnPourcentage;

  DevisItem({
    this.id,
    this.devisId,
    required this.produitId,
    required this.reference,
    required this.designation,
    required this.quantite,
    required this.unite,
    required this.prixUnitaireHT,
    this.remise = 0.0,
    this.remiseEnPourcentage = true,
  });

  // Calculs
  double get sousTotal => prixUnitaireHT * quantite;

  // Montant de la remise calculé
  double get montantRemise {
    if (remise == 0.0) return 0.0;
    final sousTotal = prixUnitaireHT * quantite;
    return remiseEnPourcentage ? (sousTotal * remise / 100) : remise;
  }

  // Sous-total après remise
  double get sousTotalApresRemise => sousTotal - montantRemise;

  // Formatage
  String get prixUnitaireFormate => '${prixUnitaireHT.toStringAsFixed(3)} DT';
  String get sousTotalFormate => '${sousTotal.toStringAsFixed(3)} DT';
  String get sousTotalApresRemiseFormate =>
      '${sousTotalApresRemise.toStringAsFixed(3)} DT';

  // Remise formatée
  String get remiseFormatee {
    if (remise == 0.0) return '0%';
    return remiseEnPourcentage
        ? '${remise.toStringAsFixed(1)}%'
        : '${remise.toStringAsFixed(3)} DT';
  }

  // Référence formatée (par défaut "N/A")
  String get referenceFormatee => reference.isNotEmpty ? reference : 'N/A';

  // Unité formatée (par défaut "pièce")
  String get uniteFormatee => unite.isNotEmpty ? unite : 'pièce';

  // Créer un DevisItem à partir d'un produit
  factory DevisItem.fromProduit({
    int? id,
    String? devisId,
    required String produitId,
    required String reference,
    required String designation,
    required int quantite,
    String unite = 'pièce',
    required double prixUnitaireHT,
    double remise = 0.0,
    bool remiseEnPourcentage = true,
  }) {
    return DevisItem(
      id: id,
      devisId: devisId,
      produitId: produitId,
      reference: reference,
      designation: designation,
      quantite: quantite,
      unite: unite,
      prixUnitaireHT: prixUnitaireHT,
      remise: remise,
      remiseEnPourcentage: remiseEnPourcentage,
    );
  }

  // Convertir un DevisItem en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'produitId': produitId,
      'reference': reference,
      'designation': designation,
      'quantite': quantite,
      'unite': unite,
      'prixUnitaireHT': prixUnitaireHT,
      'remise': remise,
      'remiseEnPourcentage': remiseEnPourcentage ? 1 : 0,
    };
  }

  // Créer un DevisItem à partir d'une Map de la base de données
  factory DevisItem.fromMap(Map<String, dynamic> map) {
    return DevisItem(
      produitId: map['produitId'],
      reference: map['reference'],
      designation: map['designation'],
      quantite: map['quantite'],
      unite: map['unite'],
      prixUnitaireHT: map['prixUnitaireHT'].toDouble(),
      remise: map['remise']?.toDouble() ?? 0.0,
      remiseEnPourcentage: (map['remiseEnPourcentage'] ?? 1) == 1,
    );
  }

  // Créer une copie du DevisItem avec des modifications
  DevisItem copyWith({
    int? id,
    String? devisId,
    String? produitId,
    String? reference,
    String? designation,
    int? quantite,
    String? unite,
    double? prixUnitaireHT,
    double? remise,
    bool? remiseEnPourcentage,
  }) {
    return DevisItem(
      id: id ?? this.id,
      devisId: devisId ?? this.devisId,
      produitId: produitId ?? this.produitId,
      reference: reference ?? this.reference,
      designation: designation ?? this.designation,
      quantite: quantite ?? this.quantite,
      unite: unite ?? this.unite,
      prixUnitaireHT: prixUnitaireHT ?? this.prixUnitaireHT,
      remise: remise ?? this.remise,
      remiseEnPourcentage: remiseEnPourcentage ?? this.remiseEnPourcentage,
    );
  }

  @override
  String toString() {
    return 'DevisItem{id: $id, reference: $reference, designation: $designation, quantite: $quantite}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DevisItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
