import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../../providers/auth_provider.dart';
import '../home_screen.dart';
import '../merchandiser/merchandiser_home_screen.dart';

import '../admin/admin_user_management_screen.dart';

import 'user_type_selection_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _rememberMe = false;
  int _logoTapCount = 0;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState?.validate() ?? false) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      final success = await authProvider.login(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (mounted && success) {
        // Navigate based on user type
        if (authProvider.userType == 'merchandiser') {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => MerchandiserHomeScreen()),
          );
        } else {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      } else if (mounted && authProvider.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.error!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final isDesktop = size.width > 1200;

    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Scaffold(
          backgroundColor: Colors.grey.shade50,
          body: SafeArea(
            child: Center(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                  horizontal:
                      isDesktop
                          ? size.width * 0.3
                          : isTablet
                          ? size.width * 0.2
                          : 24.0,
                  vertical: 24.0,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo et titre
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical:
                            isDesktop
                                ? 60
                                : isTablet
                                ? 40
                                : 32,
                      ),
                      child: Column(
                        children: [
                          // Logo VitaBrosse avec accès admin secret
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                _logoTapCount++;
                              });

                              if (_logoTapCount >= 5) {
                                // Réinitialiser le compteur
                                setState(() {
                                  _logoTapCount = 0;
                                });

                                // Naviguer vers l'écran d'administration
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder:
                                        (context) =>
                                            const AdminUserManagementScreen(),
                                  ),
                                );
                              }
                            },
                            child: Hero(
                              tag: 'vitabrosse_logo',
                              child: VitaBrosseLogo(
                                height:
                                    isDesktop
                                        ? 120
                                        : isTablet
                                        ? 100
                                        : 80,
                                showText: false,
                              ),
                            ),
                          ),
                          SizedBox(
                            height:
                                isDesktop
                                    ? 32
                                    : isTablet
                                    ? 24
                                    : 20,
                          ),

                          // Titre de bienvenue
                          Text(
                            'Bienvenue',
                            style: Theme.of(
                              context,
                            ).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF1F2937),
                              fontSize:
                                  isDesktop
                                      ? 32
                                      : isTablet
                                      ? 28
                                      : 24,
                            ),
                          ),
                          SizedBox(height: isDesktop ? 12 : 8),

                          Text(
                            'Connectez-vous à votre espace VitaBrosse',
                            style: Theme.of(
                              context,
                            ).textTheme.bodyLarge?.copyWith(
                              color: Colors.grey.shade600,
                              fontSize:
                                  isDesktop
                                      ? 18
                                      : isTablet
                                      ? 16
                                      : 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    // Formulaire de connexion
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: isDesktop ? 400 : double.infinity,
                      ),
                      child: Card(
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                          side: BorderSide(
                            color: Colors.grey.shade200,
                            width: 1,
                          ),
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(
                            isDesktop
                                ? 40
                                : isTablet
                                ? 32
                                : 24,
                          ),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // Champ email
                                TextFormField(
                                  controller: _emailController,
                                  keyboardType: TextInputType.emailAddress,
                                  decoration: const InputDecoration(
                                    labelText: 'Email',
                                    hintText: '<EMAIL>',
                                    prefixIcon: Icon(Icons.email_outlined),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Veuillez saisir votre email';
                                    }
                                    if (!RegExp(
                                      r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                    ).hasMatch(value)) {
                                      return 'Veuillez saisir un email valide';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: isDesktop ? 24 : 20),

                                // Champ mot de passe
                                TextFormField(
                                  controller: _passwordController,
                                  obscureText: !_isPasswordVisible,
                                  decoration: InputDecoration(
                                    labelText: 'Mot de passe',
                                    hintText: 'Votre mot de passe',
                                    prefixIcon: const Icon(Icons.lock_outline),
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        _isPasswordVisible
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isPasswordVisible =
                                              !_isPasswordVisible;
                                        });
                                      },
                                    ),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Veuillez saisir votre mot de passe';
                                    }
                                    if (value.length < 6) {
                                      return 'Le mot de passe doit contenir au moins 6 caractères';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: isDesktop ? 20 : 16),

                                // Se souvenir de moi et mot de passe oublié - Version responsive
                                Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    Row(
                                      children: [
                                        Checkbox(
                                          value: _rememberMe,
                                          onChanged: (value) {
                                            setState(() {
                                              _rememberMe = value ?? false;
                                            });
                                          },
                                        ),
                                        Expanded(
                                          child: GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                _rememberMe = !_rememberMe;
                                              });
                                            },
                                            child: const Text(
                                              'Se souvenir de moi',
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: TextButton(
                                        onPressed: () {
                                          // TODO: Implémenter mot de passe oublié
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                'Fonctionnalité à venir',
                                              ),
                                            ),
                                          );
                                        },
                                        child: const Text(
                                          'Mot de passe oublié ?',
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: isDesktop ? 32 : 24),

                                // Bouton de connexion
                                FilledButton(
                                  onPressed:
                                      authProvider.isLoading
                                          ? null
                                          : _handleLogin,
                                  style: FilledButton.styleFrom(
                                    padding: EdgeInsets.symmetric(
                                      vertical: isDesktop ? 20 : 16,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  child:
                                      authProvider.isLoading
                                          ? const SizedBox(
                                            height: 20,
                                            width: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                    Colors.white,
                                                  ),
                                            ),
                                          )
                                          : Text(
                                            'Se connecter',
                                            style: TextStyle(
                                              fontSize: isDesktop ? 18 : 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: isDesktop ? 32 : 24),

                    // Lien vers inscription
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Pas encore de compte ? ',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: isDesktop ? 16 : 14,
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        const UserTypeSelectionScreen(),
                              ),
                            );
                          },
                          child: Text(
                            'S\'inscrire',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: isDesktop ? 16 : 14,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Connexion en tant qu'invité
                    SizedBox(height: isDesktop ? 16 : 12),
                    TextButton.icon(
                      onPressed: () {
                        Navigator.of(context).pushReplacement(
                          MaterialPageRoute(
                            builder: (context) => const HomeScreen(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.person_outline),
                      label: Text(
                        'Continuer en tant qu\'invité',
                        style: TextStyle(fontSize: isDesktop ? 16 : 14),
                      ),
                    ),

                    // Test Firebase
                    SizedBox(height: isDesktop ? 16 : 12),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
