import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/produit_provider.dart';
import '../../models/produit.dart';
import 'produit_form_screen.dart';
import 'produit_detail_screen.dart';

class ProduitsScreen extends StatefulWidget {
  const ProduitsScreen({super.key});

  @override
  State<ProduitsScreen> createState() => _ProduitsScreenState();
}

class _ProduitsScreenState extends State<ProduitsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'Toutes';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(left: padding, bottom: 16),
              title: Text(
                'Produits',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFF1F2937),
                  fontSize: isSmallScreen ? 20 : 24,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF10B981).withValues(alpha: 0.05),
                      const Color(0xFF059669).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: isSmallScreen ? 20 : 30,
                      right: isSmallScreen ? 10 : 15,
                      child: Container(
                        width: isSmallScreen ? 60 : 80,
                        height: isSmallScreen ? 60 : 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF10B981).withValues(alpha: 0.1),
                              const Color(0xFF059669).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.inventory_2_outlined,
                            size: isSmallScreen ? 24 : 32,
                            color: const Color(0xFF10B981),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              Container(
                margin: EdgeInsets.only(right: isSmallScreen ? 12 : 16, top: 8),
                child: FilledButton.icon(
                  onPressed: () => _naviguerVersFormulaire(context),
                  icon: Icon(Icons.add, size: isSmallScreen ? 16 : 18),
                  label: Text(isSmallScreen ? 'Nouveau' : 'Nouveau'),
                  style: FilledButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 12 : 16,
                      vertical: isSmallScreen ? 6 : 8,
                    ),
                    backgroundColor: const Color(0xFF10B981),
                    textStyle: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                  ),
                ),
              ),
            ],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.fromLTRB(padding, 16, padding, 8),
              child: Column(
                children: [
                  // Barre de recherche optimisée
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(
                        isSmallScreen ? 12 : 16,
                      ),
                      border: Border.all(color: Colors.grey.shade200),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Rechercher un produit...',
                        hintStyle: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: isSmallScreen ? 14 : 16,
                        ),
                        prefixIcon: Icon(
                          Icons.search,
                          color: Colors.grey.shade400,
                          size: isSmallScreen ? 20 : 24,
                        ),
                        suffixIcon:
                            _searchController.text.isNotEmpty
                                ? IconButton(
                                  icon: Icon(
                                    Icons.clear,
                                    color: Colors.grey.shade400,
                                    size: isSmallScreen ? 20 : 24,
                                  ),
                                  onPressed: () {
                                    _searchController.clear();
                                    context
                                        .read<ProduitProvider>()
                                        .chargerProduits();
                                  },
                                )
                                : null,
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 16 : 20,
                          vertical: isSmallScreen ? 12 : 16,
                        ),
                      ),
                      onChanged: (value) {
                        if (value.isEmpty) {
                          context.read<ProduitProvider>().chargerProduits();
                        } else {
                          context.read<ProduitProvider>().rechercherProduits(
                            value,
                          );
                        }
                      },
                    ),
                  ),
                  SizedBox(height: isSmallScreen ? 12 : 16),
                  // Filtre par catégorie adaptatif
                  Consumer<ProduitProvider>(
                    builder: (context, provider, child) {
                      final categories = ['Toutes', ...provider.categories];
                      return SizedBox(
                        height: isSmallScreen ? 36 : 40,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: categories.length,
                          itemBuilder: (context, index) {
                            final category = categories[index];
                            final isSelected = category == _selectedCategory;
                            return Padding(
                              padding: EdgeInsets.only(
                                right: isSmallScreen ? 6 : 8,
                              ),
                              child: FilterChip(
                                label: Text(
                                  category,
                                  style: TextStyle(
                                    fontSize: isSmallScreen ? 12 : 14,
                                  ),
                                ),
                                selected: isSelected,
                                onSelected: (selected) {
                                  setState(() {
                                    _selectedCategory = category;
                                  });
                                  provider.filtrerParCategorie(category);
                                },
                                padding: EdgeInsets.symmetric(
                                  horizontal: isSmallScreen ? 8 : 12,
                                  vertical: isSmallScreen ? 2 : 4,
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          // Liste des produits
          Consumer<ProduitProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(strokeWidth: 3),
                        SizedBox(height: 16),
                        Text(
                          'Chargement des produits...',
                          style: TextStyle(color: Colors.grey, fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                );
              }

              if (provider.error != null) {
                return SliverFillRemaining(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.all(padding),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.red.shade50,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              Icons.error_outline,
                              size: 48,
                              color: Colors.red.shade400,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Oups ! Une erreur est survenue',
                            style: Theme.of(
                              context,
                            ).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2937),
                              fontSize: isSmallScreen ? 18 : 20,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            provider.error!,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: isSmallScreen ? 14 : 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          FilledButton.icon(
                            onPressed: () {
                              provider.effacerErreur();
                              provider.chargerProduits();
                            },
                            icon: const Icon(Icons.refresh),
                            label: const Text('Réessayer'),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              if (provider.produits.isEmpty) {
                return SliverFillRemaining(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.all(padding),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: const Color(
                                0xFF10B981,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              Icons.inventory_2_outlined,
                              size: 48,
                              color: const Color(0xFF10B981),
                            ),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Aucun produit trouvé',
                            style: Theme.of(
                              context,
                            ).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2937),
                              fontSize: isSmallScreen ? 18 : 20,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Commencez par ajouter votre premier produit',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: isSmallScreen ? 14 : 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          FilledButton.icon(
                            onPressed: () => _naviguerVersFormulaire(context),
                            icon: const Icon(Icons.add),
                            label: const Text('Ajouter un produit'),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              return SliverPadding(
                padding: EdgeInsets.fromLTRB(padding, 8, padding, 20),
                sliver: SliverGrid(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: _getOptimalCrossAxisCount(screenWidth),
                    childAspectRatio: _getOptimalAspectRatio(screenWidth),
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final produit = provider.produits[index];
                    return _buildModernProductCard(
                      context,
                      produit,
                      isSmallScreen,
                    );
                  }, childCount: provider.produits.length),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // Optimisation du nombre de colonnes selon la largeur d'écran
  int _getOptimalCrossAxisCount(double screenWidth) {
    if (screenWidth < 360) {
      return 1; // Très petits écrans : 1 colonne
    } else if (screenWidth < 600) {
      return 1; // Petits écrans : 1 colonne pour plus de lisibilité
    } else if (screenWidth < 900) {
      return 2; // Écrans moyens : 2 colonnes
    } else if (screenWidth < 1200) {
      return 3; // Grands écrans : 3 colonnes
    } else {
      return 4; // Très grands écrans : 4 colonnes
    }
  }

  // Optimisation du ratio d'aspect selon la largeur d'écran
  double _getOptimalAspectRatio(double screenWidth) {
    if (screenWidth < 600) {
      return 3.5; // Petits écrans : cartes horizontales
    } else if (screenWidth < 900) {
      return 2.8; // Écrans moyens : cartes équilibrées
    } else {
      return 2.2; // Grands écrans : cartes plus compactes
    }
  }

  Widget _buildModernProductCard(
    BuildContext context,
    Produit produit,
    bool isSmallScreen,
  ) {
    final isAvailable = produit.estDisponible;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
        border: Border.all(
          color: isAvailable ? Colors.grey.shade200 : Colors.red.shade200,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _naviguerVersDetail(context, produit),
          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
          child: Padding(
            padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
            child:
                isSmallScreen
                    ? _buildMobileLayout(produit)
                    : _buildTabletLayout(produit),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileLayout(Produit produit) {
    final isAvailable = produit.estDisponible;

    return Row(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color:
                isAvailable
                    ? const Color(0xFF10B981).withValues(alpha: 0.1)
                    : Colors.red.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            isAvailable ? Icons.inventory_2_outlined : Icons.warning_outlined,
            color: isAvailable ? const Color(0xFF10B981) : Colors.red.shade600,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                produit.nom,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color:
                      isAvailable
                          ? const Color(0xFF1F2937)
                          : Colors.grey.shade600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                'Code: ${produit.code}',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    produit.prixFormate,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color:
                          isAvailable
                              ? const Color(0xFF10B981)
                              : Colors.grey.shade500,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Stock: ${produit.stock}',
                    style: TextStyle(
                      fontSize: 12,
                      color:
                          isAvailable
                              ? Colors.grey.shade600
                              : Colors.red.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: const Color(0xFF10B981).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.arrow_forward_ios,
            size: 12,
            color: Color(0xFF10B981),
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout(Produit produit) {
    final isAvailable = produit.estDisponible;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color:
                    isAvailable
                        ? const Color(0xFF10B981).withValues(alpha: 0.1)
                        : Colors.red.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                isAvailable
                    ? Icons.inventory_2_outlined
                    : Icons.warning_outlined,
                color:
                    isAvailable ? const Color(0xFF10B981) : Colors.red.shade600,
                size: 24,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color:
                    isAvailable
                        ? const Color(0xFF10B981).withValues(alpha: 0.1)
                        : Colors.red.shade50,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                isAvailable ? 'En stock' : 'Rupture',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color:
                      isAvailable
                          ? const Color(0xFF10B981)
                          : Colors.red.shade600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          produit.nom,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isAvailable ? const Color(0xFF1F2937) : Colors.grey.shade600,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Text(
          'Code: ${produit.code}',
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
        const Spacer(),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              produit.prixFormate,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color:
                    isAvailable
                        ? const Color(0xFF10B981)
                        : Colors.grey.shade500,
              ),
            ),
            Text(
              'Stock: ${produit.stock}',
              style: TextStyle(
                fontSize: 12,
                color: isAvailable ? Colors.grey.shade600 : Colors.red.shade600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _naviguerVersFormulaire(BuildContext context, {Produit? produit}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProduitFormScreen(produit: produit),
      ),
    );
  }

  void _naviguerVersDetail(BuildContext context, Produit produit) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProduitDetailScreen(produit: produit),
      ),
    );
  }
}
