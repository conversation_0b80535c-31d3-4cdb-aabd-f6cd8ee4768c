import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/merchandiser.dart';

class MerchandiserService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'merchandizers';

  // Obtenir tous les merchandisers
  Future<List<Merchandiser>> obtenirTousMerchandisers() async {
    try {
      // print('DEBUG: Fetching merchandisers from collection: $_collection');
      QuerySnapshot snapshot =
          await _firestore.collection(_collection).orderBy('name').get();

      // print('DEBUG: Found ${snapshot.docs.length} documents');

      final merchandisers = <Merchandiser>[];
      for (var doc in snapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          // print('DEBUG: Processing document ${doc.id}: $data');
          final merchandiser = Merchandiser.fromMap({...data, 'id': doc.id});
          merchandisers.add(merchandiser);
          // print('DEBUG: Successfully parsed merchandiser: ${merchandiser.nomComplet}');
        } catch (e) {
          // print('DEBUG: Error parsing document ${doc.id}: $e');
        }
      }

      // print('DEBUG: Successfully parsed ${merchandisers.length} merchandisers');
      return merchandisers;
    } catch (e) {
      // print('DEBUG: Error fetching merchandisers: $e');
      throw Exception('Erreur lors de la récupération des merchandisers: $e');
    }
  }

  // Ajouter un merchandiser
  Future<String> ajouterMerchandiser(Merchandiser merchandiser) async {
    try {
      DocumentReference docRef = await _firestore
          .collection(_collection)
          .add(merchandiser.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('Erreur lors de l\'ajout du merchandiser: $e');
    }
  }

  // Modifier un merchandiser
  Future<bool> modifierMerchandiser(Merchandiser merchandiser) async {
    try {
      if (merchandiser.id == null) {
        throw Exception('ID du merchandiser requis pour la modification');
      }
      await _firestore
          .collection(_collection)
          .doc(merchandiser.id)
          .update(merchandiser.toMap());
      return true;
    } catch (e) {
      throw Exception('Erreur lors de la modification du merchandiser: $e');
    }
  }

  // Supprimer un merchandiser
  Future<bool> supprimerMerchandiser(String id) async {
    try {
      await _firestore.collection(_collection).doc(id).delete();
      return true;
    } catch (e) {
      throw Exception('Erreur lors de la suppression du merchandiser: $e');
    }
  }

  // Rechercher des merchandisers
  Future<List<Merchandiser>> rechercherMerchandisers(String terme) async {
    try {
      QuerySnapshot snapshot =
          await _firestore.collection(_collection).orderBy('nom').get();

      final merchandisers =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return Merchandiser.fromMap({...data, 'id': doc.id});
          }).toList();

      // Filter locally since Firestore doesn't support complex text search
      return merchandisers.where((merchandiser) {
        final searchTerm = terme.toLowerCase();
        return merchandiser.name.toLowerCase().contains(searchTerm) ||
            merchandiser.nomComplet.toLowerCase().contains(searchTerm) ||
            merchandiser.email.toLowerCase().contains(searchTerm) ||
            merchandiser.territoire.toLowerCase().contains(searchTerm);
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la recherche des merchandisers: $e');
    }
  }

  // Obtenir les statistiques
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      final merchandisers = await obtenirTousMerchandisers();

      final nombreTotal = merchandisers.length;
      final nombreActifs =
          merchandisers.where((m) => m.status == 'active').length;
      final nombreInactifs =
          merchandisers.where((m) => m.status != 'active').length;

      return {
        'nombreTotal': nombreTotal,
        'nombreActifs': nombreActifs,
        'nombreInactifs': nombreInactifs,
      };
    } catch (e) {
      throw Exception('Erreur lors de la récupération des statistiques: $e');
    }
  }

  // Obtenir un merchandiser par ID
  Future<Merchandiser?> obtenirMerchandiserParId(String id) async {
    try {
      DocumentSnapshot doc =
          await _firestore.collection(_collection).doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return Merchandiser.fromMap({...data, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Erreur lors de la récupération du merchandiser: $e');
    }
  }

  // Obtenir les merchandisers actifs
  Future<List<Merchandiser>> obtenirMerchandisersActifs() async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('status', isEqualTo: 'active')
              .orderBy('name')
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Merchandiser.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des merchandisers actifs: $e',
      );
    }
  }

  // Obtenir les merchandisers par zone
  Future<List<Merchandiser>> obtenirMerchandisersParZone(String zone) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('territoire', isEqualTo: zone)
              .where('status', isEqualTo: 'active')
              .orderBy('name')
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Merchandiser.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des merchandisers par zone: $e',
      );
    }
  }
}
