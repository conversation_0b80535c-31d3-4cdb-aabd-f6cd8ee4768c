import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../providers/firebase_client_provider.dart';
import '../providers/commande_provider.dart';
import '../models/commande.dart';
import 'professional_ui_components.dart';

class RevenueChart extends StatefulWidget {
  const RevenueChart({super.key});

  @override
  State<RevenueChart> createState() => _RevenueChartState();
}

class _RevenueChartState extends State<RevenueChart> with TickerProviderStateMixin {
  Future<Map<String, dynamic>>? _revenueDataFuture;
  int touchedIndex = -1;
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Define colors at class level for consistency
  static const List<Color> _chartColors = [
    Color(0xFF6366F1),
    Color(0xFF8B5CF6),
    Color(0xFF10B981),
    Color(0xFFF59E0B),
    Color(0xFFEF4444),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    
    // Defer initialization until after the first build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeRevenueDataWithLoading();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _initializeRevenueDataWithLoading() async {
    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );
    final commandeProvider = Provider.of<CommandeProvider>(
      context,
      listen: false,
    );

    try {
      // Ensure base data is loaded first (only if not already loaded)
      List<Future> loadingTasks = [];

      if (clientProvider.clients.isEmpty && !clientProvider.isLoading) {
        loadingTasks.add(clientProvider.loadClients());
      }
      if (commandeProvider.commandes.isEmpty && !commandeProvider.isLoading) {
        loadingTasks.add(commandeProvider.chargerCommandes());
      }

      if (loadingTasks.isNotEmpty) {
        await Future.wait(loadingTasks);
      }

      _revenueDataFuture = _getRevenueData(clientProvider, commandeProvider);
      _animationController.forward();

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      print('Error initializing revenue chart data: $e');
      // Initialize with empty future to prevent null errors
      _revenueDataFuture = _getRevenueData(clientProvider, commandeProvider);
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _revenueDataFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: ModernLoadingIndicator(size: 24));
        }

        if (snapshot.hasError) {
          return _buildErrorWidget(context);
        }

        if (!snapshot.hasData || snapshot.data!['chartData'].isEmpty) {
          return _buildEmptyWidget(context);
        }

        final data = snapshot.data!;
        return _buildChartWidget(context, data);
      },
    );
  }

  Future<Map<String, dynamic>> _getRevenueData(
    FirebaseClientProvider clientProvider,
    CommandeProvider commandeProvider,
  ) async {
    try {
      // Charger les clients et commandes
      await clientProvider.loadClients();
      await commandeProvider.chargerCommandes();

      final clients = clientProvider.clients;
      final commandes = commandeProvider.commandes;

      // Calculer le chiffre d'affaires par client
      Map<String, double> clientRevenue = {};
      Map<String, String> clientNames = {};

      for (final client in clients) {
        final clientId = client.id ?? '';
        final clientName = client.nomClient ?? client.nom ?? 'Client inconnu';
        if (clientId.isNotEmpty) {
          clientNames[clientId] = clientName;
          clientRevenue[clientId] = 0.0;
        }
      }

      for (final commande in commandes) {
        // Only exclude cancelled orders - include all other statuses
        if (commande.statut != StatutCommande.annulee) {
          // If client exists in our list, add to their revenue
          if (clientRevenue.containsKey(commande.clientId)) {
            clientRevenue[commande.clientId] =
                (clientRevenue[commande.clientId] ?? 0.0) + commande.montantTotal;
          } else {
            // For orders with missing/invalid clientId, add to "Unknown Client" category
            const unknownClientId = 'unknown_client';
            if (!clientRevenue.containsKey(unknownClientId)) {
              clientRevenue[unknownClientId] = 0.0;
              clientNames[unknownClientId] = 'Client inconnu';
            }
            clientRevenue[unknownClientId] =
                (clientRevenue[unknownClientId] ?? 0.0) + commande.montantTotal;
          }
        }
      }

      // Trier les clients par chiffre d'affaires décroissant
      final sortedClients = clientRevenue.entries
          .where((entry) => entry.value > 0)
          .toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      final totalRevenue = sortedClients.fold(
        0.0,
        (sum, entry) => sum + entry.value,
      );

      if (totalRevenue == 0) {
        return {
          'chartData': [],
          'totalRevenue': 0.0,
          'bestClient': null,
          'topClients': [],
        };
      }

      // Prendre les top clients et créer les données pour le graphique et la légende
      final topClients = sortedClients.take(5).map((entry) => {
        'name': clientNames[entry.key] ?? 'Client inconnu',
        'revenue': entry.value,
        'percentage': (entry.value / totalRevenue * 100),
      }).toList();

      // Debug: Print client data and revenue comparison
      print('=== REVENUE CHART DEBUG ===');
      print('Total clients with revenue: ${sortedClients.length}');
      print('Total revenue in chart: ${totalRevenue.toStringAsFixed(2)} DT');
      print('Top clients: ${topClients.map((c) => '${c['name']}: ${(c['revenue'] as double).toStringAsFixed(2)}').join(', ')}');
      
      // Calculate total from all orders (like main dashboard does)
      final allOrdersTotal = commandes
          .where((c) => c.statut != StatutCommande.annulee)
          .fold<double>(0, (sum, c) => sum + c.montantTotal);
      print('Total from all non-cancelled orders: ${allOrdersTotal.toStringAsFixed(2)} DT');
      print('Difference: ${(allOrdersTotal - totalRevenue).toStringAsFixed(2)} DT');
      print('=== END DEBUG ===');

      // Informations sur le meilleur client
      final bestClient = topClients.isNotEmpty ? {
        'name': topClients.first['name'],
        'revenue': topClients.first['revenue'],
      } : null;

      // Calculate percentages and create chart data
      final chartData = <PieChartSectionData>[];
      final chartClients = <Map<String, dynamic>>[];
      
      print('=== DETAILED CHART DATA CREATION ===');
      print('Total topClients available: ${topClients.length}');
      
      for (int i = 0; i < topClients.length && i < 5; i++) {
        final client = topClients[i];
        final percentage = client['percentage'] as double;
        final revenue = client['revenue'] as double;
        final name = client['name'] as String;
        
        print('Client $i: $name - Revenue: ${revenue.toStringAsFixed(2)} - Percentage: ${percentage.toStringAsFixed(2)}%');
        
        // Show all clients with revenue > 0
        if (revenue > 0) {
          final color = _chartColors[i % _chartColors.length];
          print('  -> ADDED to chart with color index $i (${color.toString()})');
          
          // Ensure minimum visibility for very small sections
          double chartValue = revenue;
          
          // For sections smaller than 1%, give them minimum visual representation
          if (percentage < 1.0) {
            // Give small sections at least 1% visual space while keeping original title
            chartValue = totalRevenue * 0.01;
            print('  -> Very small section enhanced: ${percentage.toStringAsFixed(2)}% -> 1% visual space for visibility');
          } else {
            print('  -> Normal section: ${percentage.toStringAsFixed(2)}% - using original value');
          }
          
          final chartSection = PieChartSectionData(
            color: color,
            value: chartValue,
            title: '${percentage.toStringAsFixed(1)}%',
            radius: 50,
          );
          chartData.add(chartSection);
          print('  -> Chart section created: originalValue=${revenue}, chartValue=${chartValue}, title=${percentage.toStringAsFixed(1)}%, color=${color.toString()}');
          // Add to chartClients list for legend synchronization
          chartClients.add({
            'name': name,
            'revenue': revenue,
            'percentage': percentage,
            'index': i, // Keep track of original index for color consistency
          });
        } else {
          print('  -> SKIPPED (revenue <= 0)');
        }
      }
      
      print('Final chart data count: ${chartData.length}');
      print('Final chart clients for legend: ${chartClients.map((c) => '${c['name']} (${(c['revenue'] as double).toStringAsFixed(1)}k)').join(', ')}');
      print('=== END DETAILED DEBUG ===');

      return {
        'chartData': chartData,
        'totalRevenue': totalRevenue,
        'bestClient': bestClient,
        'topClients': chartClients, // Use chartClients instead of topClients for perfect sync
      };
    } catch (e) {
      throw Exception('Erreur lors du chargement des données: $e');
    }
  }

  Widget _buildChartWidget(BuildContext context, Map<String, dynamic> data) {
    final chartData = data['chartData'] as List<PieChartSectionData>;
    final totalRevenue = data['totalRevenue'] as double;
    final bestClient = data['bestClient'] as Map<String, dynamic>?;
    final topClients = data['topClients'] as List<Map<String, dynamic>>;

    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Column(
          children: [
            // Enhanced header with gradient background
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF6366F1).withOpacity(0.05),
                    const Color(0xFF8B5CF6).withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFF6366F1).withOpacity( 0.1),
                ),
              ),
              child: Row(
                children: [
                  // Total revenue with animated counter
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: const Color(0xFF6366F1).withOpacity( 0.1),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: const Icon(
                                Icons.analytics_rounded,
                                size: 16,
                                color: Color(0xFF6366F1),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Chiffre d\'affaires total',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 12 : 14,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF374151),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${(totalRevenue * _animation.value / 1000).toStringAsFixed(1)}k DT',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 20 : 24,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF1F2937),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Best client info with trophy icon
                  if (bestClient != null && bestClient['name'] != null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF10B981).withOpacity( 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.emoji_events_rounded,
                                size: 16,
                                color: Color(0xFF10B981),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Top Client',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 10 : 11,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF10B981),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            bestClient['name'],
                            style: TextStyle(
                              fontSize: isSmallScreen ? 11 : 12,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF1F2937),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            '${(bestClient['revenue'] * _animation.value / 1000).toStringAsFixed(1)}k DT',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 9 : 10,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF10B981),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Enhanced pie chart with animations and interactions
            Container(
              height: isSmallScreen ? 300 : 220,
              child: isSmallScreen
                  ? Column(
                      children: [
                        Expanded(
                          flex: 2,
                          child: _buildAnimatedPieChart(chartData, isSmallScreen),
                        ),
                        const SizedBox(height: 20),
                        Expanded(
                          flex: 3,
                          child: Container(
                            width: double.infinity,
                            child: SingleChildScrollView(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                                child: _buildEnhancedLegend(topClients, totalRevenue, isSmallScreen),
                              ),
                            ),
                          ),
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: _buildAnimatedPieChart(chartData, isSmallScreen),
                        ),
                        const SizedBox(width: 24),
                        Expanded(
                          flex: 2,
                          child: _buildEnhancedLegend(topClients, totalRevenue, isSmallScreen),
                        ),
                      ],
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAnimatedPieChart(List<PieChartSectionData> chartData, bool isSmallScreen) {
    return PieChart(
      PieChartData(
        pieTouchData: PieTouchData(
          touchCallback: (FlTouchEvent event, pieTouchResponse) {
            setState(() {
              if (!event.isInterestedForInteractions ||
                  pieTouchResponse == null ||
                  pieTouchResponse.touchedSection == null) {
                touchedIndex = -1;
                return;
              }
              touchedIndex = pieTouchResponse.touchedSection!.touchedSectionIndex;
            });
          },
        ),
        borderData: FlBorderData(show: false),
        sectionsSpace: 3,
        centerSpaceRadius: isSmallScreen ? 25 : 35,
        startDegreeOffset: -90,
        sections: chartData.asMap().entries.map((entry) {
          final index = entry.key;
          final section = entry.value;
          final isTouched = index == touchedIndex;
          final radius = isTouched ? 65.0 : (isSmallScreen ? 50.0 : 55.0);
          
          return PieChartSectionData(
            color: section.color,
            value: section.value * _animation.value,
            title: isTouched ? section.title : '',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: isTouched ? 14 : 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.black.withOpacity( 0.3),
                  offset: const Offset(1, 1),
                  blurRadius: 2,
                ),
              ],
            ),
            badgeWidget: isTouched ? _buildBadge(section.title, section.color) : null,
            badgePositionPercentageOffset: 1.3,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildBadge(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity( 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEnhancedLegend(
    List<Map<String, dynamic>> topClients,
    double totalRevenue,
    bool isSmallScreen,
  ) {
    print('=== LEGEND CREATION DEBUG ===');
    print('Legend received ${topClients.length} clients');
    for (int i = 0; i < topClients.length; i++) {
      final client = topClients[i];
      print('Legend client $i: ${client['name']} - Revenue: ${client['revenue']} - Index: ${client['index']}');
    }
    print('=== END LEGEND DEBUG ===');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!isSmallScreen) ...[
          Text(
            'Top Clients',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
        ],
        ...topClients.asMap().entries.map((entry) {
          final legendIndex = entry.key;
          final client = entry.value;
          final percentage = client['percentage'] as double;
          final revenue = client['revenue'] as double;
          
          // Use the original index stored in chartClients for color consistency
          final originalIndex = client['index'] as int? ?? legendIndex;
          
          print('RENDERING legend item $legendIndex: ${client['name']} - Revenue: $revenue - Will show: ${revenue > 0}');
          
          // Show all top clients, don't filter by percentage
          if (revenue <= 0) {
            print('  -> SKIPPED (revenue <= 0)');
            return const SizedBox.shrink();
          }
          
          print('  -> CREATING widget for ${client['name']}');
          return Container(
            margin: const EdgeInsets.only(bottom: 2),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: _chartColors[originalIndex % _chartColors.length].withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _chartColors[originalIndex % _chartColors.length],
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: _chartColors[originalIndex % _chartColors.length].withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        client['name'] ?? 'Client inconnu',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 10 : 11,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2937),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${percentage.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 9 : 10,
                              fontWeight: FontWeight.w500,
                              color: _chartColors[originalIndex % _chartColors.length],
                            ),
                          ),
                          Text(
                            '${(revenue * _animation.value / 1000).toStringAsFixed(1)}k DT',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 8 : 9,
                              color: const Color(0xFF6B7280),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  List<Widget> _buildLegend(
    List<Map<String, dynamic>> topClients,
    double totalRevenue, [
    bool isSmallScreen = false,
  ]) {
    final colors = [
      const Color(0xFF6366F1),
      const Color(0xFF8B5CF6),
      const Color(0xFF10B981),
      const Color(0xFFF59E0B),
      const Color(0xFFEF4444),
    ];

    List<Widget> legendItems = [];

    for (int i = 0; i < topClients.length && i < 4; i++) {
      final client = topClients[i];
      final percentage = client['percentage'] as double;

      if (percentage > 0.5) {
        legendItems.add(
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: isSmallScreen ? 1 : 2,
              horizontal: isSmallScreen ? 4 : 0,
            ),
            child: Row(
              mainAxisSize: isSmallScreen ? MainAxisSize.min : MainAxisSize.max,
              children: [
                Container(
                  width: isSmallScreen ? 6 : 8,
                  height: isSmallScreen ? 6 : 8,
                  decoration: BoxDecoration(
                    color: colors[i],
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: isSmallScreen ? 4 : 6),
                Flexible(
                  child: Text(
                    client['name'] ?? 'Client inconnu',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 8 : 10,
                      color: const Color(0xFF6B7280),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    // Ajouter "Autres" si nécessaire
    final othersPercentage = topClients
        .skip(4)
        .fold(0.0, (sum, client) => sum + (client['percentage'] as double));
    if (othersPercentage > 0.5) {
      legendItems.add(
        Padding(
          padding: EdgeInsets.symmetric(
            vertical: isSmallScreen ? 1 : 2,
            horizontal: isSmallScreen ? 4 : 0,
          ),
          child: Row(
            mainAxisSize: isSmallScreen ? MainAxisSize.min : MainAxisSize.max,
            children: [
              Container(
                width: isSmallScreen ? 6 : 8,
                height: isSmallScreen ? 6 : 8,
                decoration: BoxDecoration(
                  color: Colors.grey.shade400,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: isSmallScreen ? 4 : 6),
              Flexible(
                child: Text(
                  'Autres',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 8 : 10,
                    color: const Color(0xFF6B7280),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return legendItems;
  }

  Widget _buildErrorWidget(BuildContext context) {
    return Container(
      height: 140,
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 24),
            SizedBox(height: 8),
            Text(
              'Erreur de chargement',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    return Container(
      height: 140,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.analytics_outlined, color: Colors.grey, size: 24),
            SizedBox(height: 8),
            Text(
              'Aucune donnée disponible',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
