import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import '../../models/catalogue.dart';
import '../../services/catalogue_service.dart';

class CataloguePDFViewer extends StatefulWidget {
  final Catalogue catalogue;

  const CataloguePDFViewer({Key? key, required this.catalogue})
    : super(key: key);

  @override
  State<CataloguePDFViewer> createState() => _CataloguePDFViewerState();
}

class _CataloguePDFViewerState extends State<CataloguePDFViewer> {
  PDFViewController? _pdfViewController;
  bool _isLoading = true;
  String? _error;
  String? _localPath;
  int _currentPage = 1;
  int _totalPages = 0;

  @override
  void initState() {
    super.initState();
    _preparerPDF();
  }

  Future<void> _preparerPDF() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Vérifier si le PDF est disponible localement
      if (widget.catalogue.cheminLocal != null) {
        final isAvailable = await CatalogueService.isPDFAvailableLocally(
          widget.catalogue.cheminLocal!,
        );
        if (isAvailable) {
          _localPath = widget.catalogue.cheminLocal;
        }
      }

      // Si pas disponible localement, essayer de le télécharger
      if (_localPath == null && widget.catalogue.urlPdf != null) {
        _localPath = await CatalogueService.telechargerPDF(
          widget.catalogue.urlPdf!,
          widget.catalogue.nom,
        );
      }

      if (_localPath == null && widget.catalogue.urlPdf == null) {
        setState(() {
          _error = 'Aucun PDF disponible pour ce catalogue';
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Erreur lors du chargement du PDF: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.catalogue.nom,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF1F2937),
        elevation: 0,
        surfaceTintColor: Colors.white,
        actions: [
          // Informations sur la page
          if (_totalPages > 0)
            Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '$_currentPage / $_totalPages',
                  style: const TextStyle(
                    color: Color(0xFF10B981),
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          // Bouton d'ouverture externe
          IconButton(
            icon: const Icon(Icons.open_in_new),
            onPressed: _ouvrirPDFExterne,
            tooltip: 'Ouvrir dans une application externe',
          ),
          // Menu d'options
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'info':
                  _afficherInfos();
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'info',
                    child: Row(
                      children: [
                        Icon(Icons.info),
                        SizedBox(width: 12),
                        Text('Informations'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Chargement du catalogue...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _preparerPDF,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      );
    }

    return Container(
      color: Colors.grey[100],
      child:
          _localPath != null
              ? PDFView(
                filePath: _localPath!,
                onRender: (pages) {
                  setState(() {
                    _totalPages = pages ?? 0;
                  });
                },
                onPageChanged: (page, total) {
                  setState(() {
                    _currentPage = page ?? 1;
                    _totalPages = total ?? 0;
                  });
                },
                onViewCreated: (controller) {
                  _pdfViewController = controller;
                },
                onError: (error) {
                  setState(() {
                    _error = 'Erreur lors du chargement du PDF: $error';
                  });
                },
                swipeHorizontal: true,
                autoSpacing: false,
                pageFling: true,
                pageSnap: true,
                defaultPage: _currentPage - 1,
                fitPolicy: FitPolicy.BOTH,
                preventLinkNavigation: false,
              )
              : const Center(child: Text('Aucun PDF disponible')),
    );
  }

  Widget _buildBottomNavigation() {
    if (_totalPages == 0) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Page précédente
          IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed:
                _currentPage > 1
                    ? () => _pdfViewController?.setPage(_currentPage - 2)
                    : null,
            tooltip: 'Page précédente',
          ),
          // Sélecteur de page
          Expanded(
            child: Center(
              child: GestureDetector(
                onTap: _afficherSelecteurPage,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF10B981).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Page $_currentPage sur $_totalPages',
                    style: const TextStyle(
                      color: Color(0xFF10B981),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Page suivante
          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed:
                _currentPage < _totalPages
                    ? () => _pdfViewController?.setPage(_currentPage)
                    : null,
            tooltip: 'Page suivante',
          ),
        ],
      ),
    );
  }

  void _ouvrirPDFExterne() async {
    try {
      String? urlToOpen;

      if (_localPath != null) {
        urlToOpen = 'file://$_localPath';
      } else if (widget.catalogue.urlPdf != null) {
        urlToOpen = widget.catalogue.urlPdf!;
      }

      if (urlToOpen != null) {
        final uri = Uri.parse(urlToOpen);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          throw 'Impossible d\'ouvrir le PDF';
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur lors de l\'ouverture: $e')),
      );
    }
  }

  void _afficherInfos() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Informations du catalogue'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow('Nom', widget.catalogue.nom),
                _buildInfoRow('Type', widget.catalogue.typeLabel),
                _buildInfoRow('Description', widget.catalogue.description),
                if (_totalPages > 0) _buildInfoRow('Pages', '$_totalPages'),
                _buildInfoRow(
                  'Date de création',
                  widget.catalogue.dateCreation.toString().substring(0, 10),
                ),
                if (widget.catalogue.dateModification != null)
                  _buildInfoRow(
                    'Dernière modification',
                    widget.catalogue.dateModification.toString().substring(
                      0,
                      10,
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _afficherSelecteurPage() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Aller à la page'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Numéro de page (1-$_totalPages)',
                    border: const OutlineInputBorder(),
                  ),
                  onSubmitted: (value) {
                    final page = int.tryParse(value);
                    if (page != null && page >= 1 && page <= _totalPages) {
                      _pdfViewController?.setPage(page - 1);
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
            ],
          ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
