{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutter\\commercial app\\commercial\\android\\app\\.cxx\\Debug\\6d6l404l\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutter\\commercial app\\commercial\\android\\app\\.cxx\\Debug\\6d6l404l\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}