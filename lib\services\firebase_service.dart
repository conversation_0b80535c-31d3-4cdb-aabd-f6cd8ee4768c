import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';

/// Service principal pour Firebase
class FirebaseService {
  // Instances Firebase
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // Getters pour accéder aux services
  static FirebaseFirestore get firestore => _firestore;
  static FirebaseAuth get auth => _auth;
  static FirebaseStorage get storage => _storage;

  // Collections
  static CollectionReference get users => _firestore.collection('users');
  static CollectionReference get clients => _firestore.collection('clients');
  static CollectionReference get produits => _firestore.collection('produits');
  static CollectionReference get commandes =>
      _firestore.collection('commandes');
  static CollectionReference get devis => _firestore.collection('devis');
  static CollectionReference get commercials =>
      _firestore.collection('commercials');
  static CollectionReference get merchandizers =>
      _firestore.collection('merchandizers');

  /// Initialise les paramètres Firestore
  static Future<void> initializeFirestore() async {
    try {
      // Configuration pour la persistance hors ligne
      _firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      // Activer la persistance hors ligne
      await _firestore.enableNetwork();

      print('Firestore initialisé avec succès');
    } catch (e) {
      print('Erreur lors de l\'initialisation de Firestore: $e');
      // Essayer de continuer en mode hors ligne
      try {
        await _firestore.disableNetwork();
        print('Firestore configuré en mode hors ligne');
      } catch (offlineError) {
        print(
          'Impossible de configurer Firestore en mode hors ligne: $offlineError',
        );
      }
    }
  }

  /// Vérifie la connectivité réseau
  static Future<bool> checkNetworkConnectivity() async {
    try {
      // Tester une simple requête
      await _firestore.collection('test').limit(1).get();
      return true;
    } catch (e) {
      print('Pas de connexion réseau: $e');
      return false;
    }
  }

  /// Obtient l'utilisateur actuel
  static User? getCurrentUser() {
    return _auth.currentUser;
  }

  /// Vérifie si l'utilisateur est connecté
  static bool isUserLoggedIn() {
    return _auth.currentUser != null;
  }

  /// Déconnecte l'utilisateur
  static Future<void> signOut() async {
    await _auth.signOut();
  }

  /// Obtient un document par ID
  static Future<DocumentSnapshot> getDocument(
    String collection,
    String docId,
  ) async {
    return await _firestore.collection(collection).doc(docId).get();
  }

  /// Ajoute un document
  static Future<DocumentReference> addDocument(
    String collection,
    Map<String, dynamic> data,
  ) async {
    data['createdAt'] = FieldValue.serverTimestamp();
    data['updatedAt'] = FieldValue.serverTimestamp();
    return await _firestore.collection(collection).add(data);
  }

  /// Met à jour un document
  static Future<void> updateDocument(
    String collection,
    String docId,
    Map<String, dynamic> data,
  ) async {
    data['updatedAt'] = FieldValue.serverTimestamp();
    await _firestore.collection(collection).doc(docId).update(data);
  }

  /// Supprime un document
  static Future<void> deleteDocument(String collection, String docId) async {
    await _firestore.collection(collection).doc(docId).delete();
  }

  /// Upload un fichier vers Firebase Storage
  static Future<String> uploadFile(String path, dynamic file) async {
    try {
      final ref = _storage.ref().child(path);
      await ref.putFile(file);
      return await ref.getDownloadURL();
    } catch (e) {
      print('Erreur lors de l\'upload: $e');
      rethrow;
    }
  }

  /// Supprime un fichier de Firebase Storage
  static Future<void> deleteFile(String url) async {
    try {
      await _storage.refFromURL(url).delete();
    } catch (e) {
      print('Erreur lors de la suppression du fichier: $e');
    }
  }
}
