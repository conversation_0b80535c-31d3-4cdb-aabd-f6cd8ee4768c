# Google Drive Upload Troubleshooting Guide

## Issues Identified and Solutions

### 1. OAuth Configuration Problems

**Problem**: ApiException: 10 (DEVELOPER_ERROR) when trying to sign in with Google

**Root Cause**: Missing or incorrect OAuth configuration in Google Cloud Console

**Solution**:
1. Get your app's SHA-1 fingerprint:
   ```bash
   cd android
   ./gradlew signingReport
   ```

2. Go to [Google Cloud Console](https://console.cloud.google.com/)
3. Select project: `vitabrosseapp-467110`
4. Navigate to "APIs & Services" > "Credentials"
5. Find OAuth client: `************-8oolv1cf9u61mdsqv47i9lqvt75qq4ru.apps.googleusercontent.com`
6. Add your SHA-1 fingerprint and verify package name: `com.example.commercial`

### 2. Service Account vs OAuth Confusion

**Problem**: Two different authentication methods causing conflicts

**Solution**: Created unified `PhotoUploadService` that:
- Tries <PERSON> first (preferred for user authentication)
- Falls back to Service Account if <PERSON><PERSON><PERSON> fails
- Provides consistent API for the app

### 3. Missing Error Handling

**Problem**: Upload failures not properly communicated to users

**Solution**: Enhanced error handling with:
- User-friendly error messages
- Retry mechanisms
- Progress indicators
- Success/failure notifications

### 4. Hardcoded Credentials Security Risk

**Problem**: Service account private key exposed in code

**Recommendation**: Move to environment variables or secure storage in production

## Testing Your Upload Functionality

### Step 1: Use the Debug Screen
Add this to your app to test uploads:

```dart
// In your main navigation, add:
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const UploadTestScreen(),
  ),
);
```

### Step 2: Test OAuth Configuration
1. Run "Test OAuth" button
2. Check for initialization success
3. Try "Test Google Sign In"
4. Verify user authentication

### Step 3: Test Upload Functionality
1. Use "Test Gallery Upload" with a small image
2. Try "Test Camera Upload" if camera is available
3. Check debug logs for detailed error information

## Common Error Messages and Solutions

### "ApiException: 10"
- **Cause**: OAuth configuration issue
- **Fix**: Update SHA-1 fingerprint in Google Cloud Console

### "403 Forbidden" or "Storage quota exceeded"
- **Cause**: Service account storage limit reached
- **Fix**: Service automatically falls back to root drive upload

### "Network connection failed"
- **Cause**: Internet connectivity issues
- **Fix**: Check network connection, retry upload

### "File too large"
- **Cause**: File exceeds 10MB limit
- **Fix**: Compress image or use smaller file

### "Upload failed - no file ID returned"
- **Cause**: Google Drive API returned success but no file ID
- **Fix**: Check Google Drive API quotas and permissions

## Verification Steps

1. **Check OAuth Setup**:
   - SHA-1 fingerprint added to Google Cloud Console
   - Package name matches (`com.example.commercial`)
   - OAuth client ID is correct

2. **Test Network Connectivity**:
   - App can reach Google APIs
   - Firewall/proxy not blocking requests

3. **Verify Permissions**:
   - Google Drive API enabled in Google Cloud Console
   - Service account has access to target folder
   - OAuth scopes include Drive access

4. **Test File Upload**:
   - Small test image uploads successfully
   - Google Drive URL is generated
   - File appears in target folder

## Production Recommendations

1. **Security**: Move service account credentials to secure storage
2. **Monitoring**: Add upload analytics and error tracking
3. **User Experience**: Add upload progress bars and better error messages
4. **Performance**: Implement image compression before upload
5. **Reliability**: Add automatic retry with exponential backoff

## Debug Commands

```bash
# Check SHA-1 fingerprint
cd android && ./gradlew signingReport

# Clean and rebuild
flutter clean && flutter pub get && flutter run

# Check Google Play Services
adb shell dumpsys package com.google.android.gms | grep version
```

## Support Information

- **Project ID**: vitabrosseapp-467110
- **OAuth Client ID**: ************-8oolv1cf9u61mdsqv47i9lqvt75qq4ru.apps.googleusercontent.com
- **Package Name**: com.example.commercial
- **Target Folder**: 1ryAprErbjJ1l-PZSKlOPcMlPxzVZyb9c