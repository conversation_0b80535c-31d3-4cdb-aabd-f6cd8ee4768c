/// Configuration et constantes de l'application VitaBrosse Pro
class AppConfig {
  // Version de l'application
  static const String appVersion = '1.0.0';
  static const String appName = 'VitaBrosse Pro';
  static const String companyName = 'VitaBrosse';

  // Couleurs du thème
  static const primaryColor = 0xFF6366F1;
  static const backgroundColor = 0xFFFAFAFA;
  static const textColor = 0xFF1F2937;
  static const greyColor = 0xFF6B7280;

  // Configuration de base de données
  static const String dbName = 'vitabrosse_pro.db';
  static const int dbVersion = 1;
  // Limites et validation
  static const int maxNameLength = 50;
  static const int maxDescriptionLength = 255;
  static const int maxEmailLength = 100;
  static const int minPasswordLength = 8;
  static const double maxPrice = 999999.99;
  static const int maxQuantity = 99999;
  static const int minCodeLength = 2;
  static const int maxCodeLength = 20;
  static const int maxAddressLength = 200;

  // Formats de date
  static const String dateFormat = 'dd/MM/yyyy';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';

  // Messages d'erreur
  static const String networkError = 'Erreur de connexion réseau';
  static const String genericError = 'Une erreur inattendue s\'est produite';
  static const String validationError = 'Veuillez vérifier les champs saisis';

  // Messages de succès
  static const String saveSuccess = 'Données sauvegardées avec succès';
  static const String deleteSuccess = 'Suppression effectuée avec succès';
  static const String updateSuccess = 'Mise à jour effectuée avec succès';

  // Configuration d'export
  static const String csvSeparator = ';';
  static const String pdfTitle = 'VitaBrosse Pro - Export';

  // WhatsApp
  static const String whatsappPrefix = '+33';
  static const String whatsappMessage =
      'Bonjour, voici votre commande VitaBrosse Pro :';

  // Debug
  static const bool isDebugMode = true; // Changé en false pour la production

  // URLs (à configurer selon l'environnement)
  static const String baseUrl = 'https://api.vitabrosse.com'; // Production
  static const String baseUrlDev =
      'https://dev-api.vitabrosse.com'; // Développement

  static String get currentBaseUrl => isDebugMode ? baseUrlDev : baseUrl;
}
