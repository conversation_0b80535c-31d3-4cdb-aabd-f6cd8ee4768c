import 'dart:developer' as developer;
import '../config/app_config.dart';

/// Service de logging centralisé pour l'application
class LoggingService {
  LoggingService._();

  static const String _name = 'VitaBrosse Pro';

  /// Log d'information
  static void info(String message, {String? tag, Object? error}) {
    if (AppConfig.isDebugMode) {
      developer.log(
        message,
        name: _name,
        level: 800, // INFO level
        error: error,
      );
    }
  }

  /// Log d'erreur
  static void error(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    developer.log(
      message,
      name: _name,
      level: 1000, // ERROR level
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log de debug (seulement en mode debug)
  static void debug(String message, {String? tag, Object? error}) {
    if (AppConfig.isDebugMode) {
      developer.log(
        message,
        name: _name,
        level: 500, // DEBUG level
        error: error,
      );
    }
  }

  /// Log d'avertissement
  static void warning(String message, {String? tag, Object? error}) {
    if (AppConfig.isDebugMode) {
      developer.log(
        message,
        name: _name,
        level: 900, // WARNING level
        error: error,
      );
    }
  }

  /// Log d'action utilisateur (pour l'audit)
  static void userAction(String action, Map<String, dynamic>? data) {
    info('USER_ACTION: $action', error: data);
  }

  /// Log de performance
  static void performance(String operation, Duration duration) {
    if (AppConfig.isDebugMode) {
      info('PERFORMANCE: $operation took ${duration.inMilliseconds}ms');
    }
  }
}
