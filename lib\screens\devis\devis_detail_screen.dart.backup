import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/devis.dart';
import '../../models/client.dart';
import '../../providers/firebase_client_provider.dart';
import '../../services/whatsapp_service.dart';
import '../../widgets/whatsapp_message_dialog.dart';
import '../../widgets/professional_ui_components.dart';
import '../../widgets/vitabrosse_logo.dart';

class DevisDetailScreen extends StatefulWidget {
  final Devis devis;

  const DevisDetailScreen({super.key, required this.devis});

  @override
  State<DevisDetailScreen> createState() => _DevisDetailScreenState();
}

class _DevisDetailScreenState extends State<DevisDetailScreen> {
  Client? client;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerClient();
    });
  }

  void _chargerClient() async {
    final clientProvider = context.read<FirebaseClientProvider>();
    if (!clientProvider.isLoaded) {
      await Future.microtask(() async {
        await clientProvider.chargerClients();
      });
    }

    final clients = clientProvider.clients;
    setState(() {
      client = clients.firstWhere(
        (c) => c.id == widget.devis.clientId,
        orElse:
            () => Client(
              nom: 'Client inconnu',
              prenom: '',
              email: '',
              telephone: '',
              adresse: '',
              dateCreation: DateTime.now(),
            ),
      );
    });
  }

  void _envoyerParWhatsApp() async {
    if (client == null || client!.primaryPhone.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Numéro de téléphone du client manquant'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      // Générer l'aperçu du message
      final messageParDefaut =
          WhatsAppService.genererMessageDevis(
            widget.devis,
            client!,
            null,
          ).split('\n').take(3).join('\n') +
          '...'; // Afficher la boîte de dialogue pour personnaliser le message
      final messagePersonnalise = await showDialog<String>(
        context: context,
        builder:
            (context) => WhatsAppMessageDialog(
              titre: 'Envoyer le devis par WhatsApp',
              messageParDefaut: messageParDefaut,
              client: client!,
              onEnvoyer: () {},
            ),
      );

      // Vérifier si l'utilisateur n'a pas annulé
      if (messagePersonnalise == null) {
        return; // L'utilisateur a cliqué sur Annuler
      }

      // Envoyer le devis (chaîne vide = message par défaut, sinon message personnalisé)
      final messageAUtiliser =
          messagePersonnalise.isEmpty ? null : messagePersonnalise;
      final success = await WhatsAppService.envoyerDevis(
        devis: widget.devis,
        client: client!,
        messagePersonnalise: messageAUtiliser,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'WhatsApp ouvert avec le devis'
                  : 'Erreur lors de l\'ouverture de WhatsApp',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('dd/MM/yyyy');
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Devis ${widget.devis.numeroFormate}',
              style: TextStyle(
                fontWeight: FontWeight.w700,
                color: const Color(0xFF1F2937),
                fontSize: isSmallScreen ? 18 : 20,
              ),
            ),
            Text(
              _getStatutLabel(widget.devis.statut),
              style: TextStyle(
                fontSize: 12,
                color: _getStatutColor(widget.devis.statut),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        actions: [
          Container(
            margin: EdgeInsets.only(right: 8),
            child: IconButton(
              icon: Icon(
                Icons.chat,
                color: client?.primaryPhone.isNotEmpty == true
                    ? const Color(0xFF25D366)
                    : Colors.grey[400],
                size: 24,
              ),
              onPressed: client?.primaryPhone.isNotEmpty == true
                  ? () => _envoyerParWhatsApp()
                  : null,
              tooltip: 'Envoyer par WhatsApp',
              style: IconButton.styleFrom(
                backgroundColor: client?.primaryPhone.isNotEmpty == true
                    ? const Color(0xFF25D366).withValues(alpha: 0.1)
                    : Colors.grey[100],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(right: 16),
            child: IconButton(
              icon: const Icon(Icons.share, size: 24),
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Partage en cours de développement'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
              tooltip: 'Partager',
              style: IconButton.styleFrom(
                backgroundColor: Colors.grey[100],
                foregroundColor: const Color(0xFF6B7280),
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec statut et informations principales
            ProfessionalCard(
              hasShadow: true,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.devis.numeroFormate,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: const Color(0xFF1F2937),
                              ),
                            ),
                            SizedBox(height: 8),
                            Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Icon(
                                    Icons.euro,
                                    size: 16,
                                    color: const Color(0xFF3B82F6),
                                  ),
                                ),
                                SizedBox(width: 8),
                                Text(
                                  widget.devis.totalTTCFormate,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700,
                                    color: const Color(0xFF059669),
                                  ),
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'TTC',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      _buildStatutChip(widget.devis.statut),
                    ],
                  ),
                  SizedBox(height: 20),
                  
                  // Dates importantes
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.calendar_today,
                                    size: 16,
                                    color: const Color(0xFF3B82F6),
                                  ),
                                  SizedBox(width: 6),
                                  Text(
                                    'Date de création',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 4),
                              Text(
                                dateFormat.format(widget.devis.dateCreation),
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF1F2937),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: 1,
                          height: 40,
                          color: Colors.grey[300],
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    widget.devis.estExpire ? Icons.warning : Icons.access_time,
                                    size: 16,
                                    color: widget.devis.estExpire ? Colors.red : const Color(0xFF059669),
                                  ),
                                  SizedBox(width: 6),
                                  Text(
                                    'Date d\'expiration',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 4),
                              Text(
                                dateFormat.format(widget.devis.dateExpiration),
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: widget.devis.estExpire ? Colors.red : const Color(0xFF1F2937),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: isSmallScreen ? 16 : 20),

            // Informations client
            ProfessionalCard(
              hasShadow: true,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF7C3AED).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.person,
                          color: const Color(0xFF7C3AED),
                          size: 20,
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        'Informations Client',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: const Color(0xFF1F2937),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  if (client != null) ...[
                    Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${client!.prenom} ${client!.nom}',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              fontSize: 16,
                              color: const Color(0xFF1F2937),
                            ),
                          ),
                          SizedBox(height: 8),
                          if (client!.email.isNotEmpty) ...[
                            Row(
                              children: [
                                Icon(Icons.email, size: 16, color: Colors.grey[600]),
                                SizedBox(width: 8),
                                Text(
                                  client!.email,
                                  style: TextStyle(color: Colors.grey[700]),
                                ),
                              ],
                            ),
                            SizedBox(height: 6),
                          ],
                          if (client!.primaryPhone.isNotEmpty)
                            Row(
                              children: [
                                Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                                SizedBox(width: 8),
                                Text(
                                  client!.primaryPhone,
                                  style: TextStyle(color: Colors.grey[700]),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ] else
                    Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          CircularProgressIndicator(
                            strokeWidth: 2,
                            color: const Color(0xFF3B82F6),
                          ),
                          SizedBox(width: 12),
                          Text('Chargement des informations client...'),
                        ],
                      ),
                    ),
                ],
              ),
            ),

            SizedBox(height: isSmallScreen ? 16 : 20),

            // Articles
            ProfessionalCard(
              hasShadow: true,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF059669).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.shopping_cart,
                          color: const Color(0xFF059669),
                          size: 20,
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        'Articles (${widget.devis.nombreArticles})',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: const Color(0xFF1F2937),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  ...widget.devis.items.map((item) => _buildItemCard(item, isSmallScreen)).toList(),
                ],
              ),
            ),

            SizedBox(height: isSmallScreen ? 16 : 20),

            // Récapitulatif financier
            _buildFinancialSummary(isSmallScreen),
          ],
        ),
      ),
    );
  }
                              Text(
                                'Date d\'expiration',
                                style: theme.textTheme.labelMedium,
                              ),
                              Text(
                                dateFormat.format(widget.devis.dateExpiration),
                                style: TextStyle(
                                  color:
                                      widget.devis.estExpire
                                          ? Colors.red
                                          : null,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Informations client
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Client',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (client != null) ...[
                      Text('${client!.prenom} ${client!.nom}'),
                      if (client!.email.isNotEmpty) Text(client!.email),
                      if (client!.primaryPhone.isNotEmpty)
                        Text(client!.primaryPhone),
                    ] else
                      const Text('Chargement...'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Articles
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Articles',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...widget.devis.items.map(
                      (item) => Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 3,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item.designation,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    'Ref: ${item.reference}',
                                    style: theme.textTheme.bodySmall,
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Text(
                                '${item.quantite} ${item.unite}',
                                textAlign: TextAlign.center,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                item.prixUnitaireFormate,
                                textAlign: TextAlign.right,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                item.sousTotalFormate,
                                textAlign: TextAlign.right,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Totaux
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildLigneTotaux(
                      'Sous-total HT',
                      widget.devis.sousTotalFormate,
                    ),
                    if (widget.devis.montantRemise > 0)
                      _buildLigneTotaux(
                        'Remise',
                        '- ${widget.devis.montantRemiseFormate}',
                      ),
                    _buildLigneTotaux('Total HT', widget.devis.totalHTFormate),
                    _buildLigneTotaux(
                      'TVA (${widget.devis.tauxTva}%)',
                      widget.devis.montantTvaFormate,
                    ),
                    const Divider(),
                    _buildLigneTotaux(
                      'Total TTC',
                      widget.devis.totalTTCFormate,
                      isTotal: true,
                    ),
                  ],
                ),
              ),
            ),

            // Notes
            if (widget.devis.notes?.isNotEmpty == true) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notes',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(widget.devis.notes!),
                    ],
                  ),
                ),
              ),
            ],

            // Conditions de validité
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Conditions de validité',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(widget.devis.conditionsValidite),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatutChip(StatutDevis statut) {
    Color couleur;
    switch (statut) {
      case StatutDevis.brouillon:
        couleur = Colors.grey;
        break;
      case StatutDevis.envoye:
        couleur = Colors.blue;
        break;
      case StatutDevis.accepte:
        couleur = Colors.green;
        break;
      case StatutDevis.refuse:
        couleur = Colors.red;
        break;
      case StatutDevis.expire:
        couleur = Colors.orange;
        break;
    }

    return Chip(
      label: Text(
        statut.name.toUpperCase(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: couleur,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildLigneTotaux(
    String label,
    String montant, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            montant,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
        ],
      ),
    );
  }

  String _getStatutLabel(StatutDevis statut) {
    switch (statut) {
      case StatutDevis.brouillon:
        return 'Brouillon';
      case StatutDevis.envoye:
        return 'Envoyé';
      case StatutDevis.accepte:
        return 'Accepté';
      case StatutDevis.refuse:
        return 'Refusé';
      case StatutDevis.expire:
        return 'Expiré';
    }
  }

  Color _getStatutColor(StatutDevis statut) {
    switch (statut) {
      case StatutDevis.brouillon:
        return Colors.grey[600]!;
      case StatutDevis.envoye:
        return const Color(0xFF3B82F6);
      case StatutDevis.accepte:
        return const Color(0xFF059669);
      case StatutDevis.refuse:
        return Colors.red[600]!;
      case StatutDevis.expire:
        return Colors.orange[600]!;
    }
  }

  Widget _buildStatutChip(StatutDevis statut) {
    final couleur = _getStatutColor(statut);
    final backgroundColor = couleur.withValues(alpha: 0.1);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: couleur.withValues(alpha: 0.3)),
      ),
      child: Text(
        _getStatutLabel(statut),
        style: TextStyle(
          color: couleur,
          fontSize: 12,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }

  Widget _buildItemCard(item, bool isSmallScreen) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.designation,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: const Color(0xFF1F2937),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Ref: ${item.reference}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      '${item.quantite} ${item.unite}',
                      style: TextStyle(
                        color: const Color(0xFF3B82F6),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    item.prixUnitaireFormate,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 12),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF059669).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Sous-total',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF059669),
                  ),
                ),
                Text(
                  item.sousTotalFormate,
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: 16,
                    color: const Color(0xFF059669),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummary(bool isSmallScreen) {
    return ProfessionalCard(
      hasShadow: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.calculate,
                  color: const Color(0xFFF59E0B),
                  size: 20,
                ),
              ),
              SizedBox(width: 12),
              Text(
                'Récapitulatif',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              children: [
                _buildMontantLigne(
                  'Sous-total HT',
                  widget.devis.sousTotalFormate,
                  false,
                ),
                if (widget.devis.montantRemise > 0) ...[
                  SizedBox(height: 8),
                  _buildMontantLigne(
                    'Remise ${widget.devis.remisePourcentage > 0 ? '(${widget.devis.remisePourcentage.toStringAsFixed(1)}%)' : ''}',
                    '- ${widget.devis.montantRemiseFormate}',
                    false,
                  ),
                ],
                SizedBox(height: 8),
                _buildMontantLigne(
                  'Total HT',
                  widget.devis.totalHTFormate,
                  false,
                ),
                SizedBox(height: 8),
                _buildMontantLigne(
                  'TVA (${widget.devis.tauxTva.toStringAsFixed(0)}%)',
                  widget.devis.montantTvaFormate,
                  false,
                ),
                SizedBox(height: 12),
                Container(
                  height: 1,
                  color: Colors.grey[300],
                ),
                SizedBox(height: 12),
                _buildMontantLigne(
                  'TOTAL TTC',
                  widget.devis.totalTTCFormate,
                  true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
