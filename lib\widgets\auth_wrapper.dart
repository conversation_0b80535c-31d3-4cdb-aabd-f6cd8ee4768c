import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../screens/auth/login_screen.dart';
import '../screens/home_screen.dart';
import '../screens/merchandiser/merchandiser_home_screen.dart';

/// Authentication wrapper that automatically handles routing based on auth state
class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Debug logging
        print(
            '🔍 AuthWrapper: isLoading=${authProvider.isLoading}, isAuthenticated=${authProvider.isAuthenticated}, userType=${authProvider.userType}');

        // Show loading screen while checking authentication
        if (authProvider.isLoading) {
          print('🔄 AuthWrapper: Showing loading screen');
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  Sized<PERSON><PERSON>(height: 16),
                  Text(
                    'Vérification de l\'authentification...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // If user is authenticated, route to appropriate home screen
        if (authProvider.isAuthenticated) {
          print('🏠 AuthWrapper: User authenticated, routing to home screen');
          if (authProvider.userType == 'merchandiser') {
            return const MerchandiserHomeScreen();
          } else {
            return const HomeScreen();
          }
        }

        // If not authenticated, show login screen
        print('🔐 AuthWrapper: User not authenticated, showing login screen');
        return const LoginScreen();
      },
    );
  }
}
