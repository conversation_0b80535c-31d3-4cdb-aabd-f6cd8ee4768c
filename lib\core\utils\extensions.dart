import 'package:intl/intl.dart';
import '../config/app_config.dart';

/// Extensions utiles pour les chaînes de caractères
extension StringExtensions on String {
  /// Vérifie si la chaîne est un email valide
  bool get isValidEmail {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(this);
  }

  /// Vérifie si la chaîne est un numéro de téléphone valide (français)
  bool get isValidPhoneNumber {
    return RegExp(r'^(?:\+33|0)[1-9](?:[0-9]{8})$').hasMatch(this);
  }

  /// Capitalise la première lettre
  String get capitalize {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
  }

  /// Capitalise chaque mot
  String get capitalizeWords {
    return split(' ').map((word) => word.capitalize).join(' ');
  }

  /// Supprime les espaces en début et fin et réduit les espaces multiples
  String get cleanSpaces {
    return trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// Vérifie si la chaîne contient uniquement des chiffres
  bool get isNumeric {
    return RegExp(r'^[0-9]+$').hasMatch(this);
  }

  /// Convertit en double de manière sécurisée
  double? get toDoubleOrNull {
    try {
      return double.parse(this);
    } catch (e) {
      return null;
    }
  }

  /// Convertit en int de manière sécurisée
  int? get toIntOrNull {
    try {
      return int.parse(this);
    } catch (e) {
      return null;
    }
  }
}

/// Extensions utiles pour les dates
extension DateTimeExtensions on DateTime {
  /// Formate la date selon le format par défaut de l'app
  String get formatted {
    return DateFormat(AppConfig.dateFormat).format(this);
  }

  /// Formate la date et l'heure selon le format par défaut de l'app
  String get formattedWithTime {
    return DateFormat(AppConfig.dateTimeFormat).format(this);
  }

  /// Vérifie si la date est aujourd'hui
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  /// Vérifie si la date est cette semaine
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
        isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  /// Retourne le début de la journée (00:00:00)
  DateTime get startOfDay {
    return DateTime(year, month, day);
  }

  /// Retourne la fin de la journée (23:59:59)
  DateTime get endOfDay {
    return DateTime(year, month, day, 23, 59, 59);
  }
}

/// Extensions utiles pour les doubles
extension DoubleExtensions on double {
  /// Formate le nombre avec 2 décimales
  String get formatted {
    return toStringAsFixed(2);
  }

  /// Formate le nombre comme un prix en dinars tunisiens
  String get asPrice {
    return '$formatted DT';
  }

  /// Arrondit à 2 décimales
  double get rounded {
    return double.parse(toStringAsFixed(2));
  }
}

/// Extensions utiles pour les listes
extension ListExtensions<T> on List<T> {
  /// Retourne le premier élément ou null si la liste est vide
  T? get firstOrNull {
    return isEmpty ? null : first;
  }

  /// Retourne le dernier élément ou null si la liste est vide
  T? get lastOrNull {
    return isEmpty ? null : last;
  }

  /// Groupe les éléments selon une fonction
  Map<K, List<T>> groupBy<K>(K Function(T) keyFunction) {
    final Map<K, List<T>> result = {};
    for (final element in this) {
      final key = keyFunction(element);
      result.putIfAbsent(key, () => []).add(element);
    }
    return result;
  }
}
