import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/produit.dart';
import '../../providers/produit_provider.dart';
import '../../widgets/professional_ui_components.dart';
import 'produit_form_screen.dart';

class ProduitDetailScreen extends StatefulWidget {
  final Produit produit;

  const ProduitDetailScreen({super.key, required this.produit});

  @override
  State<ProduitDetailScreen> createState() => _ProduitDetailScreenState();
}

class _ProduitDetailScreenState extends State<ProduitDetailScreen> {
  late Produit currentProduit;

  @override
  void initState() {
    super.initState();
    currentProduit = widget.produit;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 140 : 160,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2937)),
              onPressed: () => Navigator.pop(context),
            ),
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(
                left: 56, // Space for back button
                bottom: 16,
                right: 20, // Right padding
              ),
              title: Row(
                children: [
                  Container(
                    height: isSmallScreen ? 24 : 28,
                    width: isSmallScreen ? 24 : 28,
                    decoration: BoxDecoration(
                      color: const Color(0xFF10B981),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.inventory_2,
                      color: Colors.white,
                      size: isSmallScreen ? 16 : 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Détail produit',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 20,
                          ),
                        ),
                        Text(
                          currentProduit.code,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF10B981).withValues(alpha: 0.05),
                      const Color(0xFF059669).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    if (currentProduit.imageUrl != null &&
                        currentProduit.imageUrl!.isNotEmpty)
                      Positioned(
                        top: isSmallScreen ? 40 : 50,
                        right: isSmallScreen ? 20 : 30,
                        child: Container(
                          width: isSmallScreen ? 60 : 80,
                          height: isSmallScreen ? 60 : 80,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image.network(
                              currentProduit.imageUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  _buildDefaultImage(isSmallScreen),
                            ),
                          ),
                        ),
                      )
                    else
                      Positioned(
                        top: isSmallScreen ? 40 : 50,
                        right: isSmallScreen ? 20 : 30,
                        child: _buildDefaultImage(isSmallScreen),
                      ),
                  ],
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(padding),
              child: Column(
                children: [
                  // Product Information Card
                  _buildProductInfoCard(isSmallScreen, padding),
                  const SizedBox(height: 20),
                  // Commercial Information Card
                  _buildCommercialInfoCard(isSmallScreen, padding),
                  const SizedBox(height: 20),
                  // Additional Information Card
                  _buildAdditionalInfoCard(isSmallScreen, padding),
                  const SizedBox(height: 100), // Space for actions
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            onPressed: () => _showDeleteDialog(context),
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            elevation: 6,
            heroTag: "delete",
            child: const Icon(Icons.delete),
          ),
          const SizedBox(width: 16),
          FloatingActionButton.extended(
            onPressed: () => _navigateToEdit(context),
            backgroundColor: const Color(0xFF10B981),
            foregroundColor: Colors.white,
            elevation: 6,
            heroTag: "edit",
            icon: const Icon(Icons.edit),
            label: const Text('Modifier'),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultImage(bool isSmallScreen) {
    return Container(
      width: isSmallScreen ? 60 : 80,
      height: isSmallScreen ? 60 : 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: const Color(0xFF10B981).withValues(alpha: 0.1),
        border: Border.all(
          color: const Color(0xFF10B981).withValues(alpha: 0.2),
        ),
      ),
      child: Center(
        child: Icon(
          Icons.inventory_2_outlined,
          size: isSmallScreen ? 24 : 32,
          color: const Color(0xFF10B981),
        ),
      ),
    );
  }

  Widget _buildProductInfoCard(bool isSmallScreen, double padding) {
    return ProfessionalCard(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: const Color(0xFF10B981),
                  size: isSmallScreen ? 20 : 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Informations produit',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 18 : 20,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildInfoRow('Nom', currentProduit.nom, Icons.inventory_2),
            const SizedBox(height: 16),
            _buildInfoRow('Code', currentProduit.code, Icons.qr_code),
            const SizedBox(height: 16),
            _buildInfoRow(
              'Catégorie',
              currentProduit.categorie,
              Icons.category,
            ),
            if (currentProduit.description.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildInfoRow(
                'Description',
                currentProduit.description,
                Icons.description,
                isDescription: true,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCommercialInfoCard(bool isSmallScreen, double padding) {
    return ProfessionalCard(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.attach_money,
                    color: Color(0xFF10B981), size: isSmallScreen ? 22 : 26),
                SizedBox(width: 10),
                Text('Informations commerciales',
                    style: TextStyle(
                        fontSize: isSmallScreen ? 19 : 22,
                        fontWeight: FontWeight.w700,
                        color: Color(0xFF1F2937))),
              ],
            ),
            SizedBox(height: isSmallScreen ? 16 : 22),
            Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Color(0xFF10B981).withOpacity(0.07),
                      borderRadius: BorderRadius.circular(14),
                      boxShadow: [
                        BoxShadow(
                            color: Colors.black.withOpacity(0.04),
                            blurRadius: 6,
                            offset: Offset(0, 2))
                      ],
                    ),
                    padding: EdgeInsets.symmetric(
                        vertical: isSmallScreen ? 14 : 18,
                        horizontal: isSmallScreen ? 10 : 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.attach_money,
                                color: Color(0xFF10B981),
                                size: isSmallScreen ? 18 : 20),
                            SizedBox(width: 6),
                            Text('Prix unitaire',
                                style: TextStyle(
                                    fontSize: isSmallScreen ? 13 : 15,
                                    color: Color(0xFF1F2937),
                                    fontWeight: FontWeight.w500)),
                          ],
                        ),
                        SizedBox(height: 10),
                        Text('${currentProduit.prix.toStringAsFixed(3)} DT',
                            style: TextStyle(
                                fontSize: isSmallScreen ? 18 : 20,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF10B981))),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 10 : 18),
                Expanded(
                  child: Tooltip(
                    message: currentProduit.stock > 10
                        ? 'Stock suffisant'
                        : (currentProduit.stock > 0
                            ? 'Stock faible'
                            : 'Rupture de stock'),
                    child: Container(
                      decoration: BoxDecoration(
                        color: _getStockColor(currentProduit.stock)
                            .withOpacity(0.07),
                        borderRadius: BorderRadius.circular(14),
                        boxShadow: [
                          BoxShadow(
                              color: Colors.black.withOpacity(0.04),
                              blurRadius: 6,
                              offset: Offset(0, 2))
                        ],
                      ),
                      padding: EdgeInsets.symmetric(
                          vertical: isSmallScreen ? 14 : 18,
                          horizontal: isSmallScreen ? 10 : 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.inventory,
                                  color: _getStockColor(currentProduit.stock),
                                  size: isSmallScreen ? 18 : 20),
                              SizedBox(width: 6),
                              Text('Stock',
                                  style: TextStyle(
                                      fontSize: isSmallScreen ? 13 : 15,
                                      color: Color(0xFF1F2937),
                                      fontWeight: FontWeight.w500)),
                            ],
                          ),
                          SizedBox(height: 10),
                          Text('${currentProduit.stock}',
                              style: TextStyle(
                                  fontSize: isSmallScreen ? 18 : 20,
                                  fontWeight: FontWeight.bold,
                                  color: _getStockColor(currentProduit.stock))),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard(bool isSmallScreen, double padding) {
    return ProfessionalCard(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.more_horiz,
                  color: const Color(0xFF10B981),
                  size: isSmallScreen ? 20 : 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Informations additionnelles',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 18 : 20,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildInfoRow(
              'Date de création',
              _formatDate(currentProduit.dateCreation),
              Icons.calendar_today,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.visibility, color: Colors.grey.shade600, size: 20),
                const SizedBox(width: 12),
                Text(
                  'Statut',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: currentProduit.actif
                        ? const Color(0xFF10B981).withValues(alpha: 0.15)
                        : Colors.red.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        currentProduit.actif
                            ? Icons.check_circle
                            : Icons.cancel,
                        size: 16,
                        color: currentProduit.actif
                            ? const Color(0xFF10B981)
                            : Colors.red,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        currentProduit.actif ? 'Actif' : 'Inactif',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: currentProduit.actif
                              ? const Color(0xFF10B981)
                              : Colors.red,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value,
    IconData icon, {
    bool isDescription = false,
  }) {
    return Row(
      crossAxisAlignment:
          isDescription ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        Icon(icon, color: Colors.grey.shade600, size: 20),
        const SizedBox(width: 12),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
            textAlign: TextAlign.right,
            maxLines: isDescription ? null : 1,
            overflow: isDescription ? null : TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String label,
    String value,
    IconData icon,
    Color color,
    bool isSmallScreen,
  ) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: isSmallScreen ? 18 : 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 12 : 13,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: isSmallScreen ? 18 : 20,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStockColor(int stock) {
    if (stock > 10) return const Color(0xFF10B981);
    if (stock > 0) return Colors.orange;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void _navigateToEdit(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProduitFormScreen(produit: currentProduit),
      ),
    ).then((result) {
      if (result != null && result['success'] == true) {
        // Update the current product with the updated data
        if (result['updatedProduct'] != null) {
          setState(() {
            currentProduit = result['updatedProduct'];
          });
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message'] ?? 'Produit modifié avec succès'),
            backgroundColor: const Color(0xFF10B981),
          ),
        );
      }
    });
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le produit'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer "${currentProduit.nom}" ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          FilledButton(
            onPressed: () => _deleteProduct(context),
            style: FilledButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  void _deleteProduct(BuildContext context) async {
    Navigator.pop(context); // Close dialog

    final provider = context.read<ProduitProvider>();
    final success = await provider.supprimerProduit(currentProduit.id!);

    if (context.mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Produit supprimé avec succès'),
            backgroundColor: Color(0xFF10B981),
          ),
        );
        Navigator.pop(context); // Go back to products list
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(provider.error ?? 'Erreur lors de la suppression'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
