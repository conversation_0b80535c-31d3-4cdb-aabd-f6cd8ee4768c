import 'package:flutter/material.dart';
import '../config/app_config.dart';

/// Service de gestion centralisée des erreurs
class ErrorHandler {
  static void logError(
    String message, {
    dynamic error,
    StackTrace? stackTrace,
  }) {
    if (AppConfig.isDebugMode) {
      debugPrint('ERROR: $message');
      if (error != null) debugPrint('Details: $error');
      if (stackTrace != null) debugPrint('Stack: $stackTrace');
    }

    // En production, ici on pourrait envoyer les erreurs à un service de monitoring
    // comme Firebase Crashlytics, Sentry, etc.
  }

  static void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () => ScaffoldMessenger.of(context).hideCurrentSnackBar(),
        ),
      ),
    );
  }

  static void showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  static void showWarningSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  static String getErrorMessage(dynamic error) {
    if (error == null) return AppConfig.genericError;

    // Mappage des erreurs spécifiques
    String errorString = error.toString().toLowerCase();

    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return AppConfig.networkError;
    }

    if (errorString.contains('validation') || errorString.contains('format')) {
      return AppConfig.validationError;
    }

    return AppConfig.genericError;
  }
}
