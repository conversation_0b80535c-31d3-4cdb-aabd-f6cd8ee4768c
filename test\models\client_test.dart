import 'package:flutter_test/flutter_test.dart';
import 'package:vitabrosse_pro/models/client.dart';

void main() {
  group('Client Model Tests', () {
    test('Client avec nouveaux champs - toMap/fromMap', () {
      // Créer un client avec tous les champs
      final client = Client(
        id: '1',
        nom: '<PERSON><PERSON>',
        prenom: '<PERSON>',
        email: '<EMAIL>',
        telephone: '0123456789',
        adresse: '123 Rue de la Paix, 75001 Paris',
        dateCreation: DateTime(2025, 6, 24),
        codeClient: 'CLI001',
        matriculeFiscale: '1234567890123',
        categorie: 'Particulier',
        modeReglement: 'Carte bancaire',
      );

      // Test toMap()
      final map = client.toMap();
      expect(map['codeClient'], 'CLI001');
      expect(map['matriculeFiscal'], '1234567890123');
      expect(map['categorie'], 'Particulier');
      expect(map['modeReglement'], 'Carte bancaire');

      // Test fromMap()
      final clientFromMap = Client.fromMap(map);
      expect(clientFromMap.codeClient, 'CLI001');
      expect(clientFromMap.matriculeFiscale, '1234567890123');
      expect(clientFromMap.categorie, 'Particulier');
      expect(clientFromMap.modeReglement, 'Carte bancaire');
    });

    test('Client avec champs optionnels null', () {
      final client = Client(
        nom: 'Martin',
        prenom: 'Marie',
        email: '<EMAIL>',
        telephone: '0987654321',
        adresse: '456 Avenue des Champs, 69000 Lyon',
        dateCreation: DateTime(2025, 6, 24),
        // Champs optionnels non fournis
      );

      // Test que les champs optionnels sont null
      expect(client.codeClient, null);
      expect(client.matriculeFiscale, null);
      expect(client.categorie, null);
      expect(client.modeReglement, null);

      // Test toMap/fromMap avec champs null
      final map = client.toMap();
      final clientFromMap = Client.fromMap(map);

      expect(clientFromMap.codeClient, null);
      expect(clientFromMap.matriculeFiscale, null);
      expect(clientFromMap.categorie, null);
      expect(clientFromMap.modeReglement, null);
    });

    test('Client copyWith avec nouveaux champs', () {
      final client = Client(
        nom: 'Dupont',
        prenom: 'Jean',
        email: '<EMAIL>',
        telephone: '0123456789',
        adresse: '123 Rue de la Paix, 75001 Paris',
        dateCreation: DateTime(2025, 6, 24),
      );

      // Test copyWith pour ajouter les nouveaux champs
      final clientModifie = client.copyWith(
        codeClient: 'CLI001',
        matriculeFiscale: '1234567890123',
        categorie: 'Professionnel',
        modeReglement: 'Virement',
      );

      expect(clientModifie.codeClient, 'CLI001');
      expect(clientModifie.matriculeFiscale, '1234567890123');
      expect(clientModifie.categorie, 'Professionnel');
      expect(clientModifie.modeReglement, 'Virement');

      // Vérifier que les autres champs sont conservés
      expect(clientModifie.nom, 'Dupont');
      expect(clientModifie.prenom, 'Jean');
      expect(clientModifie.email, '<EMAIL>');
    });

    test('Nom complet et toString', () {
      final client = Client(
        nom: 'Dupont',
        prenom: 'Jean',
        email: '<EMAIL>',
        telephone: '0123456789',
        adresse: '123 Rue de la Paix, 75001 Paris',
        dateCreation: DateTime(2025, 6, 24),
        codeClient: 'CLI001',
      );

      expect(client.nomComplet, 'Jean Dupont');
      expect(client.toString(), contains('Jean'));
      expect(client.toString(), contains('Dupont'));
    });
  });
}
