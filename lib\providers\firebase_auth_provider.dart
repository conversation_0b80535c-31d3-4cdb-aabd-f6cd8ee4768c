import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/firebase_service.dart';

class FirebaseAuthProvider extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  User? _user;
  bool _isLoading = false;
  String? _errorMessage;
  bool _isLoggingOut = false;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;

  FirebaseAuthProvider() {
    // Écouter les changements d'état d'authentification
    _auth.authStateChanges().listen((User? user) async {
      // Skip auth state processing if we're in the middle of a logout
      if (_isLoggingOut) {
        print('🔄 Skipping auth state change during logout process');
        return;
      }

      if (user != null) {
        print(
          '🔍 Auth state changed - Vérification du statut pour: ${user.uid}',
        );

        // Add a small delay to ensure any logout process is complete
        await Future.delayed(const Duration(milliseconds: 100));

        // Check again if we're still not logging out
        if (_isLoggingOut) {
          print('🔄 Logout started during auth state processing, aborting');
          return;
        }

        // IMPORTANT: Vérifier le statut même lors des changements d'état automatiques
        final isActive = await _checkUserStatus(user.uid);
        print(
          '🔍 Résultat de la vérification du statut (auth state change): $isActive',
        );

        if (!isActive) {
          print(
            '❌ UTILISATEUR INACTIF DÉTECTÉ - DÉCONNEXION FORCÉE (auth state change)',
          );
          // L'utilisateur n'est pas actif, déconnecter immédiatement
          await _auth.signOut();
          _user = null;
          _errorMessage =
              'Votre compte est inactif. Veuillez contacter l\'administrateur.';
        } else {
          print('✅ Utilisateur actif confirmé (auth state change)');
          _user = user;
        }
      } else {
        print('🔓 Auth state changed - Utilisateur déconnecté');
        _user = null;
        _errorMessage = null;
      }

      // Only notify listeners if we're not in the middle of a logout
      if (!_isLoggingOut) {
        notifyListeners();
      }
    });
  }

  /// Connexion avec email et mot de passe
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      print('🔐 Tentative de connexion pour: $email');

      // Authentification avec Firebase
      await _auth.signInWithEmailAndPassword(email: email, password: password);

      // Récupérer l'utilisateur actuel directement depuis _auth.currentUser
      // Cette approche évite complètement l'erreur PigeonUserDetails
      _user = _auth.currentUser;

      // Vérification que l'utilisateur est bien défini
      if (_user == null) {
        print('❌ Erreur: Utilisateur null après connexion réussie');
        _errorMessage = 'Erreur lors de la connexion. Veuillez réessayer.';
        _isLoading = false;
        notifyListeners();
        return false;
      }

      print(
        '✅ Authentification Firebase réussie pour: ${_user!.email} (${_user!.uid})',
      );

      // IMPORTANT: Vérification stricte du statut de l'utilisateur
      print('🔍 Vérification du statut pour l\'utilisateur: ${_user!.uid}');

      // Vérifier le statut de l'utilisateur
      final isActive = await _checkUserStatus(_user!.uid);
      print('🔍 Résultat de la vérification du statut: $isActive');

      if (!isActive) {
        print('❌ UTILISATEUR INACTIF - DÉCONNEXION FORCÉE');
        // L'utilisateur n'est pas actif, déconnecter et renvoyer une erreur
        await _auth.signOut();
        _user = null;
        _errorMessage =
            'Votre compte est inactif. Veuillez contacter l\'administrateur.';
        _isLoading = false;
        notifyListeners();
        return false;
      }

      print('✅ Utilisateur actif, connexion autorisée');
      // Utilisateur actif, mettre à jour la dernière connexion
      await _updateLastLogin(_user!.uid);

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;

      // Gestion spécifique de l'erreur PigeonUserDetails
      if (e.toString().contains('PigeonUserDetails')) {
        print('Erreur PigeonUserDetails détectée lors de la connexion: $e');

        // Vérifier si l'utilisateur est connecté malgré l'erreur
        if (_auth.currentUser != null) {
          _user = _auth.currentUser;

          try {
            // IMPORTANT: Vérifier le statut même en cas d'erreur PigeonUserDetails
            print(
              '🔍 Vérification du statut après erreur PigeonUserDetails pour: ${_user!.uid}',
            );

            final isActive = await _checkUserStatus(_user!.uid);
            print(
              '🔍 Résultat de la vérification du statut après erreur: $isActive',
            );

            if (!isActive) {
              print(
                '❌ UTILISATEUR INACTIF - DÉCONNEXION FORCÉE (après erreur PigeonUserDetails)',
              );
              // L'utilisateur n'est pas actif, déconnecter et renvoyer une erreur
              await _auth.signOut();
              _user = null;
              _errorMessage =
                  'Votre compte est inactif. Veuillez contacter l\'administrateur.';
              _isLoading = false;
              notifyListeners();
              return false;
            }

            print(
              '✅ Utilisateur actif confirmé après erreur PigeonUserDetails',
            );
            // Mettre à jour la dernière connexion seulement si l'utilisateur est actif
            await _updateLastLogin(_user!.uid);

            // Continuer le flux de connexion
            _isLoading = false;
            notifyListeners();
            return true;
          } catch (innerError) {
            print(
              'Erreur lors de la récupération après PigeonUserDetails: $innerError',
            );
          }
        }

        _errorMessage =
            'Erreur technique lors de la connexion. Veuillez réessayer.';
      } else {
        _errorMessage = 'Une erreur inattendue s\'est produite: $e';
      }

      notifyListeners();
      return false;
    }
  }

  /// Inscription avec email et mot de passe
  Future<bool> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String nomComplet,
    required String telephone,
    String? mobile,
    String? territoire,
    required String userType,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = result.user;

      // Créer le profil utilisateur dans la collection appropriée
      if (_user != null) {
        await _createUserProfile(
          uid: _user!.uid,
          email: email,
          nomComplet: nomComplet,
          telephone: telephone,
          mobile: mobile,
          territoire: territoire,
          userType: userType,
        );
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Une erreur inattendue s\'est produite';
      notifyListeners();
      return false;
    }
  }

  /// Inscription avec données complètes pour commercial ou merchandiser
  Future<bool> signUpWithUserData({
    required String email,
    required String password,
    required String nomComplet,
    required String telephone,
    required String userType, // 'commercial' ou 'merchandiser'
    String? mobile,
    String? territoire,
    String status = 'actif', // Par défaut actif pour les nouveaux utilisateurs
  }) async {
    print('Création d\'un utilisateur avec status: $status');
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Créer l'utilisateur avec Firebase Auth
      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Correction du problème de typage PigeonUserDetails
      // Éviter l'erreur de cast en assignant directement l'utilisateur actuel
      _user = _auth.currentUser;

      // Vérification supplémentaire pour s'assurer que l'utilisateur est bien défini
      if (_user == null) {
        print(
          'Utilisateur null après création, tentative de récupération alternative',
        );
        try {
          _user = result.user;
        } catch (e) {
          print('Erreur lors de la récupération alternative: $e');
        }
      }

      // Sauvegarder les infos du formulaire directement dans Firestore
      if (_user != null) {
        final userData = {
          'uid': _user!.uid,
          'email': email,
          'name': nomComplet,
          'nomComplet': nomComplet,
          'telephone': telephone,
          'userType': userType,
          'status': status, // Utiliser uniquement le champ status
          'createdAt': FieldValue.serverTimestamp(),
          'lastLogin': null,
        };

        // Ajouter les champs optionnels s'ils existent
        if (mobile != null && mobile.isNotEmpty) {
          userData['mobile'] = mobile;
        }
        if (territoire != null && territoire.isNotEmpty) {
          userData['territoire'] = territoire;
        }

        // Sauvegarder uniquement dans la collection spécifique selon le type d'utilisateur
        if (userType == 'commercial') {
          print('Sauvegarde des données dans la collection commercials');
          await FirebaseService.commercials.doc(_user!.uid).set(userData);
        } else if (userType == 'merchandiser') {
          print('Sauvegarde des données dans la collection merchandizers');
          await FirebaseService.merchandizers.doc(_user!.uid).set(userData);
        }
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;

      // Gestion spécifique de l'erreur PigeonUserDetails
      if (e.toString().contains('PigeonUserDetails')) {
        print('Erreur PigeonUserDetails détectée: $e');

        // Vérifier si l'utilisateur a bien été créé malgré l'erreur
        if (_auth.currentUser != null) {
          _user = _auth.currentUser;

          try {
            // Tenter de sauvegarder les données utilisateur malgré l'erreur
            final userData = {
              'uid': _user!.uid,
              'email': email,
              'name': nomComplet,
              'nomComplet': nomComplet,
              'telephone': telephone,
              'userType': userType,
              'status': status,
              'createdAt': FieldValue.serverTimestamp(),
              'lastLogin': null,
            };

            if (mobile != null && mobile.isNotEmpty) {
              userData['mobile'] = mobile;
            }
            if (territoire != null && territoire.isNotEmpty) {
              userData['territoire'] = territoire;
            }

            // Sauvegarder dans la collection spécifique selon le type d'utilisateur
            if (userType == 'commercial') {
              print(
                'Sauvegarde des données dans la collection commercials malgré l\'erreur',
              );
              await FirebaseService.commercials.doc(_user!.uid).set(userData);
            } else if (userType == 'merchandiser') {
              print(
                'Sauvegarde des données dans la collection merchandizers malgré l\'erreur',
              );
              await FirebaseService.merchandizers.doc(_user!.uid).set(userData);
            }

            // Continuer le flux d'inscription
            notifyListeners();
            return true;
          } catch (innerError) {
            print(
              'Erreur lors de la récupération après PigeonUserDetails: $innerError',
            );
          }
        }

        _errorMessage =
            'Erreur technique lors de l\'inscription. Veuillez réessayer.';
      } else {
        _errorMessage = 'Erreur lors de l\'inscription: $e';
      }

      notifyListeners();
      return false;
    }
  }

  /// Déconnexion
  Future<void> signOut() async {
    try {
      print('🔓 Début de la déconnexion...');

      // Set a flag to prevent auth state listener interference during logout
      _isLoggingOut = true;

      // Clear local state first
      _user = null;
      _errorMessage = null;

      // Notify listeners immediately to update UI
      notifyListeners();

      // Sign out from Firebase
      await _auth.signOut();

      print('✅ Déconnexion Firebase réussie');

      // Wait longer to ensure Firebase auth state change is fully processed
      await Future.delayed(const Duration(milliseconds: 500));

      // Double-check that we're actually signed out
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        print('⚠️ User still exists after signOut, forcing another signOut');
        await _auth.signOut();
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // Ensure local state is cleared
      _user = null;
      _errorMessage = null;

      // Reset logout flag
      _isLoggingOut = false;

      // Final notification
      notifyListeners();

      print('✅ Déconnexion complète terminée');
    } catch (e) {
      print('❌ Erreur lors de la déconnexion Firebase: $e');

      // Even if Firebase signOut fails, clear local state
      _user = null;
      _errorMessage = 'Erreur lors de la déconnexion';
      _isLoggingOut = false;
      notifyListeners();

      // Re-throw the error
      rethrow;
    }
  }

  /// Force clear all authentication state (for debugging/testing)
  Future<void> forceSignOut() async {
    print('🔓 Force sign out initiated...');

    _isLoggingOut = true;
    _user = null;
    _errorMessage = null;

    try {
      // Multiple attempts to ensure sign out
      await _auth.signOut();
      await Future.delayed(const Duration(milliseconds: 200));

      // Check if still signed in and try again
      if (_auth.currentUser != null) {
        await _auth.signOut();
        await Future.delayed(const Duration(milliseconds: 200));
      }
    } catch (e) {
      print('❌ Error during force sign out: $e');
    }

    _isLoggingOut = false;
    notifyListeners();

    print('✅ Force sign out completed');
  }

  /// Réinitialiser le mot de passe
  Future<bool> resetPassword(String email) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await _auth.sendPasswordResetEmail(email: email);

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Une erreur inattendue s\'est produite';
      notifyListeners();
      return false;
    }
  }

  /// Obtenir le profil utilisateur depuis Firestore
  Future<Map<String, dynamic>?> getUserProfile() async {
    if (_user == null) return null;

    String uid;
    try {
      uid = _user!.uid;
    } catch (e) {
      print('Erreur lors de l\'accès à l\'UID: $e');
      // Récupération de l'utilisateur actuel si _user pose problème
      final currentUser = _auth.currentUser;
      if (currentUser == null) return null;
      uid = currentUser.uid;
    }

    try {
      // Vérifier d'abord dans commercials
      final commercialDoc = await FirebaseService.commercials.doc(uid).get();
      if (commercialDoc.exists) {
        print('Profil utilisateur trouvé dans la collection commercials');
        final data = commercialDoc.data();
        if (data != null && data is Map<String, dynamic>) {
          return data;
        }
      }

      // Sinon vérifier dans merchandizers
      final merchandiserDoc =
          await FirebaseService.merchandizers.doc(uid).get();
      if (merchandiserDoc.exists) {
        print('Profil utilisateur trouvé dans la collection merchandizers');
        final data = merchandiserDoc.data();
        if (data != null && data is Map<String, dynamic>) {
          return data;
        }
      }

      print('Profil utilisateur non trouvé dans les collections spécifiques');
      return null;
    } catch (e) {
      print('Erreur lors de la récupération du profil: $e');
      return null;
    }
  }

  /// Mettre à jour le profil utilisateur
  Future<bool> updateUserProfile(Map<String, dynamic> data) async {
    if (_user == null) return false;

    try {
      // Vérifier dans quelle collection se trouve l'utilisateur
      final uid = _user!.uid;

      // Essayer d'abord dans commercials
      final commercialDoc = await FirebaseService.commercials.doc(uid).get();
      if (commercialDoc.exists) {
        print('Mise à jour du profil dans la collection commercials');
        await FirebaseService.commercials.doc(uid).update(data);
        return true;
      }

      // Sinon essayer dans merchandizers
      final merchandiserDoc =
          await FirebaseService.merchandizers.doc(uid).get();
      if (merchandiserDoc.exists) {
        print('Mise à jour du profil dans la collection merchandizers');
        await FirebaseService.merchandizers.doc(uid).update(data);
        return true;
      }

      print(
        'Utilisateur non trouvé dans les collections spécifiques, impossible de mettre à jour le profil',
      );
      _errorMessage =
          'Utilisateur non trouvé, impossible de mettre à jour le profil';
      return false;
    } catch (e) {
      print('Erreur lors de la mise à jour du profil: $e');
      _errorMessage = 'Erreur lors de la mise à jour du profil';
      notifyListeners();
      return false;
    }
  }

  /// Créer le profil utilisateur dans Firestore
  Future<void> _createUserProfile({
    required String uid,
    required String email,
    required String nomComplet,
    required String telephone,
    String? mobile,
    String? territoire,
    required String userType,
  }) async {
    // Données communes
    final userData = {
      'uid': uid,
      'email': email,
      'nomComplet': nomComplet,
      'telephone': telephone,
      'mobile': mobile ?? '',
      'territoire': territoire ?? '',
      'userType': userType, // Important de stocker le type d'utilisateur
      'status': 'actif', // Par défaut actif pour les nouveaux utilisateurs
      'createdAt': FieldValue.serverTimestamp(),
      'lastLogin': null,
    };

    // Sauvegarder uniquement dans la collection appropriée
    if (userType == 'commercial') {
      print('Sauvegarde des données uniquement dans la collection commercials');
      await FirebaseService.commercials.doc(uid).set(userData);
    } else if (userType == 'merchandiser') {
      print(
        'Sauvegarde des données uniquement dans la collection merchandizers',
      );
      await FirebaseService.merchandizers.doc(uid).set(userData);
    }
  }

  /// Mettre à jour la dernière connexion
  Future<void> _updateLastLogin(String uid) async {
    try {
      // Vérifier dans les deux collections pour trouver le type d'utilisateur
      // Essayer d'abord dans commercials
      final commercialDoc = await FirebaseService.commercials.doc(uid).get();
      if (commercialDoc.exists) {
        print('Utilisateur trouvé dans la collection commercials');
        await FirebaseService.commercials.doc(uid).update({
          'lastLogin': FieldValue.serverTimestamp(),
        });
        return;
      }

      // Sinon essayer dans merchandizers
      final merchandiserDoc =
          await FirebaseService.merchandizers.doc(uid).get();
      if (merchandiserDoc.exists) {
        print('Utilisateur trouvé dans la collection merchandizers');
        await FirebaseService.merchandizers.doc(uid).update({
          'lastLogin': FieldValue.serverTimestamp(),
        });
        return;
      }

      print('Utilisateur non trouvé dans les collections spécifiques');
    } catch (e) {
      print('Erreur lors de la mise à jour de lastLogin: $e');
    }
  }

  /// Obtenir le message d'erreur en français
  String _getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'Aucun utilisateur trouvé avec cet email';
      case 'wrong-password':
        return 'Mot de passe incorrect';
      case 'email-already-in-use':
        return 'Cet email est déjà utilisé';
      case 'weak-password':
        return 'Le mot de passe est trop faible';
      case 'invalid-email':
        return 'Email invalide';
      case 'user-disabled':
        return 'Ce compte utilisateur a été désactivé';
      case 'too-many-requests':
        return 'Trop de tentatives, réessayez plus tard';
      default:
        return 'Erreur d\'authentification';
    }
  }

  /// Vérifier si le statut de l'utilisateur est actif
  Future<bool> _checkUserStatus(String uid) async {
    try {
      print('🔍🔍🔍 DÉBUT VÉRIFICATION STATUT pour l\'utilisateur: $uid');

      // Vérifier dans les deux collections possibles

      // Essayer d'abord dans commercials
      final commercialDoc = await FirebaseService.commercials.doc(uid).get();
      if (commercialDoc.exists) {
        print('📁 Utilisateur trouvé dans la collection commercials');
        final userData = commercialDoc.data() as Map<String, dynamic>?;
        print('📄 Données utilisateur complètes: $userData');

        if (userData != null) {
          // Vérifier explicitement le champ status
          if (!userData.containsKey('status')) {
            print(
              '❌ Champ status NON TROUVÉ, utilisateur considéré comme inactif',
            );
            return false;
          }

          // Récupérer la valeur du statut et la convertir en chaîne
          var statusValue = userData['status'];
          String? status;

          if (statusValue is String) {
            status = statusValue;
          } else if (statusValue != null) {
            status = statusValue.toString();
          }

          print(
            '📊 Statut trouvé (type: ${statusValue.runtimeType}): "$status"',
          );

          // L'utilisateur est actif uniquement si son statut est explicitement 'actif'
          if (status == null) {
            print('❌ Statut NULL, utilisateur considéré comme inactif');
            return false;
          }

          // Comparaison stricte et insensible à la casse (support français et anglais)
          String statusLower = status.trim().toLowerCase();
          bool isActive = statusLower == 'actif' || statusLower == 'active';

          print(
            isActive
                ? '✅ Utilisateur ACTIF confirmé'
                : '❌ Statut NON ACTIF: "$status", connexion refusée',
          );

          return isActive;
        } else {
          print('❌ Données utilisateur NULL');
          return false;
        }
      } else {
        print('📁 Utilisateur NON trouvé dans la collection commercials');
      }

      // Sinon essayer dans merchandizers
      final merchandiserDoc =
          await FirebaseService.merchandizers.doc(uid).get();
      if (merchandiserDoc.exists) {
        print('📁 Utilisateur trouvé dans la collection merchandizers');
        final userData = merchandiserDoc.data() as Map<String, dynamic>?;
        print('📄 Données utilisateur complètes: $userData');

        if (userData != null) {
          // Vérifier explicitement le champ status
          if (!userData.containsKey('status')) {
            print(
              '❌ Champ status NON TROUVÉ, utilisateur considéré comme inactif',
            );
            return false;
          }

          // Récupérer la valeur du statut et la convertir en chaîne
          var statusValue = userData['status'];
          String? status;

          if (statusValue is String) {
            status = statusValue;
          } else if (statusValue != null) {
            status = statusValue.toString();
          }

          print(
            '📊 Statut trouvé (type: ${statusValue.runtimeType}): "$status"',
          );

          // L'utilisateur est actif uniquement si son statut est explicitement 'actif'
          if (status == null) {
            print('❌ Statut NULL, utilisateur considéré comme inactif');
            return false;
          }

          // Comparaison stricte et insensible à la casse (support français et anglais)
          String statusLower = status.trim().toLowerCase();
          bool isActive = statusLower == 'actif' || statusLower == 'active';

          print(
            isActive
                ? '✅ Utilisateur ACTIF confirmé'
                : '❌ Statut NON ACTIF: "$status", connexion refusée',
          );

          return isActive;
        } else {
          print('❌ Données utilisateur NULL');
          return false;
        }
      }

      // Si l'utilisateur n'est trouvé dans aucune collection
      print('❌ Utilisateur non trouvé dans aucune collection');
      return false;
    } catch (e) {
      print('❌ Erreur lors de la vérification du statut: $e');
      return false;
    }
  }

  /// Effacer le message d'erreur
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
