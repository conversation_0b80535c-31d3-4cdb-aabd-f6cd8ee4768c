import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/firebase_client_provider.dart';
import '../../models/client.dart';
import '../../widgets/professional_ui_components.dart';

class ClientFormScreen extends StatefulWidget {
  final Client? client;

  const ClientFormScreen({super.key, this.client});

  @override
  State<ClientFormScreen> createState() => _ClientFormScreenState();
}

class _ClientFormScreenState extends State<ClientFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nomClientController = TextEditingController();
  final _emailController = TextEditingController();
  final _telephoneController = TextEditingController();
  final _adresseController = TextEditingController();
  final _codeClientController = TextEditingController();
  final _matriculeFiscalController = TextEditingController();
  final _categorieController = TextEditingController();
  final _modeReglementController = TextEditingController();

  bool get _isEditing => widget.client != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _nomClientController.text = widget.client!.nomClient ??
          '${widget.client!.prenom ?? ''} ${widget.client!.nom ?? ''}'.trim();
      _emailController.text = widget.client!.email;
      _telephoneController.text = widget.client!.telephone ?? '';
      _adresseController.text = widget.client!.adresse;
      _codeClientController.text = widget.client!.codeClient ?? '';
      _matriculeFiscalController.text = widget.client!.matriculeFiscale ?? '';
      _categorieController.text = widget.client!.categorie ?? '';
      _modeReglementController.text = widget.client!.modeReglement ?? '';
    }
  }

  @override
  void dispose() {
    _nomClientController.dispose();
    _emailController.dispose();
    _telephoneController.dispose();
    _adresseController.dispose();
    _codeClientController.dispose();
    _matriculeFiscalController.dispose();
    _categorieController.dispose();
    _modeReglementController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2937)),
              onPressed: () => Navigator.pop(context),
            ),
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(
                left: 56, // Space for back button
                bottom: 16,
                right: 20, // Right padding
              ),
              title: Row(
                children: [
                  Container(
                    height: isSmallScreen ? 24 : 28,
                    width: isSmallScreen ? 24 : 28,
                    decoration: BoxDecoration(
                      color: const Color(0xFF3B82F6),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      _isEditing ? Icons.edit : Icons.person_add,
                      color: Colors.white,
                      size: isSmallScreen ? 16 : 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _isEditing ? 'Modifier' : 'Nouveau client',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 20,
                          ),
                        ),
                        Text(
                          _isEditing
                              ? 'Mettre à jour les informations'
                              : 'Ajouter un nouveau client',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF3B82F6).withValues(alpha: 0.05),
                      const Color(0xFF1D4ED8).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: isSmallScreen ? 35 : 50,
                      right: isSmallScreen ? 10 : 15,
                      child: Container(
                        width: isSmallScreen ? 60 : 80,
                        height: isSmallScreen ? 60 : 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF3B82F6).withValues(alpha: 0.1),
                              const Color(0xFF1D4ED8).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            _isEditing
                                ? Icons.edit_outlined
                                : Icons.person_add_outlined,
                            size: isSmallScreen ? 24 : 32,
                            color: const Color(0xFF3B82F6),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Form(
              key: _formKey,
              child: Padding(
                padding: EdgeInsets.all(padding),
                child: Column(
                  children: [
                    // Personal Information Card
                    ProfessionalCard(
                      child: Padding(
                        padding: EdgeInsets.all(padding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Informations personnelles',
                              style: Theme.of(
                                context,
                              ).textTheme.titleLarge?.copyWith(
                                    fontSize: isSmallScreen ? 18 : 20,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2937),
                                  ),
                            ),
                            SizedBox(height: isSmallScreen ? 16 : 20),
                            // Nom du client
                            TextFormField(
                              controller: _nomClientController,
                              decoration: InputDecoration(
                                labelText: 'Nom du Client *',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: isSmallScreen ? 12 : 16,
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Le nom du client est requis';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _emailController,
                              decoration: InputDecoration(
                                labelText: 'Email *',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(
                                  Icons.email,
                                  size: isSmallScreen ? 20 : 24,
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: isSmallScreen ? 12 : 16,
                                ),
                              ),
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'L\'email est requis';
                                }
                                if (!RegExp(
                                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                ).hasMatch(value)) {
                                  return 'Format d\'email invalide';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _telephoneController,
                              decoration: InputDecoration(
                                labelText: 'Téléphone *',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(
                                  Icons.phone,
                                  size: isSmallScreen ? 20 : 24,
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: isSmallScreen ? 12 : 16,
                                ),
                              ),
                              keyboardType: TextInputType.phone,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Le téléphone est requis';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _adresseController,
                              decoration: InputDecoration(
                                labelText: 'Adresse *',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(
                                  Icons.location_on,
                                  size: isSmallScreen ? 20 : 24,
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: isSmallScreen ? 12 : 16,
                                ),
                              ),
                              maxLines: 2,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'L\'adresse est requise';
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    // Business Information Card
                    ProfessionalCard(
                      child: Padding(
                        padding: EdgeInsets.all(padding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Informations commerciales',
                              style: Theme.of(
                                context,
                              ).textTheme.titleLarge?.copyWith(
                                    fontSize: isSmallScreen ? 18 : 20,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2937),
                                  ),
                            ),
                            SizedBox(height: isSmallScreen ? 16 : 20),
                            TextFormField(
                              controller: _codeClientController,
                              decoration: InputDecoration(
                                labelText: 'Code Client',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(
                                  Icons.qr_code,
                                  size: isSmallScreen ? 20 : 24,
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: isSmallScreen ? 12 : 16,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _matriculeFiscalController,
                              decoration: InputDecoration(
                                labelText: 'Matricule Fiscal',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(
                                  Icons.business,
                                  size: isSmallScreen ? 20 : 24,
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: isSmallScreen ? 12 : 16,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _categorieController,
                              decoration: InputDecoration(
                                labelText: 'Catégorie',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(
                                  Icons.category,
                                  size: isSmallScreen ? 20 : 24,
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: isSmallScreen ? 12 : 16,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _modeReglementController,
                              decoration: InputDecoration(
                                labelText: 'Mode de Règlement',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(
                                  Icons.payment,
                                  size: isSmallScreen ? 20 : 24,
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: isSmallScreen ? 12 : 16,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),
                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('Annuler'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Consumer<FirebaseClientProvider>(
                            builder: (context, provider, child) {
                              return FilledButton(
                                onPressed:
                                    provider.isLoading ? null : _sauvegarder,
                                style: FilledButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                  ),
                                  backgroundColor: const Color(0xFF3B82F6),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: provider.isLoading
                                    ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: Colors.white,
                                        ),
                                      )
                                    : Text(
                                        _isEditing ? 'Modifier' : 'Créer',
                                      ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 100), // Space for FAB
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _sauvegarder,
        backgroundColor: const Color(0xFF3B82F6),
        foregroundColor: Colors.white,
        elevation: 6,
        heroTag: "save_client",
        icon: Icon(_isEditing ? Icons.save : Icons.add),
        label: Text(_isEditing ? 'Sauvegarder' : 'Créer'),
      ),
    );
  }

  Future<void> _sauvegarder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final provider = context.read<FirebaseClientProvider>();
    provider.effacerErreur();

    final client = Client(
      id: _isEditing ? widget.client!.id : null,
      nomClient: _nomClientController.text.trim(),
      email: _emailController.text.trim(),
      telephone: _telephoneController.text.trim(),
      adresse: _adresseController.text.trim(),
      dateCreation: _isEditing ? widget.client!.dateCreation : DateTime.now(),
      createdAt: _isEditing ? widget.client!.createdAt : DateTime.now(),
      codeClient: _codeClientController.text.trim().isEmpty
          ? null
          : _codeClientController.text.trim(),
      matriculeFiscale: _matriculeFiscalController.text.trim().isEmpty
          ? null
          : _matriculeFiscalController.text.trim(),
      categorie: _categorieController.text.trim().isEmpty
          ? null
          : _categorieController.text.trim(),
      modeReglement: _modeReglementController.text.trim().isEmpty
          ? null
          : _modeReglementController.text.trim(),
      status: _isEditing
          ? widget.client!.status
          : 'inactive', // New clients are inactive by default
    );

    bool success;
    if (_isEditing) {
      success = await provider.modifierClient(client);
    } else {
      success = await provider.ajouterClient(client);
    }

    if (success && mounted) {
      // Show success message in the form itself
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isEditing
                ? 'Client modifié avec succès'
                : 'Client créé avec succès',
          ),
          backgroundColor: const Color(0xFF10B981),
        ),
      );

      // Small delay to show the message, then navigate back
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        Navigator.pop(context, {
          'success': true,
          'message': _isEditing
              ? 'Client modifié avec succès'
              : 'Client créé avec succès',
          'updatedClient': client, // Pass the updated client back
        });
      }
    } else if (provider.error != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(provider.error!), backgroundColor: Colors.red),
      );
    }
  }
}
