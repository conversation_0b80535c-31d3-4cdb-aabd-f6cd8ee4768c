import 'package:flutter/material.dart';

/// Widget pour les titres d'AppBar avec gestion du débordement
class ResponsiveAppBarTitle extends StatelessWidget {
  final String title;
  final List<Widget>? actions;
  final TextStyle? titleStyle;
  final double? maxFontSize;
  final double? minFontSize;

  const ResponsiveAppBarTitle({
    super.key,
    required this.title,
    this.actions,
    this.titleStyle,
    this.maxFontSize = 20,
    this.minFontSize = 16,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    // Calcul de la taille de police basée sur la largeur d'écran
    final fontSize = isSmallScreen ? minFontSize! : maxFontSize!;

    return AppBar(
      title: FittedBox(
        fit: BoxFit.scaleDown,
        alignment: Alignment.centerLeft,
        child: Text(
          title,
          style:
              titleStyle?.copyWith(fontSize: fontSize) ??
              TextStyle(fontSize: fontSize, fontWeight: FontWeight.w600),
          overflow: TextOverflow.ellipsis,
        ),
      ),
      actions: actions,
      elevation: 0,
      scrolledUnderElevation: 2,
      surfaceTintColor: Colors.transparent,
    );
  }
}

/// Widget pour les lignes de titre avec action qui débordent
class ResponsiveTitleRow extends StatelessWidget {
  final String title;
  final Widget? action;
  final TextStyle? titleStyle;
  final EdgeInsets? padding;

  const ResponsiveTitleRow({
    super.key,
    required this.title,
    this.action,
    this.titleStyle,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              alignment: Alignment.centerLeft,
              child: Text(
                title,
                style: titleStyle ?? Theme.of(context).textTheme.headlineSmall,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          if (action != null) ...[const SizedBox(width: 8), action!],
        ],
      ),
    );
  }
}
