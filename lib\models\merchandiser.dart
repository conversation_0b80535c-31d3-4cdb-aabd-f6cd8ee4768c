class Merchandiser {
  final String? id;
  final String uid;
  final String name;
  final String nomComplet;
  final String email;
  final String telephone;
  final String? mobile;
  final String territoire;
  final String status;
  final String userType;
  final DateTime createdAt;
  final DateTime? lastLogin;

  Merchandiser({
    this.id,
    required this.uid,
    required this.name,
    required this.nomComplet,
    required this.email,
    required this.telephone,
    this.mobile,
    required this.territoire,
    this.status = 'active',
    this.userType = 'merchandiser',
    required this.createdAt,
    this.lastLogin,
  });

  // Convertir un Merchandiser en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'name': name,
      'nomComplet': nomComplet,
      'email': email,
      'telephone': telephone,
      'mobile': mobile,
      'territoire': territoire,
      'status': status,
      'userType': userType,
      'createdAt': createdAt.toIso8601String(),
      'lastLogin': lastLogin?.toIso8601String(),
    };
  }

  // <PERSON><PERSON>er un Merchandiser à partir d'une Map de la base de données
  factory Merchandiser.fromMap(Map<String, dynamic> map) {
    return Merchandiser(
      id: map['id'] as String?,
      uid: map['uid'] ?? '',
      name: map['name'] ?? '',
      nomComplet: map['nomComplet'] ?? '',
      email: map['email'] ?? '',
      telephone: map['telephone'] ?? '',
      mobile: map['mobile'],
      territoire: map['territoire'] ?? '',
      status: map['status'] ?? 'active',
      userType: map['userType'] ?? 'merchandiser',
      createdAt: _parseDateTime(map['createdAt']) ?? DateTime.now(),
      lastLogin: _parseDateTime(map['lastLogin']),
    );
  }

  // Helper method to parse DateTime from various formats
  static DateTime? _parseDateTime(dynamic dateValue) {
    if (dateValue == null) return null;

    try {
      if (dateValue is String) {
        return DateTime.parse(dateValue);
      } else if (dateValue is int) {
        // Timestamp in milliseconds
        return DateTime.fromMillisecondsSinceEpoch(dateValue);
      } else if (dateValue.toString().isNotEmpty) {
        return DateTime.parse(dateValue.toString());
      }
    } catch (e) {
      print('Error parsing date: $dateValue, error: $e');
    }

    return null;
  }

  // Créer une copie du merchandiser avec des modifications
  Merchandiser copyWith({
    String? id,
    String? uid,
    String? name,
    String? nomComplet,
    String? email,
    String? telephone,
    String? mobile,
    String? territoire,
    String? status,
    String? userType,
    DateTime? createdAt,
    DateTime? lastLogin,
  }) {
    return Merchandiser(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      name: name ?? this.name,
      nomComplet: nomComplet ?? this.nomComplet,
      email: email ?? this.email,
      telephone: telephone ?? this.telephone,
      mobile: mobile ?? this.mobile,
      territoire: territoire ?? this.territoire,
      status: status ?? this.status,
      userType: userType ?? this.userType,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  @override
  String toString() {
    return 'Merchandiser{id: $id, name: $name, nomComplet: $nomComplet, territoire: $territoire}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Merchandiser && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
