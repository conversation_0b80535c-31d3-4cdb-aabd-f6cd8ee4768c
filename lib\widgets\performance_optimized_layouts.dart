import 'package:flutter/material.dart';

/// Performance-optimized horizontal layout widgets with lazy loading and efficient rendering
class PerformanceOptimizedLayouts {
  /// Creates a performance-optimized horizontal list with lazy loading
  static Widget buildOptimizedHorizontalList<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index)
    itemBuilder,
    required double itemWidth,
    double itemHeight = 120,
    EdgeInsets? padding,
    double spacing = 12,
    bool enableCaching = true,
  }) {
    return SizedBox(
      height: itemHeight,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 16),
        itemCount: items.length,
        cacheExtent:
            enableCaching ? itemWidth * 3 : null, // Cache 3 items ahead
        separatorBuilder: (context, index) => SizedBox(width: spacing),
        itemBuilder: (context, index) {
          return SizedBox(
            width: itemWidth,
            child:
                enableCaching
                    ? _CachedItemWrapper(
                      key: ValueKey('${T.toString()}_$index'),
                      child: itemBuilder(context, items[index], index),
                    )
                    : itemBuilder(context, items[index], index),
          );
        },
      ),
    );
  }

  /// Creates a performance-optimized responsive grid with dynamic column calculation
  static Widget buildOptimizedResponsiveGrid<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index)
    itemBuilder,
    required double minItemWidth,
    double maxItemWidth = double.infinity,
    double itemHeight = 200,
    double spacing = 12,
    EdgeInsets? padding,
    bool enableLazyLoading = true,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth =
            constraints.maxWidth - (padding?.horizontal ?? 0);
        final crossAxisCount = _calculateOptimalColumns(
          availableWidth,
          minItemWidth,
          maxItemWidth,
          spacing,
        );

        final itemWidth =
            (availableWidth - (spacing * (crossAxisCount - 1))) /
            crossAxisCount;

        if (enableLazyLoading && items.length > 20) {
          return _buildLazyGrid(
            items: items,
            itemBuilder: itemBuilder,
            crossAxisCount: crossAxisCount,
            itemWidth: itemWidth,
            itemHeight: itemHeight,
            spacing: spacing,
            padding: padding,
          );
        }

        return _buildStandardGrid(
          items: items,
          itemBuilder: itemBuilder,
          crossAxisCount: crossAxisCount,
          itemHeight: itemHeight,
          spacing: spacing,
          padding: padding,
        );
      },
    );
  }

  /// Creates a performance-optimized horizontal card layout with memory management
  static Widget buildOptimizedHorizontalCard({
    required Widget leftContent,
    required Widget rightContent,
    required bool isSmallScreen,
    int leftFlex = 3,
    int rightFlex = 2,
    EdgeInsets? padding,
    VoidCallback? onTap,
    bool enableHoverEffects = true,
  }) {
    final cardPadding = padding ?? const EdgeInsets.all(16);

    if (isSmallScreen) {
      return _OptimizedCard(
        onTap: onTap,
        enableHoverEffects: enableHoverEffects,
        child: Padding(
          padding: cardPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [leftContent, const SizedBox(height: 12), rightContent],
          ),
        ),
      );
    }

    return _OptimizedCard(
      onTap: onTap,
      enableHoverEffects: enableHoverEffects,
      child: Padding(
        padding: cardPadding,
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(flex: leftFlex, child: leftContent),
              const SizedBox(width: 16),
              Expanded(flex: rightFlex, child: rightContent),
            ],
          ),
        ),
      ),
    );
  }

  /// Calculates optimal number of columns for responsive grid
  static int _calculateOptimalColumns(
    double availableWidth,
    double minItemWidth,
    double maxItemWidth,
    double spacing,
  ) {
    if (availableWidth <= minItemWidth) return 1;

    int maxPossibleColumns =
        ((availableWidth + spacing) / (minItemWidth + spacing)).floor();

    // Check if we can fit items within maxItemWidth constraint
    for (int columns = maxPossibleColumns; columns >= 1; columns--) {
      final itemWidth = (availableWidth - (spacing * (columns - 1))) / columns;
      if (itemWidth <= maxItemWidth) {
        return columns;
      }
    }

    return 1;
  }

  /// Builds a lazy-loaded grid for large datasets
  static Widget _buildLazyGrid<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index)
    itemBuilder,
    required int crossAxisCount,
    required double itemWidth,
    required double itemHeight,
    required double spacing,
    EdgeInsets? padding,
  }) {
    return GridView.builder(
      padding: padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: itemWidth / itemHeight,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
      ),
      itemCount: items.length,
      cacheExtent: itemHeight * 2, // Cache 2 rows ahead
      itemBuilder: (context, index) {
        return _CachedItemWrapper(
          key: ValueKey('grid_item_$index'),
          child: itemBuilder(context, items[index], index),
        );
      },
    );
  }

  /// Builds a standard grid for smaller datasets
  static Widget _buildStandardGrid<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index)
    itemBuilder,
    required int crossAxisCount,
    required double itemHeight,
    required double spacing,
    EdgeInsets? padding,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final itemWidth =
            (constraints.maxWidth -
                (padding?.horizontal ?? 0) -
                (spacing * (crossAxisCount - 1))) /
            crossAxisCount;

        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: Wrap(
            spacing: spacing,
            runSpacing: spacing,
            children:
                items.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;

                  return SizedBox(
                    width: itemWidth,
                    height: itemHeight,
                    child: itemBuilder(context, item, index),
                  );
                }).toList(),
          ),
        );
      },
    );
  }
}

/// Cached item wrapper for performance optimization
class _CachedItemWrapper extends StatefulWidget {
  final Widget child;

  const _CachedItemWrapper({super.key, required this.child});

  @override
  State<_CachedItemWrapper> createState() => _CachedItemWrapperState();
}

class _CachedItemWrapperState extends State<_CachedItemWrapper>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
}

/// Optimized card widget with performance enhancements
class _OptimizedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final bool enableHoverEffects;

  const _OptimizedCard({
    required this.child,
    this.onTap,
    this.enableHoverEffects = true,
  });

  @override
  State<_OptimizedCard> createState() => _OptimizedCardState();
}

class _OptimizedCardState extends State<_OptimizedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        child: Material(
          elevation: _isHovered && widget.enableHoverEffects ? 4.0 : 2.0,
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
          child: InkWell(
            onTap: widget.onTap,
            borderRadius: BorderRadius.circular(12),
            child: MouseRegion(
              onEnter:
                  widget.enableHoverEffects
                      ? (_) {
                        setState(() => _isHovered = true);
                        _controller.forward();
                      }
                      : null,
              onExit:
                  widget.enableHoverEffects
                      ? (_) {
                        setState(() => _isHovered = false);
                        _controller.reverse();
                      }
                      : null,
              child: AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return Transform.scale(
                    scale:
                        widget.enableHoverEffects
                            ? 1.0 + (_controller.value * 0.02)
                            : 1.0,
                    child: widget.child,
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Memory-efficient statistics row widget
class OptimizedStatsRow extends StatelessWidget {
  final List<StatItem> stats;
  final bool isSmallScreen;
  final double itemWidth;
  final double itemHeight;

  const OptimizedStatsRow({
    super.key,
    required this.stats,
    required this.isSmallScreen,
    this.itemWidth = 140,
    this.itemHeight = 120,
  });

  @override
  Widget build(BuildContext context) {
    if (isSmallScreen) {
      return PerformanceOptimizedLayouts.buildOptimizedHorizontalList<StatItem>(
        items: stats,
        itemBuilder: (context, stat, index) => _buildStatCard(stat),
        itemWidth: itemWidth,
        itemHeight: itemHeight,
        enableCaching: true,
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children:
            stats.map((stat) {
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: RepaintBoundary(child: _buildStatCard(stat)),
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildStatCard(StatItem stat) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: stat.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: stat.color.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(stat.icon, color: stat.color, size: 24),
          const SizedBox(height: 8),
          Text(
            stat.value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: stat.color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            stat.label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

/// Data model for statistics items
class StatItem {
  final String label;
  final String value;
  final IconData icon;
  final Color color;

  const StatItem({
    required this.label,
    required this.value,
    required this.icon,
    required this.color,
  });
}
