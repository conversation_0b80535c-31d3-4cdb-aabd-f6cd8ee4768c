import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../models/produit.dart';
import '../../providers/produit_provider.dart';
import '../../widgets/professional_ui_components.dart';

class ProduitFormScreen extends StatefulWidget {
  final Produit? produit;

  const ProduitFormScreen({super.key, this.produit});

  @override
  State<ProduitFormScreen> createState() => _ProduitFormScreenState();
}

class _ProduitFormScreenState extends State<ProduitFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  final _nomController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _prixController = TextEditingController();
  final _stockController = TextEditingController();
  final _imageUrlController = TextEditingController();
  final _categorieController = TextEditingController();

  bool _actif = true;
  bool _isLoading = false;
  bool _isDisposed = false;

  bool get _isEditing => widget.produit != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _populateFields();
    }
    _loadCategories();
  }

  void _populateFields() {
    final produit = widget.produit!;
    _codeController.text = produit.code;
    _nomController.text = produit.nom;
    _descriptionController.text = produit.description;
    _prixController.text = produit.prix.toStringAsFixed(3);
    _stockController.text = produit.stock.toString();
    _imageUrlController.text = produit.imageUrl ?? '';
    _categorieController.text = produit.categorie;
    _actif = produit.actif;
  }

  void _loadCategories() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isDisposed) {
        context.read<ProduitProvider>().chargerCategories();
      }
    });
  }

  @override
  void dispose() {
    _isDisposed = true;
    _codeController.dispose();
    _nomController.dispose();
    _descriptionController.dispose();
    _prixController.dispose();
    _stockController.dispose();
    _imageUrlController.dispose();
    _categorieController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;

        // Check if there are unsaved changes
        final hasChanges =
            _codeController.text.isNotEmpty ||
            _nomController.text.isNotEmpty ||
            _descriptionController.text.isNotEmpty ||
            _prixController.text.isNotEmpty ||
            _stockController.text.isNotEmpty ||
            _imageUrlController.text.isNotEmpty ||
            _categorieController.text.isNotEmpty;

        if (!hasChanges) {
          Navigator.of(context).pop();
          return;
        }

        final shouldPop = await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Quitter sans enregistrer ?'),
                content: const Text(
                  'Vous avez des modifications non enregistrées. Voulez-vous vraiment quitter ?',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('Annuler'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: TextButton.styleFrom(foregroundColor: Colors.red),
                    child: const Text('Quitter'),
                  ),
                ],
              ),
        );

        if (shouldPop == true && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: isSmallScreen ? 120 : 140,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              elevation: 0,
              scrolledUnderElevation: 2,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2937)),
                onPressed: () => Navigator.pop(context),
              ),
              flexibleSpace: FlexibleSpaceBar(
                titlePadding: EdgeInsets.only(
                  left: 56, // Space for back button
                  bottom: 16,
                  right: 20, // Right padding
                ),
                title: Row(
                  children: [
                    Container(
                      height: isSmallScreen ? 24 : 28,
                      width: isSmallScreen ? 24 : 28,
                      decoration: BoxDecoration(
                        color: const Color(0xFF10B981),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        _isEditing ? Icons.edit : Icons.add_box,
                        color: Colors.white,
                        size: isSmallScreen ? 16 : 18,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _isEditing ? 'Modifier produit' : 'Nouveau produit',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: const Color(0xFF1F2937),
                              fontSize: isSmallScreen ? 18 : 20,
                            ),
                          ),
                          Text(
                            _isEditing
                                ? 'Mettre à jour les informations'
                                : 'Ajouter un nouveau produit',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        const Color(0xFF10B981).withValues(alpha: 0.05),
                        const Color(0xFF059669).withValues(alpha: 0.05),
                      ],
                      stops: const [0.0, 0.7, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: isSmallScreen ? 35 : 50,
                        right: isSmallScreen ? 10 : 15,
                        child: Container(
                          width: isSmallScreen ? 60 : 80,
                          height: isSmallScreen ? 60 : 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF10B981).withValues(alpha: 0.1),
                                const Color(0xFF059669).withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                          child: Center(
                            child: Icon(
                              Icons.inventory_2_outlined,
                              size: isSmallScreen ? 24 : 32,
                              color: const Color(0xFF10B981),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(padding),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      // Informations de base
                      _buildBasicInfoCard(isSmallScreen, padding),
                      const SizedBox(height: 20),
                      // Informations commerciales
                      _buildCommercialInfoCard(isSmallScreen, padding),
                      const SizedBox(height: 20),
                      // Image et statut
                      _buildImageAndStatusCard(isSmallScreen, padding),
                      const SizedBox(height: 100), // Space for FAB
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: _isLoading ? null : _saveProduct,
          backgroundColor: const Color(0xFF10B981),
          foregroundColor: Colors.white,
          elevation: 6,
          heroTag: "save_product",
          icon:
              _isLoading
                  ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                  : const Icon(Icons.save),
          label: Text(_isLoading ? 'Enregistrement...' : 'Enregistrer'),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard(bool isSmallScreen, double padding) {
    return ProfessionalCard(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations de base',
              style: TextStyle(
                fontSize: isSmallScreen ? 18 : 20,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'Code produit *',
                hintText: 'Ex: PROD001',
                prefixIcon: Icon(Icons.qr_code),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le code produit est obligatoire';
                }
                if (value.trim().length < 3) {
                  return 'Le code doit contenir au moins 3 caractères';
                }
                return null;
              },
              textCapitalization: TextCapitalization.characters,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nomController,
              decoration: const InputDecoration(
                labelText: 'Nom du produit *',
                hintText: 'Ex: Brosse à dents électrique',
                prefixIcon: Icon(Icons.inventory_2),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le nom du produit est obligatoire';
                }
                if (value.trim().length < 2) {
                  return 'Le nom doit contenir au moins 2 caractères';
                }
                return null;
              },
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Description détaillée du produit',
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
              textCapitalization: TextCapitalization.sentences,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommercialInfoCard(bool isSmallScreen, double padding) {
    return ProfessionalCard(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations commerciales',
              style: TextStyle(
                fontSize: isSmallScreen ? 18 : 20,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _prixController,
                    decoration: const InputDecoration(
                      labelText: 'Prix unitaire *',
                      hintText: '0.000',
                      prefixIcon: Icon(Icons.attach_money),
                      suffixText: 'DT',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp(r'^\d*\.?\d{0,3}'),
                      ),
                    ],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Le prix est obligatoire';
                      }
                      final price = double.tryParse(value.trim());
                      if (price == null || price <= 0) {
                        return 'Prix invalide';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _stockController,
                    decoration: const InputDecoration(
                      labelText: 'Stock *',
                      hintText: '0',
                      prefixIcon: Icon(Icons.inventory),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Le stock est obligatoire';
                      }
                      final stock = int.tryParse(value.trim());
                      if (stock == null || stock < 0) {
                        return 'Stock invalide';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer<ProduitProvider>(
              builder: (context, provider, child) {
                // Get the current category value, but only if it exists in the list
                String? currentValue;
                final currentCategory = _categorieController.text.trim();
                if (currentCategory.isNotEmpty &&
                    provider.categories.contains(currentCategory)) {
                  currentValue = currentCategory;
                }

                return DropdownButtonFormField<String>(
                  value: currentValue,
                  decoration: const InputDecoration(
                    labelText: 'Catégorie *',
                    hintText: 'Sélectionner une catégorie',
                    prefixIcon: Icon(Icons.category),
                  ),
                  items: [
                    ...provider.categories.map(
                      (category) => DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      ),
                    ),
                    const DropdownMenuItem(
                      value: '__nouvelle__',
                      child: Text('+ Nouvelle catégorie'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value == '__nouvelle__') {
                      _showNewCategoryDialog();
                    } else {
                      setState(() {
                        _categorieController.text = value ?? '';
                      });
                    }
                  },
                  validator: (value) {
                    final category = _categorieController.text.trim();
                    if (category.isEmpty) {
                      return 'La catégorie est obligatoire';
                    }
                    return null;
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageAndStatusCard(bool isSmallScreen, double padding) {
    return ProfessionalCard(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Image et statut',
              style: TextStyle(
                fontSize: isSmallScreen ? 18 : 20,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: _imageUrlController,
              decoration: const InputDecoration(
                labelText: 'URL de l\'image',
                hintText: 'https://exemple.com/image.jpg',
                prefixIcon: Icon(Icons.image),
              ),
              keyboardType: TextInputType.url,
              onChanged: (value) {
                setState(() {}); // Trigger rebuild for image preview
              },
            ),
            if (_imageUrlController.text.trim().isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                height: 120,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    _imageUrlController.text.trim(),
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey.shade100,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.broken_image,
                              color: Colors.grey.shade400,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Image non trouvée',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        color: Colors.grey.shade100,
                        child: Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: const Color(0xFF10B981),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                Switch(
                  value: _actif,
                  onChanged: (value) {
                    setState(() {
                      _actif = value;
                    });
                  },
                  activeColor: const Color(0xFF10B981),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Produit actif',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2937),
                        ),
                      ),
                      Text(
                        _actif
                            ? 'Le produit sera visible dans le catalogue'
                            : 'Le produit sera masqué du catalogue',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showNewCategoryDialog() {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Nouvelle catégorie'),
            content: TextFormField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Nom de la catégorie',
                hintText: 'Ex: Soins dentaires',
              ),
              autofocus: true,
              textCapitalization: TextCapitalization.words,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              FilledButton(
                onPressed: () {
                  final newCategory = controller.text.trim();
                  if (newCategory.isNotEmpty) {
                    // Add the new category to the provider's list
                    final provider = context.read<ProduitProvider>();
                    if (!provider.categories.contains(newCategory)) {
                      provider.categories.add(newCategory);
                    }

                    // Update the controller and trigger rebuild
                    setState(() {
                      _categorieController.text = newCategory;
                    });

                    Navigator.pop(context);
                  }
                },
                child: const Text('Ajouter'),
              ),
            ],
          ),
    );
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final produit = Produit(
        id: widget.produit?.id,
        code: _codeController.text.trim(),
        nom: _nomController.text.trim(),
        description: _descriptionController.text.trim(),
        prix: double.parse(_prixController.text.trim()),
        stock: int.parse(_stockController.text.trim()),
        imageUrl:
            _imageUrlController.text.trim().isEmpty
                ? null
                : _imageUrlController.text.trim(),
        categorie: _categorieController.text.trim(),
        dateCreation: widget.produit?.dateCreation ?? DateTime.now(),
        actif: _actif,
      );

      final provider = context.read<ProduitProvider>();
      bool success;
      String message;

      if (_isEditing) {
        success = await provider.modifierProduit(produit);
        message =
            success
                ? 'Produit modifié avec succès'
                : 'Erreur lors de la modification';
      } else {
        success = await provider.ajouterProduit(produit);
        message =
            success ? 'Produit ajouté avec succès' : 'Erreur lors de l\'ajout';
      }

      if (mounted && !_isDisposed) {
        if (success) {
          Navigator.pop(context, {
            'success': true,
            'message': message,
            'updatedProduct': produit,
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(provider.error ?? message),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted && !_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted && !_isDisposed) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
