import 'package:flutter/foundation.dart';
import 'firebase_auth_provider.dart';

class AuthProvider extends ChangeNotifier {
  final FirebaseAuthProvider _firebaseAuthProvider = FirebaseAuthProvider();

  String? _userType;

  bool get isAuthenticated => _firebaseAuthProvider.isAuthenticated;
  bool get isLoading => _firebaseAuthProvider.isLoading;
  String? get currentUser => _firebaseAuthProvider.user?.email;
  String? get currentUserId => _firebaseAuthProvider.user?.uid;
  String? get userType => _userType;
  String? get error => _firebaseAuthProvider.errorMessage;

  AuthProvider() {
    // Écouter les changements du FirebaseAuthProvider
    _firebaseAuthProvider.addListener(_onFirebaseAuthChange);
  }

  void _onFirebaseAuthChange() {
    notifyListeners();
  }

  @override
  void dispose() {
    _firebaseAuthProvider.removeListener(_onFirebaseAuthChange);
    super.dispose();
  }

  // Méthode de connexion utilisant Firebase
  Future<bool> login(String email, String password) async {
    final success = await _firebaseAuthProvider.signInWithEmailAndPassword(
      email,
      password,
    );

    if (success) {
      // Récupérer le type d'utilisateur après connexion réussie
      final profile = await _firebaseAuthProvider.getUserProfile();
      _userType = profile?['userType'] as String?;
      notifyListeners();
    }

    return success;
  }

  // Méthode d'inscription utilisant Firebase
  Future<bool> signup({
    required String nomComplet,
    required String email,
    required String password,
    required String telephone,
    String? mobile,
    String? territoire,
    required String userType,
    String status = 'actif', // Changé pour les tests
  }) async {
    return await _firebaseAuthProvider.signUpWithUserData(
      email: email,
      password: password,
      nomComplet: nomComplet,
      telephone: telephone,
      userType: userType,
      mobile: mobile,
      territoire: territoire,
      status: status,
    );
  }

  // Déconnexion
  Future<void> logout() async {
    print('🔄 AuthProvider.logout() called');

    // Prevent multiple concurrent logout attempts
    if (_firebaseAuthProvider.isLoading) {
      print('⚠️ Logout already in progress, waiting...');
      // Wait for any ongoing logout to complete
      int attempts = 0;
      while (_firebaseAuthProvider.isLoading && attempts < 50) {
        // Max 5 seconds wait
        await Future.delayed(const Duration(milliseconds: 100));
        attempts++;
      }
    }

    try {
      // Clear user type first
      _userType = null;
      print('🔄 User type cleared');

      // Notify listeners immediately to start updating UI
      notifyListeners();

      // Sign out from Firebase with enhanced reliability
      print('🔄 Calling Firebase signOut...');
      await _firebaseAuthProvider.signOut();
      print('✅ Firebase signOut completed');

      // Clear any cached data from providers
      print('🔄 Clearing cached data...');
      // Note: Individual providers will clear their caches when they detect auth state change

      // Notify listeners of the change
      print('🔄 Notifying listeners...');
      notifyListeners();

      // Add a delay to ensure complete logout
      await Future.delayed(const Duration(milliseconds: 800));

      // Triple-check that we're actually logged out
      if (_firebaseAuthProvider.isAuthenticated) {
        print('⚠️ Still authenticated after logout, forcing sign out...');
        await _firebaseAuthProvider.forceSignOut();
        _userType = null;
        notifyListeners();

        // Final check with shorter delay
        await Future.delayed(const Duration(milliseconds: 300));

        if (_firebaseAuthProvider.isAuthenticated) {
          print('❌ CRITICAL: Still authenticated after force sign out!');
          // Force clear everything locally
          _userType = null;
          notifyListeners();
        }
      }

      print('✅ AuthProvider.logout() completed successfully');
    } catch (e) {
      print('❌ Error in AuthProvider.logout(): $e');
      // Even if Firebase logout fails, clear local state
      _userType = null;
      notifyListeners();

      // Try one more force logout attempt
      try {
        await _firebaseAuthProvider.forceSignOut();
        _userType = null;
        notifyListeners();
      } catch (forceError) {
        print('❌ Force logout also failed: $forceError');
      }

      // Re-throw the original error so the UI can handle it
      rethrow;
    }
  }

  // Continuer en tant qu'invité (si nécessaire)
  void loginAsGuest() {
    // Cette méthode peut être supprimée si vous n'utilisez pas le mode invité
  }

  // Vérifier le statut d'authentification
  Future<void> checkAuthStatus() async {
    // Le FirebaseAuthProvider gère automatiquement l'état d'authentification
    notifyListeners();
  }

  void clearError() {
    _firebaseAuthProvider.clearError();
  }
}
